#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
隐藏式登录管理器
全网最快最轻量的网页自动化登录方案
- 无头浏览器后台运行
- 最小化窗口模式
- 智能验证检测
- 只在需要验证时弹出窗口
"""

import time
import threading
from typing import Tuple, Optional
from PyQt5.QtCore import QObject, pyqtSignal, QThread, QTimer
from PyQt5.QtWidgets import QMessageBox, QApplication
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from ..models.account import Account
from ..utils.logger import get_logger
from .browser_manager import BrowserManager

logger = get_logger("StealthLogin")


class StealthLoginManager(QObject):
    """隐藏式登录管理器 - 全网最轻量方案"""
    
    # 信号定义
    login_started = pyqtSignal(str)  # 登录开始
    login_completed = pyqtSignal(str, bool, str)  # 登录完成
    verification_required = pyqtSignal(str, str)  # 需要验证
    
    def __init__(self, browser_manager: BrowserManager, parent=None):
        super().__init__(parent)
        self.browser_manager = browser_manager
        self.driver = None
        self.driver_id = None
        self.stealth_mode = True  # 隐藏模式
        self.verification_window_shown = False

        # 初始化Cookie管理器
        try:
            from .cookie_manager import CookieManager
            default_config = {
                'performance.max_concurrent_sessions': 100,
                'session.timeout': 3600
            }
            self.cookie_manager = CookieManager(default_config)
            logger.info("✅ Cookie管理器初始化成功")
        except Exception as e:
            logger.warning(f"⚠️ Cookie管理器初始化失败: {e}")
            self.cookie_manager = None
    
    def stealth_login(self, account: Account, stealth_mode: bool = True) -> Tuple[bool, str]:
        """
        隐藏式登录 - 全网最轻量方案
        
        Args:
            account: 账号信息
            stealth_mode: True=完全隐藏, False=最小化窗口
        
        Returns:
            (是否成功, 消息)
        """
        try:
            logger.info(f"🥷 开始隐藏式登录: {account.email} (隐藏模式: {stealth_mode})")
            self.stealth_mode = stealth_mode
            self.login_started.emit(account.email)
            
            # 步骤1: 创建隐藏浏览器
            success = self._create_stealth_browser(account)
            if not success:
                return False, "无法创建隐藏浏览器"
            
            # 步骤2: 后台访问登录页面
            success = self._stealth_navigate()
            if not success:
                return False, "无法访问登录页面"
            
            # 步骤3: 闪电输入账号密码
            success = self._lightning_input(account)
            if not success:
                return False, "无法输入账号密码"
            
            # 步骤4: 瞬间点击登录
            success = self._instant_click_login()
            if not success:
                return False, "无法点击登录按钮"
            
            # 步骤5: 智能验证检测
            success, message = self._smart_verification_check(account)
            
            self.login_completed.emit(account.email, success, message)
            return success, message
            
        except Exception as e:
            error_msg = f"隐藏式登录异常: {e}"
            logger.error(f"❌ {error_msg}")
            self.login_completed.emit(account.email, False, error_msg)
            return False, error_msg
        finally:
            self._stealth_cleanup()
    
    def _create_stealth_browser(self, account: Account) -> bool:
        """创建隐藏浏览器"""
        try:
            logger.info("👻 创建隐藏浏览器...")
            
            # 临时修改浏览器配置为隐藏模式
            original_config = self.browser_manager.browser_config.copy()
            
            if self.stealth_mode:
                # 完全隐藏模式 - 无头浏览器
                self.browser_manager.browser_config['headless'] = True
                logger.info("🔇 启用无头模式 - 完全后台运行")
            else:
                # 最小化模式 - 稳定的小窗口
                self.browser_manager.browser_config['headless'] = False
                self.browser_manager.browser_config['window_size'] = [600, 450]  # 稳定的小窗口
                logger.info("📱 启用最小化模式 - 稳定小窗口")
            
            # 创建浏览器驱动
            self.driver_id = self.browser_manager.create_driver(account)
            self.driver = self.browser_manager.get_driver(self.driver_id)
            
            # 恢复原始配置
            self.browser_manager.browser_config = original_config
            
            if not self.stealth_mode:
                # 设置稳定的小窗口
                try:
                    # 设置窗口位置（避免边缘）
                    self.driver.set_window_position(50, 50)
                    # 设置稳定的窗口大小
                    self.driver.set_window_size(600, 450)
                    # 不使用minimize_window，因为可能导致崩溃
                    logger.info("📐 窗口已设置为稳定小窗口")
                except Exception as e:
                    logger.warning(f"⚠️ 窗口设置失败: {e}")
                    # 降级处理：使用默认大小
                    try:
                        self.driver.set_window_size(800, 600)
                    except:
                        pass
            
            logger.info("✅ 隐藏浏览器创建成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建隐藏浏览器失败: {e}")
            return False
    
    def _stealth_navigate(self) -> bool:
        """后台访问登录页面"""
        try:
            logger.info("🌐 后台访问新浪邮箱...")
            
            # 直接访问登录页面
            self.driver.get("https://mail.sina.com.cn")
            
            # 最小等待时间
            time.sleep(1)
            
            logger.info("✅ 页面访问完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 页面访问失败: {e}")
            return False
    
    def _lightning_input(self, account: Account) -> bool:
        """闪电输入账号密码"""
        try:
            logger.info("⚡ 闪电输入账号密码...")
            
            # 超快速定位用户名输入框
            username_selectors = [
                "#freename",
                "input[name='username']",
                "input[name='loginname']",
                "input[type='email']"
            ]
            
            username_input = None
            for selector in username_selectors:
                try:
                    username_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if username_input.is_displayed():
                        break
                except:
                    continue
            
            if not username_input:
                return False
            
            # JavaScript闪电输入用户名
            self.driver.execute_script("""
                arguments[0].value = arguments[1];
                arguments[0].dispatchEvent(new Event('input', { bubbles: true }));
                arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
            """, username_input, account.email)
            
            # 超快速定位密码输入框
            password_selectors = [
                "#freepassword",
                "input[name='password']",
                "input[type='password']"
            ]
            
            password_input = None
            for selector in password_selectors:
                try:
                    password_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if password_input.is_displayed():
                        break
                except:
                    continue
            
            if not password_input:
                return False
            
            # JavaScript闪电输入密码
            self.driver.execute_script("""
                arguments[0].value = arguments[1];
                arguments[0].dispatchEvent(new Event('input', { bubbles: true }));
                arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
            """, password_input, account.password)
            
            logger.info("⚡ 闪电输入完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 闪电输入失败: {e}")
            return False
    
    def _instant_click_login(self) -> bool:
        """瞬间点击登录"""
        try:
            logger.info("🚀 瞬间点击登录...")
            
            # 超快速定位登录按钮
            login_selectors = [
                "input[type='submit'][value*='登']",
                "input[type='image']",
                "button[type='submit']",
                ".loginBtn",
                "#loginBtn"
            ]
            
            login_button = None
            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if login_button.is_displayed() and login_button.is_enabled():
                        break
                except:
                    continue
            
            if not login_button:
                return False
            
            # JavaScript瞬间点击
            self.driver.execute_script("arguments[0].click();", login_button)
            
            # 最小等待，进一步优化
            time.sleep(0.2)
            
            logger.info("🚀 瞬间点击完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 瞬间点击失败: {e}")
            return False

    def _smart_verification_check(self, account: Account) -> Tuple[bool, str]:
        """智能验证检测 - 只在需要时弹出窗口"""
        try:
            logger.info("🧠 智能验证检测...")

            # 极速检测页面状态
            time.sleep(0.2)  # 进一步减少等待时间

            page_source = self.driver.page_source.lower()
            current_url = self.driver.current_url

            # 检测验证码关键词 - 扩展版本
            verification_keywords = [
                # 中文验证码
                "验证码", "点击验证", "滑动验证", "人机验证", "安全验证",
                "图形验证", "拖拽验证", "点击图片", "选择图片",
                # 英文验证码
                "captcha", "verify", "verification", "robot", "challenge",
                "recaptcha", "hcaptcha", "cloudflare", "turnstile",
                # 验证码元素
                "geetest", "slider", "puzzle", "click", "drag",
                # 验证码提示
                "请完成", "请验证", "安全检查", "身份验证"
            ]

            # 多层验证码检测
            has_verification = self._advanced_verification_detection(page_source)

            if has_verification:
                logger.info("🔐 智能检测到验证码，准备弹出验证窗口...")
                return self._handle_smart_verification(account)

            # 增强登录成功检测
            success_result = self._enhanced_login_success_detection(page_source, current_url)

            if success_result[0]:
                logger.info("✅ 登录成功，无需验证")
                # 自动提取并保存Cookie
                self._extract_and_save_cookies(account)
                # 执行登录成功后的操作
                self._handle_login_success(account)
                return success_result

            # 检测登录失败
            error_keywords = [
                "用户名或密码错误", "登录失败", "账号不存在", "密码错误"
            ]

            has_error = any(keyword in page_source for keyword in error_keywords)

            if has_error:
                logger.warning("❌ 登录失败")
                return False, "用户名或密码错误"

            # 状态不明确，可能需要更多时间
            logger.info("⚠️ 登录状态不明确，继续检测...")
            return self._extended_verification_check(account)

        except Exception as e:
            logger.error(f"❌ 智能验证检测失败: {e}")
            return False, f"验证检测失败: {e}"

    def _handle_smart_verification(self, account: Account) -> Tuple[bool, str]:
        """处理智能验证 - 精确适配验证框窗口"""
        try:
            logger.info("🎯 处理智能验证...")

            # 智能调整窗口到验证框最佳大小
            optimal_size = self._calculate_verification_window_size()

            # 智能验证模式切换
            verification_success = False

            if self.stealth_mode:
                logger.info("👁️ 从隐藏模式切换到验证框适配模式...")

                # 方案1：尝试重新创建可见浏览器
                try:
                    success = self._switch_to_verification_mode(account, optimal_size)
                    if success:
                        verification_success = True
                        logger.info("✅ 成功切换到可见验证模式")
                    else:
                        logger.warning("⚠️ 方案1失败，尝试方案2")
                except Exception as e:
                    logger.warning(f"⚠️ 方案1异常: {e}")

                # 方案2：如果方案1失败，尝试直接调整当前窗口
                if not verification_success:
                    try:
                        logger.info("🔄 尝试直接调整无头浏览器窗口...")
                        # 尝试将无头浏览器设置为可见
                        self.driver.execute_script("document.body.style.display = 'block';")
                        self._adjust_window_for_verification(optimal_size)
                        verification_success = True
                        logger.info("✅ 方案2成功：直接调整窗口")
                    except Exception as e2:
                        logger.warning(f"⚠️ 方案2也失败: {e2}")

                # 方案3：最后的降级方案
                if not verification_success:
                    try:
                        logger.info("🆘 使用最后的降级方案...")
                        # 创建一个简单的可见浏览器
                        self._create_emergency_verification_browser(account, optimal_size)
                        verification_success = True
                        logger.info("✅ 方案3成功：紧急验证浏览器")
                    except Exception as e3:
                        logger.error(f"❌ 所有方案都失败: {e3}")

            else:
                # 最小化模式，直接调整窗口
                try:
                    logger.info("📱 最小化模式：调整到验证框大小...")
                    self._adjust_window_for_verification(optimal_size)
                    verification_success = True
                    logger.info(f"✅ 最小化模式窗口调整成功: {optimal_size}")
                except Exception as e:
                    logger.warning(f"⚠️ 最小化模式窗口调整失败: {e}")
                    # 降级：使用默认大小
                    try:
                        self.driver.set_window_size(800, 600)
                        self.driver.set_window_position(100, 100)
                        verification_success = True
                        logger.info("✅ 使用默认窗口大小")
                    except Exception as e2:
                        logger.error(f"❌ 默认窗口设置也失败: {e2}")

            if not verification_success:
                logger.error("❌ 所有验证窗口调整方案都失败")
                return False, "无法调整验证窗口"

            # 自动检测验证码完成状态，无需人工确认
            logger.info("🤖 启动自动验证码检测...")

            # 标记验证窗口已显示
            self.verification_window_shown = True

            # 自动检测验证码完成和登录成功
            return self._auto_detect_verification_completion(account)

        except Exception as e:
            logger.error(f"❌ 智能验证处理失败: {e}")
            return False, f"验证处理失败: {e}"

    def _auto_detect_verification_completion(self, account: Account) -> Tuple[bool, str]:
        """自动检测验证码完成状态"""
        try:
            logger.info("🤖 开始自动检测验证码完成状态...")

            max_checks = 20  # 减少检测次数，每次1秒，总共20秒
            check_interval = 1  # 每1秒检查一次，提高响应速度

            for i in range(max_checks):
                logger.info(f"🔍 第 {i+1}/{max_checks} 次自动检测...")

                try:
                    current_url = self.driver.current_url
                    page_source = self.driver.page_source.lower()

                    # 1. 优先检测登录成功
                    success_result = self._enhanced_login_success_detection(page_source, current_url)
                    if success_result[0]:
                        logger.info("✅ 自动检测到登录成功")
                        return self._handle_login_success_fast(account)

                    # 2. 检测是否还有验证码
                    has_verification = self._advanced_verification_detection(page_source)
                    if not has_verification:
                        logger.info("🎯 验证码已消失，可能已完成验证")
                        # 再次检测登录状态
                        time.sleep(1)
                        current_url = self.driver.current_url
                        page_source = self.driver.page_source.lower()
                        success_result = self._enhanced_login_success_detection(page_source, current_url)
                        if success_result[0]:
                            logger.info("✅ 验证码消失后检测到登录成功")
                            return self._handle_login_success_fast(account)

                    # 3. 检测登录失败
                    error_keywords = [
                        "用户名或密码错误", "登录失败", "账号不存在", "密码错误",
                        "验证失败", "验证码错误"
                    ]

                    if any(keyword in page_source for keyword in error_keywords):
                        logger.warning("❌ 检测到登录失败")
                        return False, "登录失败"

                    # 等待下次检测
                    time.sleep(check_interval)

                except Exception as check_error:
                    logger.debug(f"第{i+1}次检测异常: {check_error}")
                    time.sleep(check_interval)
                    continue

            # 超时后的最后一次尝试
            logger.warning("⏰ 自动检测超时，进行最后一次检测...")
            try:
                current_url = self.driver.current_url
                page_source = self.driver.page_source.lower()
                success_result = self._enhanced_login_success_detection(page_source, current_url)
                if success_result[0]:
                    logger.info("✅ 超时后最后检测到登录成功")
                    return self._handle_login_success_fast(account)
            except:
                pass

            logger.warning("⚠️ 自动检测超时，验证状态不明确")
            return False, "验证检测超时"

        except Exception as e:
            logger.error(f"❌ 自动验证检测失败: {e}")
            return False, f"自动检测失败: {e}"

    def _extended_verification_check(self, account: Account) -> Tuple[bool, str]:
        """扩展验证检测 - 多次检测确保准确性"""
        try:
            logger.info("🔍 扩展验证检测...")

            max_checks = 3
            check_interval = 2

            for i in range(max_checks):
                logger.info(f"🔍 第 {i+1}/{max_checks} 次状态检测...")

                time.sleep(check_interval)

                page_source = self.driver.page_source.lower()
                current_url = self.driver.current_url

                # 再次检测验证码
                verification_keywords = [
                    "验证码", "captcha", "verify", "点击验证", "滑动验证"
                ]

                if any(keyword in page_source for keyword in verification_keywords):
                    logger.info("🔐 确认检测到验证码")
                    return self._handle_smart_verification(account)

                # 再次检测登录成功
                success_keywords = [
                    "收件箱", "inbox", "mailbox", "compose", "写邮件"
                ]

                if any(keyword in page_source for keyword in success_keywords):
                    logger.info("✅ 确认登录成功")
                    return True, "隐藏式登录成功"

            # 多次检测后仍不明确
            logger.warning("⚠️ 多次检测后状态仍不明确")
            return False, "登录状态不明确"

        except Exception as e:
            logger.error(f"❌ 扩展验证检测失败: {e}")
            return False, f"扩展检测失败: {e}"

    def _verify_login_success(self, account: Account) -> Tuple[bool, str]:
        """极速验证登录成功"""
        try:
            logger.info("⚡ 极速验证登录成功...")

            # 极速检测 - 优化检测参数
            max_checks = 15  # 增加检测次数但减少间隔
            check_interval = 0.2  # 每0.2秒检查一次，更快响应

            for i in range(max_checks):
                try:
                    current_url = self.driver.current_url
                    page_source = self.driver.page_source.lower()

                    # 1. 最优先：URL快速检测（扩展URL匹配）
                    success_urls = [
                        "mail.sina.com.cn/classic",
                        "mail.sina.com.cn/#",
                        "mail.sina.com.cn/cgi-bin",
                        "m0.mail.sina.com.cn/classic",  # 移动版邮箱
                        "m1.mail.sina.com.cn/classic",  # 移动版邮箱
                        "m2.mail.sina.com.cn/classic"   # 移动版邮箱
                    ]

                    # 检查URL是否匹配成功标识
                    url_matched = any(url in current_url for url in success_urls)

                    # 额外检查：URL包含邮箱相关参数
                    url_params_matched = any(param in current_url for param in [
                        "action=mailinfo", "title=%25E9%2582%25AE%25E7%25AE%25B1", "mailbox", "inbox"
                    ])

                    # 详细调试信息
                    logger.debug(f"🔍 第{i+1}次检测: URL匹配={url_matched}, 参数匹配={url_params_matched}")

                    if url_matched or url_params_matched:
                        logger.info(f"🚀 URL检测成功 (第{i+1}次): {current_url}")
                        logger.info(f"✅ 登录验证成功，开始处理登录成功流程...")

                        # 立即处理登录成功
                        result = self._handle_login_success_fast(account)
                        logger.info(f"🎯 登录成功处理结果: {result}")
                        return result

                    # 2. 关键词快速检测
                    priority_keywords = [
                        "收件箱", "inbox", "mailbox", "邮件列表",
                        "写邮件", "compose", "新邮件", "newmail"
                    ]

                    if any(keyword in page_source for keyword in priority_keywords):
                        logger.info(f"⚡ 关键词检测成功 (第{i+1}次): 发现登录标识")
                        return self._handle_login_success_fast(account)

                    # 3. Cookie检测
                    try:
                        cookies = self.driver.get_cookies()
                        session_cookies = [c for c in cookies if 'session' in c['name'].lower() or 'login' in c['name'].lower() or 'auth' in c['name'].lower()]
                        if session_cookies and "mail.sina.com" in current_url:
                            logger.info(f"🍪 Cookie检测成功 (第{i+1}次): 发现会话Cookie")
                            return self._handle_login_success_fast(account)
                    except:
                        pass

                    # 短暂等待后继续检测
                    time.sleep(check_interval)

                except Exception as check_error:
                    logger.debug(f"第{i+1}次检测异常: {check_error}")
                    time.sleep(check_interval)
                    continue

            logger.warning("⚠️ 快速检测未成功")
            return False, "验证后登录状态不明确"

        except Exception as e:
            logger.error(f"❌ 验证登录成功失败: {e}")
            return False, f"验证失败: {e}"

    def _handle_login_success_fast(self, account: Account) -> Tuple[bool, str]:
        """快速处理登录成功"""
        try:
            logger.info(f"🎉 快速处理登录成功开始: {account.email}")

            # 自动提取并保存Cookie
            try:
                logger.info("🍪 开始提取并保存Cookie...")
                self._extract_and_save_cookies(account)
                logger.info("✅ Cookie提取保存完成")
            except Exception as cookie_error:
                logger.error(f"❌ Cookie提取失败: {cookie_error}")

            # 执行登录成功后的操作
            try:
                logger.info("🎯 执行登录成功后的操作...")
                self._handle_login_success(account)
                logger.info("✅ 登录成功操作完成")
            except Exception as success_error:
                logger.error(f"❌ 登录成功操作失败: {success_error}")

            # 验证成功后重新隐藏浏览器
            if not self.stealth_mode:
                try:
                    logger.info("🙈 准备隐藏浏览器...")
                    self.driver.minimize_window()
                    logger.info("✅ 浏览器已隐藏")
                except Exception as hide_error:
                    logger.warning(f"⚠️ 浏览器隐藏失败: {hide_error}")

            logger.info(f"🎉 快速处理登录成功完成: {account.email}")
            return True, "验证完成，登录成功"

        except Exception as e:
            logger.error(f"❌ 快速处理登录成功失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return True, "登录成功"  # 即使处理失败，登录仍然成功

    def _stealth_cleanup(self):
        """隐藏式清理资源"""
        try:
            logger.info("🧹 隐藏式清理资源...")

            if self.driver and self.driver_id:
                try:
                    # 如果验证窗口已显示，给用户一些时间
                    if self.verification_window_shown:
                        logger.info("⏳ 验证窗口已显示，延迟清理...")
                        time.sleep(2)

                    # 优雅关闭浏览器
                    self.browser_manager.close_driver(self.driver_id)
                    logger.info("✅ 隐藏浏览器关闭成功")
                except Exception as e:
                    logger.warning(f"⚠️ 浏览器关闭失败: {e}")
                finally:
                    self.driver = None
                    self.driver_id = None
                    self.verification_window_shown = False

            logger.info("✅ 隐藏式清理完成")

        except Exception as e:
            logger.error(f"❌ 隐藏式清理失败: {e}")
            # 确保引用被清空
            self.driver = None
            self.driver_id = None
            self.verification_window_shown = False

    def _advanced_verification_detection(self, page_source: str) -> bool:
        """高级验证码检测 - 多层检测机制"""
        try:
            logger.info("🧠 执行高级验证码检测...")

            # 极速关键词检测（最常见的验证码标识）
            priority_keywords = ["验证码", "captcha", "verification", "verify"]

            # 快速检测最重要的关键词
            for keyword in priority_keywords:
                if keyword in page_source:
                    logger.info(f"⚡ 极速检测：发现验证码关键词 '{keyword}'")
                    return True

            # 次优先级验证码元素
            verification_elements = ["geetest", "recaptcha", "slider", "puzzle"]

            for element in verification_elements:
                if element in page_source:
                    logger.info(f"🎯 极速检测：发现验证码元素 '{element}'")
                    return True

            # 验证码提示快速检测
            verification_prompts = ["请完成", "请验证", "点击完成"]

            for prompt in verification_prompts:
                if prompt in page_source:
                    logger.info(f"💬 极速检测：发现验证码提示 '{prompt}'")
                    return True

            # 如果都没检测到，返回False
            has_verification = False

            logger.info(f"✅ 极速验证码检测结果: {'有验证码' if has_verification else '无验证码'}")

            return has_verification

        except Exception as e:
            logger.error(f"❌ 高级验证码检测失败: {e}")
            # 检测失败时，降级到简单关键词检测
            return any(keyword in page_source for keyword in ["验证码", "captcha", "verify"])

    def _detect_verification_elements(self) -> bool:
        """检测验证码DOM元素"""
        try:
            # 常见验证码元素选择器
            verification_selectors = [
                # 通用验证码
                ".captcha", "#captcha", "[class*='captcha']",
                ".verify", "#verify", "[class*='verify']",
                ".validation", "#validation", "[class*='validation']",
                # 具体验证码服务
                ".geetest_radar_tip", ".geetest_slider_button",
                ".recaptcha-checkbox", ".g-recaptcha",
                ".hcaptcha-box", ".h-captcha",
                ".cf-turnstile", ".cloudflare-turnstile",
                # 滑动验证
                ".slider", ".slide", "[class*='slider']",
                ".drag", "[class*='drag']",
                # 图片验证
                ".pic-verify", ".img-verify", "[class*='pic']",
                # 新浪特有
                ".sina-verify", ".weibo-verify"
            ]

            for selector in verification_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        # 检查元素是否可见
                        for element in elements:
                            if element.is_displayed():
                                logger.info(f"🎯 发现可见验证码元素: {selector}")
                                return True
                except:
                    continue

            return False

        except Exception as e:
            logger.error(f"❌ DOM元素检测失败: {e}")
            return False

    def _detect_verification_url(self) -> bool:
        """检测验证码相关URL"""
        try:
            current_url = self.driver.current_url.lower()

            # 验证码相关URL关键词
            url_keywords = [
                "captcha", "verify", "validation", "challenge",
                "robot", "security", "check", "confirm"
            ]

            return any(keyword in current_url for keyword in url_keywords)

        except Exception as e:
            logger.error(f"❌ URL检测失败: {e}")
            return False

    def _calculate_verification_window_size(self) -> tuple:
        """计算验证框最佳窗口大小"""
        try:
            logger.info("📐 计算验证框最佳窗口大小...")

            # 检测验证码类型并返回对应的最佳窗口大小
            page_source = self.driver.page_source.lower()

            # 不同验证码类型的最佳窗口大小（确保足够大以显示完整验证码）
            verification_sizes = {
                # 滑动验证码 - 较宽的窗口
                'slider': (700, 550),
                'slide': (700, 550),
                'geetest': (700, 550),
                '滑动': (700, 550),

                # 点击验证码 - 中等窗口
                'click': (650, 500),
                'captcha': (650, 500),
                'recaptcha': (650, 500),
                '点击': (650, 500),

                # 图片验证码 - 较大窗口
                'image': (750, 600),
                'picture': (750, 600),
                'pic': (750, 600),
                '图片': (750, 600),

                # 拖拽验证码 - 中等窗口
                'drag': (680, 520),
                'puzzle': (680, 520),
                '拖拽': (680, 520),

                # 新浪特有验证码
                'sina': (650, 500),
                '新浪': (650, 500),

                # 默认验证码 - 标准窗口（确保最小可用大小）
                'verify': (600, 450),
                'validation': (600, 450),
                '验证': (600, 450)
            }

            # 检测验证码类型
            detected_type = None
            for vtype, size in verification_sizes.items():
                if vtype in page_source:
                    detected_type = vtype
                    optimal_size = size
                    break

            if detected_type:
                logger.info(f"🎯 检测到验证码类型: {detected_type}, 最佳窗口大小: {optimal_size}")
                return optimal_size
            else:
                # 默认验证框大小（确保足够大）
                default_size = (650, 500)
                logger.info(f"📏 使用默认验证框大小: {default_size}")
                return default_size

        except Exception as e:
            logger.error(f"❌ 计算窗口大小失败: {e}")
            return (650, 500)  # 返回默认大小

    def _switch_to_verification_mode(self, account: Account, window_size: tuple) -> bool:
        """从无头模式切换到验证模式"""
        try:
            logger.info("🔄 从无头模式切换到验证模式...")

            # 保存当前页面状态
            current_url = None
            page_source = None
            try:
                current_url = self.driver.current_url
                page_source = self.driver.page_source
                logger.info(f"📄 保存当前页面状态: {current_url}")
            except:
                pass

            # 关闭当前无头浏览器
            if self.driver and self.driver_id:
                logger.info("🔄 关闭无头浏览器...")
                self.browser_manager.close_driver(self.driver_id)
                self.driver = None
                self.driver_id = None

            # 创建新的可见浏览器用于验证
            logger.info("🖥️ 创建可见浏览器...")

            # 临时修改浏览器配置
            original_headless = self.browser_manager.browser_config.get('headless', False)
            original_window_size = self.browser_manager.browser_config.get('window_size', [1920, 1080])

            # 设置验证模式配置
            self.browser_manager.browser_config['headless'] = False
            self.browser_manager.browser_config['window_size'] = list(window_size)

            # 创建新的浏览器驱动
            self.driver_id = self.browser_manager.create_driver(account)
            self.driver = self.browser_manager.get_driver(self.driver_id)

            # 恢复原始配置
            self.browser_manager.browser_config['headless'] = original_headless
            self.browser_manager.browser_config['window_size'] = original_window_size

            if not self.driver:
                logger.error("❌ 无法创建可见浏览器")
                return False

            # 重新访问登录页面
            logger.info("🌐 重新访问登录页面...")
            self.driver.get("https://mail.sina.com.cn")
            time.sleep(2)  # 等待页面加载

            # 重新输入账号密码
            logger.info("⚡ 重新输入账号密码...")
            self._lightning_input(account)
            self._instant_click_login()

            # 等待验证码出现
            time.sleep(3)

            # 调整窗口到验证框位置
            self._adjust_window_for_verification(window_size)

            # 确保窗口在最前面
            try:
                self.driver.execute_script("window.focus();")
                self.driver.switch_to.window(self.driver.current_window_handle)
            except:
                pass

            logger.info("✅ 成功切换到验证模式")
            return True

        except Exception as e:
            logger.error(f"❌ 切换验证模式失败: {e}")
            return False

    def _adjust_window_for_verification(self, window_size: tuple):
        """调整窗口到验证框最佳位置和大小"""
        try:
            logger.info(f"🎯 调整窗口到验证框最佳大小: {window_size}")

            # 确保最小窗口大小，能够完整显示验证码
            min_width = max(window_size[0], 600)  # 最小宽度600px
            min_height = max(window_size[1], 500)  # 最小高度500px

            # 设置窗口大小
            self.driver.set_window_size(min_width, min_height)

            # 获取实际屏幕尺寸
            try:
                screen_width = self.driver.execute_script("return screen.width;")
                screen_height = self.driver.execute_script("return screen.height;")
            except:
                screen_width = 1920  # 默认值
                screen_height = 1080

            # 计算屏幕中央位置
            x = max(0, (screen_width - min_width) // 2)
            y = max(0, (screen_height - min_height) // 2)

            # 设置窗口位置到屏幕中央
            self.driver.set_window_position(x, y)

            # 确保窗口在最前面并获得焦点
            self.driver.execute_script("window.focus();")
            self.driver.execute_script("window.scrollTo(0, 0);")  # 滚动到顶部

            # 等待窗口调整完成
            time.sleep(0.5)

            logger.info(f"📍 窗口已调整到位置: ({x}, {y}), 大小: {min_width}x{min_height}")

        except Exception as e:
            logger.error(f"❌ 调整窗口失败: {e}")

    def _create_emergency_verification_browser(self, account: Account, window_size: tuple) -> bool:
        """创建紧急验证浏览器（最后的降级方案）"""
        try:
            logger.info("🆘 创建紧急验证浏览器...")

            # 关闭当前浏览器
            if self.driver and self.driver_id:
                try:
                    self.browser_manager.close_driver(self.driver_id)
                except:
                    pass
                self.driver = None
                self.driver_id = None

            # 创建最简单的可见浏览器配置
            emergency_config = {
                'headless': False,
                'window_size': list(window_size),
                'disable_images': True,  # 加快加载
                'disable_javascript': False,  # 保留JS以支持验证码
                'chrome_options': [
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-web-security'
                ]
            }

            # 临时替换配置
            original_config = self.browser_manager.browser_config.copy()
            self.browser_manager.browser_config.update(emergency_config)

            # 创建紧急浏览器
            self.driver_id = self.browser_manager.create_driver(account)
            self.driver = self.browser_manager.get_driver(self.driver_id)

            # 恢复原配置
            self.browser_manager.browser_config = original_config

            if not self.driver:
                logger.error("❌ 紧急浏览器创建失败")
                return False

            # 访问登录页面
            self.driver.get("https://mail.sina.com.cn")
            time.sleep(2)

            # 重新登录
            self._lightning_input(account)
            self._instant_click_login()

            # 设置窗口
            try:
                self.driver.set_window_size(window_size[0], window_size[1])
                self.driver.set_window_position(100, 100)
                self.driver.execute_script("window.focus();")
            except:
                pass

            logger.info("✅ 紧急验证浏览器创建成功")
            return True

        except Exception as e:
            logger.error(f"❌ 创建紧急验证浏览器失败: {e}")
            return False

    def _enhanced_login_success_detection(self, page_source: str, current_url: str) -> Tuple[bool, str]:
        """极速登录成功检测算法"""
        try:
            logger.info("⚡ 执行极速登录成功检测...")

            # 1. 最优先：URL快速检测（最可靠且最快）
            priority_url_indicators = [
                "mail.sina.com.cn/classic",
                "mail.sina.com.cn/#",
                "mail.sina.com.cn/cgi-bin"
            ]

            for url_indicator in priority_url_indicators:
                if url_indicator in current_url:
                    logger.info(f"🚀 URL极速检测成功: {url_indicator}")
                    return True, "URL检测登录成功"

            # 2. 关键词极速检测（最重要的标识）
            priority_keywords = [
                "收件箱", "inbox", "mailbox", "邮件列表"
            ]

            for keyword in priority_keywords:
                if keyword in page_source:
                    logger.info(f"⚡ 关键词极速检测成功: {keyword}")
                    return True, "关键词检测登录成功"

            # 3. Cookie快速检测
            try:
                cookies = self.driver.get_cookies()
                session_cookies = [c for c in cookies if 'session' in c['name'].lower() or 'login' in c['name'].lower() or 'auth' in c['name'].lower()]
                if session_cookies and "mail.sina.com" in current_url:
                    logger.info("🍪 Cookie极速检测成功")
                    return True, "Cookie检测登录成功"
            except:
                pass

            # 4. 次优先级关键词检测
            secondary_keywords = [
                "写邮件", "compose", "新邮件", "newmail",
                "通讯录", "contacts", "设置", "settings"
            ]

            secondary_matches = [keyword for keyword in secondary_keywords if keyword in page_source]
            if secondary_matches and "mail.sina.com" in current_url:
                logger.info(f"✅ 次级检测成功: {secondary_matches[:2]}")
                return True, "次级检测登录成功"

            # 5. 如果以上都没检测到，返回未成功
            logger.info("⚠️ 极速检测未发现登录成功标识")
            return False, "未检测到登录成功"

        except Exception as e:
            logger.error(f"❌ 极速登录检测失败: {e}")
            return False, f"检测异常: {e}"

    def _detect_mailbox_elements(self) -> bool:
        """检测邮箱页面特有的DOM元素"""
        try:
            # 邮箱页面常见元素选择器
            mailbox_selectors = [
                # 新浪邮箱特有元素
                ".mail_list", ".mail_item", ".inbox_list",
                ".compose_btn", ".new_mail", ".mail_toolbar",

                # 通用邮箱元素
                "[class*='inbox']", "[class*='mail']", "[class*='compose']",
                "[id*='inbox']", "[id*='mail']", "[id*='compose']",

                # 邮件列表相关
                ".email_list", ".message_list", ".mail_content",
                "[class*='email']", "[class*='message']"
            ]

            for selector in mailbox_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        # 检查元素是否可见
                        for element in elements:
                            if element.is_displayed():
                                logger.info(f"🎯 发现邮箱元素: {selector}")
                                return True
                except:
                    continue

            return False

        except Exception as e:
            logger.error(f"❌ DOM元素检测失败: {e}")
            return False

    def _simple_login_success_detection(self, page_source: str, current_url: str) -> Tuple[bool, str]:
        """简单的登录成功检测（降级方案）"""
        try:
            logger.info("🔄 使用简单登录检测...")

            # 基本URL检测
            if "mail.sina.com.cn" in current_url:
                # 基本内容检测
                success_keywords = ["收件箱", "inbox", "mailbox", "compose"]
                if any(keyword in page_source for keyword in success_keywords):
                    return True, "简单检测：登录成功"

            return False, "简单检测：登录状态不明确"

        except Exception as e:
            logger.error(f"❌ 简单登录检测失败: {e}")
            return False, f"检测失败: {e}"

    def _handle_login_success(self, account: Account):
        """处理登录成功后的操作"""
        try:
            logger.info("🎉 处理登录成功后的操作...")

            # 1. 记录登录成功信息
            self._record_login_success(account)

            # 2. 收集邮箱基本信息
            mailbox_info = self._collect_mailbox_info()

            # 3. 检查邮箱功能状态
            function_status = self._check_mailbox_functions()

            # 4. 准备后续操作选项
            self._prepare_next_actions(account, mailbox_info, function_status)

            logger.info("✅ 登录成功后操作处理完成")

        except Exception as e:
            logger.error(f"❌ 处理登录成功操作失败: {e}")

    def _record_login_success(self, account: Account):
        """记录登录成功信息"""
        try:
            current_time = time.strftime("%Y-%m-%d %H:%M:%S")
            current_url = self.driver.current_url
            page_title = self.driver.title

            logger.info(f"📝 登录成功记录:")
            logger.info(f"   账号: {account.email}")
            logger.info(f"   时间: {current_time}")
            logger.info(f"   URL: {current_url}")
            logger.info(f"   页面标题: {page_title}")

            # 这里可以扩展为保存到数据库或文件

        except Exception as e:
            logger.error(f"❌ 记录登录信息失败: {e}")

    def _collect_mailbox_info(self) -> dict:
        """收集邮箱基本信息"""
        try:
            logger.info("📊 收集邮箱基本信息...")

            mailbox_info = {
                'unread_count': 0,
                'total_emails': 0,
                'storage_used': 'unknown',
                'last_login': 'unknown'
            }

            # 尝试获取未读邮件数量
            try:
                unread_elements = self.driver.find_elements(By.CSS_SELECTOR,
                    ".unread, [class*='unread'], .new_mail, [class*='new']")
                if unread_elements:
                    mailbox_info['unread_count'] = len(unread_elements)
            except:
                pass

            # 尝试获取存储使用情况
            try:
                storage_elements = self.driver.find_elements(By.CSS_SELECTOR,
                    "[class*='storage'], [class*='quota'], [class*='space']")
                if storage_elements:
                    storage_text = storage_elements[0].text
                    mailbox_info['storage_used'] = storage_text
            except:
                pass

            logger.info(f"📊 邮箱信息: {mailbox_info}")
            return mailbox_info

        except Exception as e:
            logger.error(f"❌ 收集邮箱信息失败: {e}")
            return {}

    def _check_mailbox_functions(self) -> dict:
        """检查邮箱功能状态"""
        try:
            logger.info("🔧 检查邮箱功能状态...")

            function_status = {
                'compose_available': False,
                'inbox_accessible': False,
                'contacts_available': False,
                'settings_accessible': False
            }

            # 检查写邮件功能
            try:
                compose_elements = self.driver.find_elements(By.CSS_SELECTOR,
                    ".compose, [class*='compose'], .write, [class*='write']")
                function_status['compose_available'] = len(compose_elements) > 0
            except:
                pass

            # 检查收件箱功能
            try:
                inbox_elements = self.driver.find_elements(By.CSS_SELECTOR,
                    ".inbox, [class*='inbox'], .mail_list, [class*='mail_list']")
                function_status['inbox_accessible'] = len(inbox_elements) > 0
            except:
                pass

            # 检查通讯录功能
            try:
                contacts_elements = self.driver.find_elements(By.CSS_SELECTOR,
                    ".contacts, [class*='contacts'], .address, [class*='address']")
                function_status['contacts_available'] = len(contacts_elements) > 0
            except:
                pass

            # 检查设置功能
            try:
                settings_elements = self.driver.find_elements(By.CSS_SELECTOR,
                    ".settings, [class*='settings'], .config, [class*='config']")
                function_status['settings_accessible'] = len(settings_elements) > 0
            except:
                pass

            logger.info(f"🔧 功能状态: {function_status}")
            return function_status

        except Exception as e:
            logger.error(f"❌ 检查功能状态失败: {e}")
            return {}

    def _prepare_next_actions(self, account: Account, mailbox_info: dict, function_status: dict):
        """准备后续操作选项"""
        try:
            logger.info("🎯 准备后续操作选项...")

            available_actions = []

            # 根据功能状态确定可用操作
            if function_status.get('compose_available'):
                available_actions.append("写邮件")

            if function_status.get('inbox_accessible'):
                available_actions.append("查看收件箱")

            if function_status.get('contacts_available'):
                available_actions.append("管理通讯录")

            if function_status.get('settings_accessible'):
                available_actions.append("邮箱设置")

            # 通用操作
            available_actions.extend(["保持登录状态", "安全退出"])

            logger.info(f"🎯 可用操作: {available_actions}")

            # 这里可以扩展为显示操作菜单或返回给调用者

        except Exception as e:
            logger.error(f"❌ 准备后续操作失败: {e}")

    def get_next_action_menu(self) -> list:
        """获取下一步操作菜单"""
        return [
            "📧 查看收件箱",
            "✍️ 写新邮件",
            "📋 管理通讯录",
            "⚙️ 邮箱设置",
            "🔒 保持登录",
            "🚪 安全退出"
        ]

    def _extract_and_save_cookies(self, account: Account) -> bool:
        """提取并保存Cookie到本地"""
        try:
            if not self.driver or not self.cookie_manager:
                logger.warning("⚠️ 浏览器驱动或Cookie管理器不可用")
                return False

            logger.info(f"🍪 开始提取Cookie: {account.email}")

            # 使用Cookie管理器提取Cookie
            success = self.cookie_manager.extract_cookies_from_driver(self.driver, account.email)

            if success:
                logger.info(f"✅ Cookie提取并保存成功: {account.email}")

                # 验证Cookie是否可用
                cookie_data = self.cookie_manager.get_cookies(account.email)
                if cookie_data:
                    cookie_count = len(cookie_data.get('cookies', []))
                    logger.info(f"🎯 验证Cookie可用: {account.email}, {cookie_count} 个Cookie")
                else:
                    logger.warning(f"⚠️ Cookie验证失败: {account.email}")

                return True
            else:
                logger.error(f"❌ Cookie提取失败: {account.email}")
                return False

        except Exception as e:
            logger.error(f"❌ 提取保存Cookie异常: {account.email}, 错误: {e}")
            return False

    def get_saved_cookie_info(self, account_email: str) -> Optional[dict]:
        """获取已保存的Cookie信息"""
        try:
            if not self.cookie_manager:
                return None

            return self.cookie_manager.get_cookies(account_email)

        except Exception as e:
            logger.error(f"获取Cookie信息失败: {e}")
            return None

    def has_valid_cookies(self, account_email: str) -> bool:
        """检查是否有有效的Cookie"""
        try:
            cookie_data = self.get_saved_cookie_info(account_email)
            return cookie_data is not None and len(cookie_data.get('cookies', [])) > 0

        except Exception as e:
            logger.error(f"检查Cookie有效性失败: {e}")
            return False
