2025-08-01 00:05:40 | ERROR    | src.utils.encryption:decrypt:104 | 数据解密失败: Incorrect padding
2025-08-01 20:10:56 | ERROR    | src.utils.encryption:decrypt:104 | 数据解密失败: Incorrect padding
2025-08-01 20:11:38 | ERROR    | src.utils.encryption:decrypt:104 | 数据解密失败: Invalid base64-encoded string: number of data characters (9) cannot be 1 more than a multiple of 4
2025-08-01 21:03:40 | ERROR    | src.utils.encryption:decrypt:117 | 数据解密失败: 
2025-08-01 21:04:44 | ERROR    | src.utils.encryption:decrypt:117 | 数据解密失败: 
2025-08-01 21:04:44 | ERROR    | src.utils.encryption:decrypt:117 | 数据解密失败: 
2025-08-01 21:05:03 | ERROR    | src.utils.encryption:decrypt:117 | 数据解密失败: 
2025-08-01 21:09:28 | ERROR    | src.utils.encryption:decrypt:117 | 数据解密失败: 
2025-08-01 21:09:28 | ERROR    | src.utils.encryption:decrypt:117 | 数据解密失败: 
2025-08-01 21:15:33 | ERROR    | src.utils.encryption:decrypt:117 | 数据解密失败: 
2025-08-01 21:15:33 | ERROR    | src.utils.encryption:decrypt:117 | 数据解密失败: 
2025-08-01 21:15:45 | ERROR    | src.utils.encryption:decrypt:117 | 数据解密失败: 
2025-08-01 21:25:08 | ERROR    | src.core.browser_manager:create_driver:91 | 创建浏览器驱动失败: Could not reach host. Are you offline?
2025-08-01 22:45:17 | ERROR    | src.core.account_login_manager:_check_login_in_frame:1385 | ❌ 在当前框架中检查登录状态失败: Message: tab crashed
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0xf0ba83+63395]
	GetHandleVerifier [0x0xf0bac4+63460]
	(No symbol) [0x0xd51f70]
	(No symbol) [0x0xd42b60]
	(No symbol) [0x0xd40b44]
	(No symbol) [0x0xd4160d]
	(No symbol) [0x0xd4dda9]
	(No symbol) [0x0xd5f2b5]
	(No symbol) [0x0xd64cb6]
	(No symbol) [0x0xd41c4d]
	(No symbol) [0x0xd5eb43]
	(No symbol) [0x0xde06c9]
	(No symbol) [0x0xdbf1a6]
	(No symbol) [0x0xd8e7b2]
	(No symbol) [0x0xd8f654]
	GetHandleVerifier [0x0x1188883+2672035]
	GetHandleVerifier [0x0x1183cba+2652634]
	GetHandleVerifier [0x0xf32bca+223466]
	GetHandleVerifier [0x0xf22cb8+158168]
	GetHandleVerifier [0x0xf2978d+185517]
	GetHandleVerifier [0x0xf13b78+96408]
	GetHandleVerifier [0x0xf13d02+96802]
	GetHandleVerifier [0x0xefe90a+9770]
	BaseThreadInitThunk [0x0x7682fa29+25]
	RtlGetAppContainerNamedObjectPath [0x0x776c7a9e+286]
	RtlGetAppContainerNamedObjectPath [0x0x776c7a6e+238]

2025-08-01 22:45:17 | ERROR    | src.core.account_login_manager:_check_login_in_frame:1385 | ❌ 在当前框架中检查登录状态失败: Message: tab crashed
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0xf0ba83+63395]
	GetHandleVerifier [0x0xf0bac4+63460]
	(No symbol) [0x0xd51f70]
	(No symbol) [0x0xd42b60]
	(No symbol) [0x0xd40b44]
	(No symbol) [0x0xd4160d]
	(No symbol) [0x0xd4dda9]
	(No symbol) [0x0xd5f2b5]
	(No symbol) [0x0xd64cb6]
	(No symbol) [0x0xd41c4d]
	(No symbol) [0x0xd5eb43]
	(No symbol) [0x0xde06c9]
	(No symbol) [0x0xdbf1a6]
	(No symbol) [0x0xd8e7b2]
	(No symbol) [0x0xd8f654]
	GetHandleVerifier [0x0x1188883+2672035]
	GetHandleVerifier [0x0x1183cba+2652634]
	GetHandleVerifier [0x0xf32bca+223466]
	GetHandleVerifier [0x0xf22cb8+158168]
	GetHandleVerifier [0x0xf2978d+185517]
	GetHandleVerifier [0x0xf13b78+96408]
	GetHandleVerifier [0x0xf13d02+96802]
	GetHandleVerifier [0x0xefe90a+9770]
	BaseThreadInitThunk [0x0x7682fa29+25]
	RtlGetAppContainerNamedObjectPath [0x0x776c7a9e+286]
	RtlGetAppContainerNamedObjectPath [0x0x776c7a6e+238]

2025-08-01 23:43:50 | ERROR    | src.core.browser_manager:create_driver:91 | 创建浏览器驱动失败: Could not reach host. Are you offline?
2025-08-02 02:17:11 | ERROR    | src.core.stealth_login_manager:_detect_verification_url:594 | ❌ URL检测失败: 'NoneType' object has no attribute 'current_url'
2025-08-02 02:17:11 | ERROR    | src.core.stealth_login_manager:_detect_verification_url:594 | ❌ URL检测失败: 'NoneType' object has no attribute 'current_url'
2025-08-02 02:17:11 | ERROR    | src.core.stealth_login_manager:_detect_verification_url:594 | ❌ URL检测失败: 'NoneType' object has no attribute 'current_url'
2025-08-02 02:17:11 | ERROR    | src.core.stealth_login_manager:_detect_verification_url:594 | ❌ URL检测失败: 'NoneType' object has no attribute 'current_url'
2025-08-02 02:17:11 | ERROR    | src.core.stealth_login_manager:_detect_verification_url:594 | ❌ URL检测失败: 'NoneType' object has no attribute 'current_url'
2025-08-02 03:09:58 | ERROR    | __main__:main:154 | 程序启动失败: CookieManager.__init__() missing 1 required positional argument: 'config'

详细错误信息:
Traceback (most recent call last):
  File "D:\1-Ai\youxiang\sina\main.py", line 139, in main
    main_window = MainWindow(config)
  File "D:\1-Ai\youxiang\sina\src\gui\main_window.py", line 46, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "D:\1-Ai\youxiang\sina\src\gui\main_window.py", line 79, in init_ui
    self.setup_tabs()
    ~~~~~~~~~~~~~~~^^
  File "D:\1-Ai\youxiang\sina\src\gui\main_window.py", line 127, in setup_tabs
    lightweight_tab = self.create_lightweight_tab()
  File "D:\1-Ai\youxiang\sina\src\gui\main_window.py", line 163, in create_lightweight_tab
    cookie_manager = CookieManager()
TypeError: CookieManager.__init__() missing 1 required positional argument: 'config'

2025-08-02 03:19:44 | ERROR    | src.core.lightweight_email_sender:create_lightweight_session:124 | ❌ 创建轻量化会话失败: <EMAIL>, 错误: 'CookieManager' object has no attribute 'get_cookies'
2025-08-02 03:29:15 | ERROR    | src.core.lightweight_email_sender:create_lightweight_session:124 | ❌ 创建轻量化会话失败: <EMAIL>, 错误: 'CookieManager' object has no attribute 'get_cookies'
2025-08-02 03:56:55 | ERROR    | src.core.proxy_manager:bind_account_proxy:401 | 代理不存在: None
2025-08-02 04:03:29 | ERROR    | src.core.proxy_manager:bind_account_proxy:401 | 代理不存在: None
2025-08-02 04:06:32 | ERROR    | src.core.proxy_manager:bind_account_proxy:401 | 代理不存在: None
2025-08-02 04:17:40 | ERROR    | src.core.proxy_manager:bind_account_proxy:401 | 代理不存在: None
2025-08-02 04:24:39 | ERROR    | src.core.proxy_manager:bind_account_proxy:401 | 代理不存在: None
2025-08-02 04:24:40 | ERROR    | src.core.proxy_manager:bind_account_proxy:401 | 代理不存在: None
2025-08-02 04:25:34 | ERROR    | src.core.proxy_manager:bind_account_proxy:401 | 代理不存在: None
2025-08-02 04:25:37 | ERROR    | src.core.proxy_manager:bind_account_proxy:401 | 代理不存在: None
2025-08-02 04:26:11 | ERROR    | src.core.proxy_manager:bind_account_proxy:401 | 代理不存在: None
2025-08-02 04:29:07 | ERROR    | src.core.proxy_manager:bind_account_proxy:401 | 代理不存在: None
2025-08-02 04:30:02 | ERROR    | src.core.proxy_manager:bind_account_proxy:401 | 代理不存在: None
2025-08-02 04:31:01 | ERROR    | src.core.proxy_manager:bind_account_proxy:401 | 代理不存在: None
2025-08-02 04:34:00 | ERROR    | src.core.proxy_manager:get_proxy_for_account:494 | 获取账号代理失败: '<EMAIL>'
2025-08-02 04:34:01 | ERROR    | src.core.proxy_manager:get_proxy_for_account:494 | 获取账号代理失败: '<EMAIL>'
2025-08-02 04:34:36 | ERROR    | src.core.proxy_manager:get_proxy_for_account:494 | 获取账号代理失败: '<EMAIL>'
2025-08-02 04:34:37 | ERROR    | src.core.proxy_manager:get_proxy_for_account:494 | 获取账号代理失败: '<EMAIL>'
2025-08-02 04:36:28 | ERROR    | src.core.proxy_manager:get_proxy_for_account:494 | 获取账号代理失败: '<EMAIL>'
2025-08-02 04:38:16 | ERROR    | src.core.proxy_manager:get_proxy_for_account:494 | 获取账号代理失败: '<EMAIL>'
2025-08-02 04:40:30 | ERROR    | src.core.proxy_manager:get_proxy_for_account:494 | 获取账号代理失败: '<EMAIL>'
2025-08-02 04:41:08 | ERROR    | src.core.proxy_manager:get_proxy_for_account:494 | 获取账号代理失败: '<EMAIL>'
2025-08-02 04:53:57 | ERROR    | src.core.lightweight_email_sender:create_lightweight_session:165 | ❌ 创建轻量化会话失败: <EMAIL>, 错误: 'ProxyManager' object has no attribute 'get_cookies'
2025-08-02 05:09:53 | ERROR    | src.core.lightweight_email_sender:_submit_email:485 | ❌ 所有发送接口都失败
2025-08-02 05:11:01 | ERROR    | src.core.lightweight_email_sender:_submit_email:543 | ❌ 所有发送接口都失败
2025-08-02 05:11:11 | ERROR    | src.core.lightweight_email_sender:_submit_email:552 | ❌ 所有发送接口都失败
2025-08-02 21:30:15 | ERROR    | src.core.lightweight_email_sender:_submit_email:552 | ❌ 所有发送接口都失败
2025-08-02 21:46:14 | ERROR    | __main__:load_cookies:59 | ❌ 加载Cookie失败: string indices must be integers, not 'str'
2025-08-02 21:46:16 | ERROR    | __main__:run_discovery:265 | ❌ 未发现任何接口
2025-08-02 21:52:36 | ERROR    | src.core.lightweight_email_sender:_submit_email:552 | ❌ 所有发送接口都失败
2025-08-03 00:34:28 | ERROR    | src.core.browser_manager:safe_send_keys:456 | 输入文本失败: css selector=input[name='username'], 错误: Message: element not interactable
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementnotinteractableexception
Stacktrace:
	GetHandleVerifier [0x0xabba83+63395]
	GetHandleVerifier [0x0xabbac4+63460]
	(No symbol) [0x0x901f70]
	(No symbol) [0x0x942653]
	(No symbol) [0x0x96f3ac]
	(No symbol) [0x0x93ffa4]
	(No symbol) [0x0x96f624]
	(No symbol) [0x0x9907ba]
	(No symbol) [0x0x96f1a6]
	(No symbol) [0x0x93e7b2]
	(No symbol) [0x0x93f654]
	GetHandleVerifier [0x0xd38883+2672035]
	GetHandleVerifier [0x0xd33cba+2652634]
	GetHandleVerifier [0x0xae2bca+223466]
	GetHandleVerifier [0x0xad2cb8+158168]
	GetHandleVerifier [0x0xad978d+185517]
	GetHandleVerifier [0x0xac3b78+96408]
	GetHandleVerifier [0x0xac3d02+96802]
	GetHandleVerifier [0x0xaae90a+9770]
	BaseThreadInitThunk [0x0x7514fa29+25]
	RtlGetAppContainerNamedObjectPath [0x0x772d7a9e+286]
	RtlGetAppContainerNamedObjectPath [0x0x772d7a6e+238]

2025-08-03 00:53:49 | ERROR    | __main__:load_cookies_from_file:69 | ❌ 加载cookies失败: Expecting value: line 1 column 1 (char 0)
2025-08-03 01:36:42 | ERROR    | src.core.sina_ultra_fast_sender_correct:prepare_compose_page:126 | ❌ 未找到或无法点击写信按钮
2025-08-03 02:23:38 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:316 | ❌ 等待15秒后仍未检测到右侧发件界面
2025-08-03 02:23:38 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:317 | ❌ 可能的原因：
2025-08-03 02:23:38 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:318 |    1. 写信按钮点击失败
2025-08-03 02:23:38 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:319 |    2. 页面加载缓慢
2025-08-03 02:23:38 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:320 |    3. 选择器不匹配真实的输入框
2025-08-03 02:23:42 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:330 | ❌ 最后检查失败，右侧发件界面仍未显示
2025-08-03 02:27:41 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:316 | ❌ 等待15秒后仍未检测到右侧发件界面
2025-08-03 02:27:41 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:317 | ❌ 可能的原因：
2025-08-03 02:27:41 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:318 |    1. 写信按钮点击失败
2025-08-03 02:27:41 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:319 |    2. 页面加载缓慢
2025-08-03 02:27:41 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:320 |    3. 选择器不匹配真实的输入框
2025-08-03 02:27:44 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:330 | ❌ 最后检查失败，右侧发件界面仍未显示
2025-08-03 02:42:35 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:333 | ❌ 等待15秒后仍未检测到右侧发件界面
2025-08-03 02:42:35 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:334 | ❌ 可能的原因：
2025-08-03 02:42:35 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:335 |    1. 写信按钮点击失败
2025-08-03 02:42:35 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:336 |    2. 页面加载缓慢
2025-08-03 02:42:35 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:337 |    3. 选择器不匹配真实的输入框
2025-08-03 02:42:42 | ERROR    | src.core.sina_ultra_fast_sender_final:prepare_compose_page:347 | ❌ 最后检查失败，右侧发件界面仍未显示
2025-08-03 02:56:34 | ERROR    | src.core.sina_ultra_fast_sender_final:_send_with_elements:623 | ❌ 未找到收件人字段
2025-08-03 02:56:34 | ERROR    | src.core.sina_ultra_fast_sender_final:send_email_ultra_fast:454 | ❌ 所有发送策略都失败了 (42.81秒)
2025-08-03 05:00:10 | ERROR    | src.core.sina_ultra_fast_sender_final:_send_with_elements:628 | ❌ 元素操作发送失败: Message: element click intercepted: Element <input type="text" value="" size="8"> is not clickable at point (244, 149). Other element would receive the click: <div id="panel_left" class="write-left" _align="client" style="height: 4px;">...</div>
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6bea5e415+77285]
	GetHandleVerifier [0x0x7ff6bea5e470+77376]
	(No symbol) [0x0x7ff6be829a6a]
	(No symbol) [0x0x7ff6be8883e9]
	(No symbol) [0x0x7ff6be885d8b]
	(No symbol) [0x0x7ff6be882dd1]
	(No symbol) [0x0x7ff6be881ce1]
	(No symbol) [0x0x7ff6be873458]
	(No symbol) [0x0x7ff6be8a860a]
	(No symbol) [0x0x7ff6be872d06]
	(No symbol) [0x0x7ff6be8a8820]
	(No symbol) [0x0x7ff6be8d087f]
	(No symbol) [0x0x7ff6be8a83e3]
	(No symbol) [0x0x7ff6be871521]
	(No symbol) [0x0x7ff6be8722b3]
	GetHandleVerifier [0x0x7ff6bed41efd+3107021]
	GetHandleVerifier [0x0x7ff6bed3c29d+3083373]
	GetHandleVerifier [0x0x7ff6bed5bedd+3213485]
	GetHandleVerifier [0x0x7ff6bea7884e+184862]
	GetHandleVerifier [0x0x7ff6bea8055f+216879]
	GetHandleVerifier [0x0x7ff6bea67084+113236]
	GetHandleVerifier [0x0x7ff6bea67239+113673]
	GetHandleVerifier [0x0x7ff6bea4e298+11368]
	BaseThreadInitThunk [0x0x7ffd87b17034+20]
	RtlUserThreadStart [0x0x7ffd87e02651+33]

2025-08-03 05:00:10 | ERROR    | src.core.sina_ultra_fast_sender_final:send_email_ultra_fast:410 | ❌ 所有发送策略都失败了 (2.31秒)
2025-08-03 05:00:10 | ERROR    | src.core.email_sending_scheduler:send_email_with_browser:203 | ❌ 超高速邮件发送失败: task_1754168406897_0 (4.06秒)
2025-08-03 05:00:10 | ERROR    | src.core.email_sending_scheduler:send_email_with_browser:207 | 📈 发送统计: 成功率 0.0%, 总计 1 封
2025-08-03 05:00:14 | ERROR    | src.core.sina_ultra_fast_sender_final:_send_with_elements:628 | ❌ 元素操作发送失败: Message: element click intercepted: Element <input type="text" value="" size="8"> is not clickable at point (244, 149). Other element would receive the click: <div id="panel_left" class="write-left" _align="client" style="height: 4px;">...</div>
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6bea5e415+77285]
	GetHandleVerifier [0x0x7ff6bea5e470+77376]
	(No symbol) [0x0x7ff6be829a6a]
	(No symbol) [0x0x7ff6be8883e9]
	(No symbol) [0x0x7ff6be885d8b]
	(No symbol) [0x0x7ff6be882dd1]
	(No symbol) [0x0x7ff6be881ce1]
	(No symbol) [0x0x7ff6be873458]
	(No symbol) [0x0x7ff6be8a860a]
	(No symbol) [0x0x7ff6be872d06]
	(No symbol) [0x0x7ff6be8a8820]
	(No symbol) [0x0x7ff6be8d087f]
	(No symbol) [0x0x7ff6be8a83e3]
	(No symbol) [0x0x7ff6be871521]
	(No symbol) [0x0x7ff6be8722b3]
	GetHandleVerifier [0x0x7ff6bed41efd+3107021]
	GetHandleVerifier [0x0x7ff6bed3c29d+3083373]
	GetHandleVerifier [0x0x7ff6bed5bedd+3213485]
	GetHandleVerifier [0x0x7ff6bea7884e+184862]
	GetHandleVerifier [0x0x7ff6bea8055f+216879]
	GetHandleVerifier [0x0x7ff6bea67084+113236]
	GetHandleVerifier [0x0x7ff6bea67239+113673]
	GetHandleVerifier [0x0x7ff6bea4e298+11368]
	BaseThreadInitThunk [0x0x7ffd87b17034+20]
	RtlUserThreadStart [0x0x7ffd87e02651+33]

2025-08-03 05:00:14 | ERROR    | src.core.sina_ultra_fast_sender_final:send_email_ultra_fast:410 | ❌ 所有发送策略都失败了 (2.23秒)
2025-08-03 05:00:14 | ERROR    | src.core.email_sending_scheduler:send_email_with_browser:203 | ❌ 超高速邮件发送失败: task_1754168406897_0 (2.26秒)
2025-08-03 05:00:14 | ERROR    | src.core.email_sending_scheduler:send_email_with_browser:207 | 📈 发送统计: 成功率 0.0%, 总计 1 封
2025-08-03 05:00:24 | ERROR    | src.core.sina_ultra_fast_sender_final:_send_with_elements:628 | ❌ 元素操作发送失败: Message: element click intercepted: Element <input type="text" value="" size="8"> is not clickable at point (244, 149). Other element would receive the click: <div id="panel_left" class="write-left" _align="client" style="height: 4px;">...</div>
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6bea5e415+77285]
	GetHandleVerifier [0x0x7ff6bea5e470+77376]
	(No symbol) [0x0x7ff6be829a6a]
	(No symbol) [0x0x7ff6be8883e9]
	(No symbol) [0x0x7ff6be885d8b]
	(No symbol) [0x0x7ff6be882dd1]
	(No symbol) [0x0x7ff6be881ce1]
	(No symbol) [0x0x7ff6be873458]
	(No symbol) [0x0x7ff6be8a860a]
	(No symbol) [0x0x7ff6be872d06]
	(No symbol) [0x0x7ff6be8a8820]
	(No symbol) [0x0x7ff6be8d087f]
	(No symbol) [0x0x7ff6be8a83e3]
	(No symbol) [0x0x7ff6be871521]
	(No symbol) [0x0x7ff6be8722b3]
	GetHandleVerifier [0x0x7ff6bed41efd+3107021]
	GetHandleVerifier [0x0x7ff6bed3c29d+3083373]
	GetHandleVerifier [0x0x7ff6bed5bedd+3213485]
	GetHandleVerifier [0x0x7ff6bea7884e+184862]
	GetHandleVerifier [0x0x7ff6bea8055f+216879]
	GetHandleVerifier [0x0x7ff6bea67084+113236]
	GetHandleVerifier [0x0x7ff6bea67239+113673]
	GetHandleVerifier [0x0x7ff6bea4e298+11368]
	BaseThreadInitThunk [0x0x7ffd87b17034+20]
	RtlUserThreadStart [0x0x7ffd87e02651+33]

2025-08-03 05:00:24 | ERROR    | src.core.sina_ultra_fast_sender_final:send_email_ultra_fast:410 | ❌ 所有发送策略都失败了 (2.25秒)
2025-08-03 05:00:24 | ERROR    | src.core.email_sending_scheduler:send_email_with_browser:203 | ❌ 超高速邮件发送失败: task_1754168406897_0 (2.29秒)
2025-08-03 05:00:24 | ERROR    | src.core.email_sending_scheduler:send_email_with_browser:207 | 📈 发送统计: 成功率 0.0%, 总计 1 封
2025-08-03 05:00:24 | ERROR    | src.core.email_sending_scheduler:worker_thread_func:312 | ❌ 任务最终失败: task_1754168406897_0
2025-08-03 05:26:04 | ERROR    | src.core.sina_ultra_fast_sender_final:_send_with_elements:609 | ❌ 未找到收件人字段
2025-08-03 05:26:04 | ERROR    | src.core.sina_ultra_fast_sender_final:send_email_ultra_fast:410 | ❌ 所有发送策略都失败了 (16.15秒)
2025-08-03 05:26:04 | ERROR    | src.core.email_sending_scheduler:send_email_with_browser:203 | ❌ 超高速邮件发送失败: task_1754169948035_1 (16.18秒)
2025-08-03 05:26:04 | ERROR    | src.core.email_sending_scheduler:send_email_with_browser:207 | 📈 发送统计: 成功率 0.0%, 总计 1 封
2025-08-03 05:26:20 | ERROR    | src.core.sina_ultra_fast_sender_final:_send_with_elements:609 | ❌ 未找到收件人字段
2025-08-03 05:26:20 | ERROR    | src.core.sina_ultra_fast_sender_final:send_email_ultra_fast:410 | ❌ 所有发送策略都失败了 (16.00秒)
2025-08-03 05:26:20 | ERROR    | src.core.email_sending_scheduler:send_email_with_browser:203 | ❌ 超高速邮件发送失败: task_1754169948035_1 (16.04秒)
2025-08-03 05:26:20 | ERROR    | src.core.email_sending_scheduler:send_email_with_browser:207 | 📈 发送统计: 成功率 0.0%, 总计 1 封
2025-08-03 05:26:36 | ERROR    | src.core.sina_ultra_fast_sender_final:_send_with_elements:609 | ❌ 未找到收件人字段
2025-08-03 05:26:36 | ERROR    | src.core.sina_ultra_fast_sender_final:send_email_ultra_fast:410 | ❌ 所有发送策略都失败了 (16.05秒)
2025-08-03 05:26:36 | ERROR    | src.core.email_sending_scheduler:send_email_with_browser:203 | ❌ 超高速邮件发送失败: task_1754169948035_1 (16.10秒)
2025-08-03 05:26:36 | ERROR    | src.core.email_sending_scheduler:send_email_with_browser:207 | 📈 发送统计: 成功率 0.0%, 总计 1 封
2025-08-03 05:26:36 | ERROR    | src.core.email_sending_scheduler:worker_thread_func:312 | ❌ 任务最终失败: task_1754169948035_1
2025-08-03 17:09:57 | ERROR    | src.models.database:get_cursor:57 | 数据库操作失败: no such column: task_id
2025-08-03 17:09:57 | ERROR    | src.models.database:execute_update:213 | 更新执行失败: CREATE INDEX IF NOT EXISTS idx_send_records_task_id ON send_records(task_id), 参数: (), 错误: no such column: task_id
2025-08-03 17:09:57 | ERROR    | src.models.send_record:ensure_table_exists:159 | 创建发送记录表失败: no such column: task_id
2025-08-03 17:09:57 | ERROR    | src.gui.multi_browser_sender_widget:create_statistics_tab:401 | 创建发送统计选项卡失败: no such column: task_id
