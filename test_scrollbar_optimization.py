#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滚动条优化测试
测试左侧滚动条遮挡问题修复和右侧空间分配优化
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer
from src.gui.multi_browser_sender_widget import MultiBrowserSenderWidget


class ScrollbarOptimizationTestWindow(QMainWindow):
    """滚动条优化测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_test_data()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("📏 滚动条优化测试 - 修复遮挡问题")
        self.setGeometry(50, 50, 1600, 1000)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 添加测试说明
        info_layout = QHBoxLayout()
        
        info_label = QLabel("""
📏 滚动条优化测试

🎯 本次优化重点：

🔧 左侧滚动条优化：
✅ 滚动条宽度：12px → 8px (更细)
✅ 内容右边距：15px → 20px (更多空间)
✅ 滚动条样式：更美观的圆角设计
✅ 边距优化：避免滚动条遮挡内容

📐 右侧空间分配优化：
✅ 邮件内容编辑：400px → 250-350px (更合理)
✅ 状态表格：无限制 → 120-200px (控制高度)
✅ 日志区域：无限制 → 120-200px (控制高度)
✅ 整体空间：更平衡的分配

🔍 请重点检查：
1. 左侧滚动条是否不再遮挡设置内容
2. 右侧邮件发送区域是否空间更合理
3. 所有功能是否仍然完整可用
4. 滚动操作是否流畅
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f0f8ff;
                border: 2px solid #4169e1;
                border-radius: 8px;
                padding: 10px;
                font-size: 10pt;
                color: #191970;
            }
        """)
        info_layout.addWidget(info_label)
        
        # 添加测试按钮
        test_buttons_layout = QVBoxLayout()
        
        self.test_left_scroll_btn = QPushButton("⬅️ 测试左侧滚动")
        self.test_left_scroll_btn.clicked.connect(self.test_left_scrollbar)
        test_buttons_layout.addWidget(self.test_left_scroll_btn)
        
        self.test_right_space_btn = QPushButton("➡️ 测试右侧空间")
        self.test_right_space_btn.clicked.connect(self.test_right_space)
        test_buttons_layout.addWidget(self.test_right_space_btn)
        
        self.fill_content_btn = QPushButton("📝 填充测试内容")
        self.fill_content_btn.clicked.connect(self.fill_test_content)
        test_buttons_layout.addWidget(self.fill_content_btn)
        
        self.check_visibility_btn = QPushButton("👁️ 检查内容可见性")
        self.check_visibility_btn.clicked.connect(self.check_content_visibility)
        test_buttons_layout.addWidget(self.check_visibility_btn)
        
        test_buttons_layout.addStretch()
        info_layout.addLayout(test_buttons_layout)
        
        layout.addLayout(info_layout)
        
        # 创建多浏览器发送组件
        self.sender_widget = MultiBrowserSenderWidget()
        layout.addWidget(self.sender_widget)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
        """)
    
    def setup_test_data(self):
        """设置测试数据"""
        # 延迟执行，确保界面完全加载
        QTimer.singleShot(1000, self.fill_test_content)
    
    def test_left_scrollbar(self):
        """测试左侧滚动条"""
        try:
            print("\n⬅️ 左侧滚动条测试：")
            print("   1. 请检查左侧滚动条是否不再遮挡设置内容")
            print("   2. 滚动条宽度是否变细（8px）")
            print("   3. 内容右边距是否足够（20px）")
            print("   4. 滚动条样式是否美观")
            print("   5. 请尝试滚动左侧面板")
            print("✅ 左侧滚动条测试指引完成")
        except Exception as e:
            print(f"❌ 左侧滚动条测试失败: {e}")
    
    def test_right_space(self):
        """测试右侧空间分配"""
        try:
            print("\n➡️ 右侧空间分配测试：")
            print("   1. 请检查邮件内容编辑框高度是否合理（250-350px）")
            print("   2. 状态表格高度是否控制在合理范围（120-200px）")
            print("   3. 日志区域高度是否控制在合理范围（120-200px）")
            print("   4. 整体空间分配是否更平衡")
            print("   5. 请切换到'手动输入邮件发送'选项卡查看")
            print("✅ 右侧空间分配测试指引完成")
        except Exception as e:
            print(f"❌ 右侧空间分配测试失败: {e}")
    
    def fill_test_content(self):
        """填充测试内容"""
        try:
            # 切换到邮件发送选项卡
            if hasattr(self.sender_widget, 'task_tab_widget'):
                tab_widget = self.sender_widget.task_tab_widget
                for i in range(tab_widget.count()):
                    tab_text = tab_widget.tabText(i)
                    if "手动输入" in tab_text or "邮件发送" in tab_text:
                        tab_widget.setCurrentIndex(i)
                        break
            
            # 填充收件人
            if hasattr(self.sender_widget, 'to_email_edit'):
                self.sender_widget.to_email_edit.setText("<EMAIL>;<EMAIL>")
                print("✅ 收件人数据填充完成")
            
            # 填充主题
            if hasattr(self.sender_widget, 'subject_edit'):
                self.sender_widget.subject_edit.setText("【滚动条优化测试】空间分配优化验证")
                print("✅ 邮件主题填充完成")
            
            # 填充适中长度的内容
            if hasattr(self.sender_widget, 'content_edit'):
                moderate_content = """亲爱的用户，

这是一封用于测试滚动条优化的邮件。

## 本次优化内容

### 左侧滚动条优化
- 滚动条宽度从12px减少到8px
- 内容右边距从15px增加到20px
- 优化滚动条样式，更美观
- 确保滚动条不遮挡设置内容

### 右侧空间分配优化
- 邮件内容编辑框：250-350px高度范围
- 状态表格：120-200px高度范围
- 日志区域：120-200px高度范围
- 整体空间分配更加平衡

这段内容的长度经过精心设计，既能测试编辑框的显示效果，又不会占用过多空间。

请检查：
1. 内容是否完整显示
2. 编辑框高度是否合适
3. 滚动是否流畅
4. 空间分配是否合理

谢谢您的测试！

此致
敬礼！

滚动条优化测试团队"""
                
                self.sender_widget.content_edit.setPlainText(moderate_content)
                print("✅ 邮件内容填充完成")
            
            # 设置一些配置项
            if hasattr(self.sender_widget, 'max_browsers_spin'):
                self.sender_widget.max_browsers_spin.setValue(3)
            
            if hasattr(self.sender_widget, 'send_interval_spin'):
                self.sender_widget.send_interval_spin.setValue(2.5)
            
            print("✅ 所有测试内容填充完成")
            
        except Exception as e:
            print(f"❌ 填充测试内容失败: {e}")
    
    def check_content_visibility(self):
        """检查内容可见性"""
        try:
            print("\n👁️ 内容可见性检查：")
            
            # 检查左侧组件
            print("   左侧面板组件：")
            left_components = [
                ('快速配置', 'max_browsers_spin'),
                ('发送间隔', 'send_interval_spin'),
                ('账号表格', 'account_table'),
            ]
            
            for name, attr in left_components:
                if hasattr(self.sender_widget, attr):
                    component = getattr(self.sender_widget, attr)
                    if component.isVisible():
                        print(f"     ✅ {name}: 可见")
                    else:
                        print(f"     ❌ {name}: 不可见")
                else:
                    print(f"     ❓ {name}: 未找到")
            
            # 检查右侧组件
            print("   右侧面板组件：")
            right_components = [
                ('收件人输入框', 'to_email_edit'),
                ('邮件主题输入框', 'subject_edit'),
                ('邮件内容编辑框', 'content_edit'),
                ('状态表格', 'status_table'),
                ('日志区域', 'log_text')
            ]
            
            for name, attr in right_components:
                if hasattr(self.sender_widget, attr):
                    component = getattr(self.sender_widget, attr)
                    if component.isVisible():
                        size = component.size()
                        print(f"     ✅ {name}: 可见 ({size.width()}×{size.height()})")
                    else:
                        print(f"     ❌ {name}: 不可见")
                else:
                    print(f"     ❓ {name}: 未找到")
            
            print("✅ 内容可见性检查完成")
            
        except Exception as e:
            print(f"❌ 检查内容可见性失败: {e}")


def main():
    """主函数"""
    print("📏 启动滚动条优化测试...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("滚动条优化测试")
    app.setApplicationVersion("2.0")
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    try:
        # 创建测试窗口
        window = ScrollbarOptimizationTestWindow()
        window.show()
        
        print("✅ 滚动条优化测试启动成功！")
        print("\n🎯 重点测试项目：")
        print("   1. 左侧滚动条是否不再遮挡内容")
        print("   2. 左侧滚动条是否变细更美观")
        print("   3. 右侧邮件编辑区域是否空间合理")
        print("   4. 整体空间分配是否更平衡")
        print("   5. 所有功能是否仍然完整可用")
        print("\n📏 优化详情：")
        print("   左侧滚动条：12px → 8px，边距15px → 20px")
        print("   邮件编辑框：400px无限制 → 250-350px")
        print("   状态表格：无限制 → 120-200px")
        print("   日志区域：无限制 → 120-200px")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
