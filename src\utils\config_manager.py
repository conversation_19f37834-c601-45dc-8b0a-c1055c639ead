#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责加载、保存和管理应用程序配置
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from src.utils.logger import get_logger

logger = get_logger("ConfigManager")


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认路径
        """
        if config_file is None:
            project_root = Path(__file__).parent.parent.parent
            self.config_file = project_root / "config" / "app_config.yaml"
        else:
            self.config_file = Path(config_file)
        
        self.config = {}
        self._default_config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "app": {
                "name": "新浪邮箱自动化程序",
                "version": "1.0.0",
                "author": "AI Assistant"
            },
            "database": {
                "type": "sqlite",
                "path": "data/sina_email_automation.db"
            },
            "browser": {
                "default_browser": "chrome",
                "headless": False,
                "window_size": [1920, 1080],
                "page_load_timeout": 30,
                "implicit_wait": 10
            },
            "email": {
                "send_interval_min": 5,
                "send_interval_max": 15,
                "max_retry_count": 3,
                "batch_size": 10
            },
            "logging": {
                "level": "INFO",
                "max_file_size": "10MB",
                "backup_count": 5
            },
            "gui": {
                "window_size": [1200, 800],
                "theme": "default"
            }
        }
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f) or {}
                logger.info(f"配置文件加载成功: {self.config_file}")
            else:
                logger.warning(f"配置文件不存在，使用默认配置: {self.config_file}")
                self.config = self._default_config.copy()
                self.save_config()
            
            # 合并默认配置，确保所有必要的配置项都存在
            self.config = self._merge_config(self._default_config, self.config)
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            logger.info("使用默认配置")
            self.config = self._default_config.copy()
        
        return self.config
    
    def save_config(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            是否保存成功
        """
        try:
            # 确保配置目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(
                    self.config, 
                    f, 
                    default_flow_style=False, 
                    allow_unicode=True,
                    indent=2
                )
            
            logger.info(f"配置文件保存成功: {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 "database.path"
            default: 默认值
        
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 创建嵌套字典结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        logger.debug(f"配置已更新: {key} = {value}")
    
    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并配置字典
        
        Args:
            default: 默认配置
            user: 用户配置
        
        Returns:
            合并后的配置
        """
        result = default.copy()
        
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self.config = self._default_config.copy()
        logger.info("配置已重置为默认值")
    
    def get_database_path(self) -> Path:
        """获取数据库文件路径"""
        db_path = self.get("database.path", "data/sina_email_automation.db")
        if not os.path.isabs(db_path):
            project_root = Path(__file__).parent.parent.parent
            db_path = project_root / db_path
        return Path(db_path)
    
    def get_logs_dir(self) -> Path:
        """获取日志目录路径"""
        project_root = Path(__file__).parent.parent.parent
        return project_root / "logs"
