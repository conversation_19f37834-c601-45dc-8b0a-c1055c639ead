#!/usr/bin/env python3
"""
高速浏览器邮件发送器
专门优化的超高速邮件发送功能
"""

import time
import json
import threading
from typing import Dict, List, Optional, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class HighSpeedEmailSender:
    """高速邮件发送器"""
    
    def __init__(self, driver: webdriver.Chrome):
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        self.is_ready = False
        self.current_url = ""
        
    def prepare_for_sending(self) -> bool:
        """准备发送邮件 - 智能导航到写邮件页面"""
        try:
            logger.info("🚀 准备高速邮件发送...")

            # 首先检查当前页面是否已经是写邮件页面
            if self._check_compose_page():
                self.current_url = self.driver.current_url
                self.is_ready = True
                logger.info("✅ 当前页面已是写邮件页面")
                return True

            # 方法1: 尝试从当前页面查找写邮件链接
            if self._navigate_from_current_page():
                return True

            # 方法2: 访问邮箱主页然后导航
            logger.info("🔄 访问邮箱主页...")
            try:
                self.driver.get("https://mail.sina.com.cn")
                time.sleep(3)

                if self._navigate_from_current_page():
                    return True

            except Exception as e:
                logger.debug(f"主页访问失败: {e}")

            # 方法3: 直接访问写邮件页面 (最后尝试)
            compose_urls = [
                "https://mail.sina.com.cn/classic/compose.php",
                "https://mail.sina.com.cn/compose.php"
            ]

            for url in compose_urls:
                try:
                    logger.info(f"🔗 直接访问: {url}")
                    self.driver.get(url)
                    time.sleep(3)

                    if self._check_compose_page():
                        self.current_url = url
                        self.is_ready = True
                        logger.info(f"✅ 写邮件页面准备完成: {url}")
                        return True

                except Exception as e:
                    logger.debug(f"⚠️ URL {url} 访问失败: {e}")
                    continue

            logger.error("❌ 所有导航方法都失败了")
            return False

        except Exception as e:
            logger.error(f"❌ 准备发送失败: {e}")
            return False
    
    def _check_compose_page(self) -> bool:
        """检查是否在写邮件页面"""
        try:
            # 检查页面元素
            indicators = [
                "//input[@name='to']",
                "//input[@name='subject']", 
                "//textarea[@name='content']",
                "//input[@value='发送']",
                "//input[@type='submit']"
            ]
            
            found_count = 0
            for xpath in indicators:
                try:
                    element = self.driver.find_element(By.XPATH, xpath)
                    if element:
                        found_count += 1
                except:
                    pass
            
            return found_count >= 3
            
        except Exception as e:
            logger.debug(f"检查写邮件页面失败: {e}")
            return False
    
    def _navigate_from_current_page(self) -> bool:
        """从当前页面导航到写邮件页面"""
        try:
            # 查找写邮件链接
            compose_selectors = [
                "//a[contains(text(), '写邮件')]",
                "//a[contains(text(), '写信')]",
                "//a[contains(text(), 'compose')]",
                "//a[contains(@href, 'compose')]",
                "//input[@value='写邮件']",
                "//button[contains(text(), '写邮件')]",
                "//a[contains(@onclick, 'compose')]"
            ]

            for selector in compose_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element and element.is_displayed():
                        logger.info(f"🔗 找到写邮件链接: {selector}")
                        element.click()
                        time.sleep(3)

                        if self._check_compose_page():
                            self.current_url = self.driver.current_url
                            self.is_ready = True
                            logger.info("✅ 成功导航到写邮件页面")
                            return True

                except Exception as e:
                    logger.debug(f"链接点击失败: {selector} - {e}")
                    continue

            # 尝试JavaScript导航
            try:
                logger.info("🔄 尝试JavaScript导航...")
                js_navigate = """
                var composeLink = document.querySelector('a[href*="compose"]') ||
                                 document.querySelector('a[onclick*="compose"]') ||
                                 document.querySelector('a:contains("写邮件")');
                if (composeLink) {
                    composeLink.click();
                    return true;
                }
                return false;
                """

                result = self.driver.execute_script(js_navigate)
                if result:
                    time.sleep(3)
                    if self._check_compose_page():
                        self.current_url = self.driver.current_url
                        self.is_ready = True
                        logger.info("✅ JavaScript导航成功")
                        return True

            except Exception as e:
                logger.debug(f"JavaScript导航失败: {e}")

            return False

        except Exception as e:
            logger.error(f"从当前页面导航失败: {e}")
            return False

    def _navigate_from_main_page(self) -> bool:
        """从主页导航到写邮件页面"""
        try:
            # 访问邮箱主页
            main_urls = [
                "https://mail.sina.com.cn/classic/",
                "https://mail.sina.com.cn/",
                "https://m0.mail.sina.com.cn/classic/",
            ]
            
            for main_url in main_urls:
                try:
                    self.driver.get(main_url)
                    time.sleep(3)
                    
                    # 查找写邮件链接
                    compose_links = [
                        "//a[contains(text(), '写邮件')]",
                        "//a[contains(text(), '写信')]", 
                        "//a[contains(text(), 'compose')]",
                        "//a[contains(@href, 'compose')]",
                        "//input[@value='写邮件']",
                        "//button[contains(text(), '写邮件')]"
                    ]
                    
                    for link_xpath in compose_links:
                        try:
                            link = self.wait.until(EC.element_to_be_clickable((By.XPATH, link_xpath)))
                            link.click()
                            time.sleep(2)
                            
                            if self._check_compose_page():
                                self.current_url = self.driver.current_url
                                self.is_ready = True
                                logger.info(f"✅ 成功导航到写邮件页面")
                                return True
                                
                        except Exception as e:
                            logger.debug(f"点击链接失败: {link_xpath} - {e}")
                            continue
                    
                except Exception as e:
                    logger.debug(f"主页 {main_url} 导航失败: {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"从主页导航失败: {e}")
            return False
    
    def send_email_ultra_fast(self, to_email: str, subject: str, content: str, 
                             content_type: str = "text/plain") -> bool:
        """超高速发送邮件"""
        try:
            if not self.is_ready:
                logger.warning("⚠️ 发送器未准备就绪，尝试重新准备...")
                if not self.prepare_for_sending():
                    return False
            
            logger.info(f"⚡ 超高速发送邮件: {to_email}")
            start_time = time.time()
            
            # 策略1: 直接JavaScript注入 (最快)
            if self._send_with_javascript(to_email, subject, content):
                elapsed = time.time() - start_time
                logger.info(f"✅ JavaScript发送成功 ({elapsed:.2f}秒)")
                return True
            
            # 策略2: 快速表单填写
            if self._send_with_fast_form(to_email, subject, content):
                elapsed = time.time() - start_time
                logger.info(f"✅ 快速表单发送成功 ({elapsed:.2f}秒)")
                return True
            
            # 策略3: 标准发送
            if self._send_with_standard_method(to_email, subject, content):
                elapsed = time.time() - start_time
                logger.info(f"✅ 标准发送成功 ({elapsed:.2f}秒)")
                return True
            
            logger.error("❌ 所有发送策略都失败了")
            return False
            
        except Exception as e:
            logger.error(f"❌ 超高速发送失败: {e}")
            return False
    
    def _send_with_javascript(self, to_email: str, subject: str, content: str) -> bool:
        """使用JavaScript直接发送 (最快方法)"""
        try:
            logger.info("⚡ 尝试JavaScript直接发送...")

            # 转义特殊字符
            safe_email = to_email.replace("'", "\\'").replace('"', '\\"')
            safe_subject = subject.replace("'", "\\'").replace('"', '\\"')
            safe_content = content.replace("'", "\\'").replace('"', '\\"').replace('\n', '\\n')

            # 分步执行JavaScript
            # 步骤1: 填写收件人
            js_fill_to = f"""
            var toField = document.querySelector('input[name="to"]') ||
                         document.querySelector('input[id*="to"]') ||
                         document.querySelector('input[placeholder*="收件人"]');
            if (toField) {{
                toField.value = '{safe_email}';
                toField.dispatchEvent(new Event('input', {{bubbles: true}}));
                toField.dispatchEvent(new Event('change', {{bubbles: true}}));
                return true;
            }}
            return false;
            """

            to_result = self.driver.execute_script(js_fill_to)
            if not to_result:
                logger.warning("⚠️ JavaScript填写收件人失败")
                return False

            # 步骤2: 填写主题
            js_fill_subject = f"""
            var subjectField = document.querySelector('input[name="subject"]') ||
                              document.querySelector('input[id*="subject"]') ||
                              document.querySelector('input[placeholder*="主题"]');
            if (subjectField) {{
                subjectField.value = '{safe_subject}';
                subjectField.dispatchEvent(new Event('input', {{bubbles: true}}));
                subjectField.dispatchEvent(new Event('change', {{bubbles: true}}));
                return true;
            }}
            return false;
            """

            subject_result = self.driver.execute_script(js_fill_subject)
            logger.debug(f"主题填写结果: {subject_result}")

            # 步骤3: 填写内容
            js_fill_content = f"""
            var contentField = document.querySelector('textarea[name="content"]') ||
                              document.querySelector('textarea[id*="content"]') ||
                              document.querySelector('div[contenteditable="true"]');

            if (contentField) {{
                if (contentField.tagName === 'TEXTAREA') {{
                    contentField.value = '{safe_content}';
                    contentField.dispatchEvent(new Event('input', {{bubbles: true}}));
                }} else if (contentField.contentEditable === 'true') {{
                    contentField.innerHTML = '{safe_content}';
                    contentField.dispatchEvent(new Event('input', {{bubbles: true}}));
                }}
                contentField.dispatchEvent(new Event('change', {{bubbles: true}}));
                return true;
            }}
            return false;
            """

            content_result = self.driver.execute_script(js_fill_content)
            logger.debug(f"内容填写结果: {content_result}")

            # 等待一下
            time.sleep(0.5)

            # 步骤4: 发送邮件
            js_send = """
            var sendButton = document.querySelector('input[value*="发送"]') ||
                           document.querySelector('input[type="submit"]') ||
                           document.querySelector('button[type="submit"]') ||
                           document.querySelector('button[onclick*="send"]') ||
                           document.querySelector('a[onclick*="send"]');

            if (sendButton) {
                sendButton.click();
                return true;
            }
            return false;
            """

            send_result = self.driver.execute_script(js_send)

            if send_result:
                logger.info("✅ JavaScript发送按钮点击成功")
                time.sleep(3)  # 等待发送完成
                return self._check_send_success()
            else:
                logger.warning("⚠️ JavaScript未找到发送按钮")
                return False

        except Exception as e:
            logger.error(f"❌ JavaScript发送失败: {e}")
            return False
    
    def _send_with_fast_form(self, to_email: str, subject: str, content: str) -> bool:
        """快速表单填写发送"""
        try:
            logger.info("⚡ 尝试快速表单发送...")
            
            # 快速查找并填写表单元素
            selectors = {
                'to': [
                    'input[name="to"]',
                    'input[id*="to"]', 
                    'input[placeholder*="收件人"]',
                    'input[class*="to"]'
                ],
                'subject': [
                    'input[name="subject"]',
                    'input[id*="subject"]',
                    'input[placeholder*="主题"]',
                    'input[class*="subject"]'
                ],
                'content': [
                    'textarea[name="content"]',
                    'textarea[id*="content"]',
                    'div[contenteditable="true"]',
                    'textarea[class*="content"]'
                ],
                'send': [
                    'input[value*="发送"]',
                    'input[type="submit"]',
                    'button[type="submit"]',
                    'button[onclick*="send"]',
                    'a[onclick*="send"]'
                ]
            }
            
            # 填写收件人
            to_field = self._find_element_fast(selectors['to'])
            if to_field:
                to_field.clear()
                to_field.send_keys(to_email)
            else:
                logger.warning("⚠️ 未找到收件人字段")
                return False
            
            # 填写主题
            subject_field = self._find_element_fast(selectors['subject'])
            if subject_field:
                subject_field.clear()
                subject_field.send_keys(subject)
            else:
                logger.warning("⚠️ 未找到主题字段")
            
            # 填写内容
            content_field = self._find_element_fast(selectors['content'])
            if content_field:
                if content_field.tag_name == 'textarea':
                    content_field.clear()
                    content_field.send_keys(content)
                else:
                    # 可能是富文本编辑器
                    self.driver.execute_script("arguments[0].innerHTML = arguments[1];", content_field, content)
            else:
                logger.warning("⚠️ 未找到内容字段")
            
            # 发送邮件
            send_button = self._find_element_fast(selectors['send'])
            if send_button:
                send_button.click()
                time.sleep(2)
                return self._check_send_success()
            else:
                logger.warning("⚠️ 未找到发送按钮")
                return False
            
        except Exception as e:
            logger.debug(f"快速表单发送失败: {e}")
            return False
    
    def _find_element_fast(self, selectors: List[str]):
        """快速查找元素"""
        for selector in selectors:
            try:
                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                if element and element.is_displayed():
                    return element
            except:
                continue
        return None
    
    def _send_with_standard_method(self, to_email: str, subject: str, content: str) -> bool:
        """标准发送方法"""
        try:
            logger.info("📧 使用标准方法发送...")
            
            # 等待页面加载
            time.sleep(1)
            
            # 填写收件人
            try:
                to_field = self.wait.until(EC.presence_of_element_located((By.NAME, "to")))
                to_field.clear()
                to_field.send_keys(to_email)
            except:
                # 尝试其他选择器
                to_field = self.driver.find_element(By.XPATH, "//input[contains(@placeholder, '收件人') or contains(@id, 'to')]")
                to_field.clear()
                to_field.send_keys(to_email)
            
            # 填写主题
            try:
                subject_field = self.driver.find_element(By.NAME, "subject")
                subject_field.clear()
                subject_field.send_keys(subject)
            except:
                subject_field = self.driver.find_element(By.XPATH, "//input[contains(@placeholder, '主题') or contains(@id, 'subject')]")
                subject_field.clear()
                subject_field.send_keys(subject)
            
            # 填写内容
            try:
                content_field = self.driver.find_element(By.NAME, "content")
                content_field.clear()
                content_field.send_keys(content)
            except:
                # 尝试富文本编辑器
                try:
                    iframe = self.driver.find_element(By.TAG_NAME, "iframe")
                    self.driver.switch_to.frame(iframe)
                    content_field = self.driver.find_element(By.TAG_NAME, "body")
                    content_field.clear()
                    content_field.send_keys(content)
                    self.driver.switch_to.default_content()
                except:
                    content_field = self.driver.find_element(By.XPATH, "//textarea[contains(@id, 'content') or contains(@name, 'content')]")
                    content_field.clear()
                    content_field.send_keys(content)
            
            # 发送邮件
            send_button = self.driver.find_element(By.XPATH, "//input[@type='submit' and contains(@value, '发送')] | //button[contains(text(), '发送')]")
            send_button.click()
            
            time.sleep(3)
            return self._check_send_success()
            
        except Exception as e:
            logger.error(f"标准发送方法失败: {e}")
            return False
    
    def _check_send_success(self) -> bool:
        """检查发送是否成功"""
        try:
            # 等待页面响应
            time.sleep(2)
            
            current_url = self.driver.current_url.lower()
            page_source = self.driver.page_source.lower()
            
            # 成功指标
            success_indicators = [
                '发送成功', '已发送', 'sent successfully', 'message sent',
                '邮件已发送', '发送完成', 'send success'
            ]
            
            # 失败指标
            error_indicators = [
                '发送失败', 'send failed', 'error', '错误', 'failed'
            ]
            
            # 检查成功指标
            has_success = any(indicator in page_source for indicator in success_indicators)
            has_error = any(indicator in page_source for indicator in error_indicators)
            
            # URL变化也可能表示成功
            url_success = any(keyword in current_url for keyword in ['sent', 'success', 'complete'])
            
            if has_success or (url_success and not has_error):
                logger.info("✅ 邮件发送成功确认")
                return True
            elif has_error:
                logger.warning("⚠️ 检测到发送错误")
                return False
            else:
                # 如果没有明确指标，假设成功
                logger.info("🤔 无明确指标，假设发送成功")
                return True
                
        except Exception as e:
            logger.error(f"检查发送结果失败: {e}")
            return False
    
    def reset_for_next_email(self) -> bool:
        """为下一封邮件重置状态"""
        try:
            # 快速返回写邮件页面
            if self.current_url:
                self.driver.get(self.current_url)
                time.sleep(1)
                return self._check_compose_page()
            else:
                return self.prepare_for_sending()
                
        except Exception as e:
            logger.error(f"重置状态失败: {e}")
            return False
