#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件监控界面模块
提供文件监控的管理界面，包括路径配置、监控状态、提取结果等
"""

import os
from pathlib import Path
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QLabel, QMessageBox, QFileDialog, QGroupBox,
    QTextEdit, QHeaderView, QProgressBar, QCheckBox, QSpinBox, QComboBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from typing import List, Dict, Any
from src.core.file_monitor import FileMonitor
from src.models.database import DatabaseManager
from src.utils.logger import get_logger

logger = get_logger("FileMonitorWidget")


class FileMonitorWidget(QWidget):
    """文件监控主界面"""
    
    # 信号定义
    new_emails_detected = pyqtSignal(list)  # 检测到新邮箱信号
    
    def __init__(self, config: Dict[str, Any], db_manager: DatabaseManager):
        """
        初始化文件监控界面
        
        Args:
            config: 配置字典
            db_manager: 数据库管理器
        """
        super().__init__()
        self.config = config
        self.db_manager = db_manager
        
        # 创建文件监控器
        self.file_monitor = FileMonitor(config, db_manager)
        self.file_monitor.set_new_emails_callback(self._on_new_emails)
        self.file_monitor.set_status_callback(self._on_status_change)
        
        # 状态变量
        self.is_monitoring = False
        self.total_extracted = 0
        
        self.init_ui()
        self.setup_timer()
        
        logger.info("文件监控界面初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 监控配置区域
        config_group = self.create_config_section()
        layout.addWidget(config_group)
        
        # 监控路径区域
        paths_group = self.create_paths_section()
        layout.addWidget(paths_group)
        
        # 监控状态区域
        status_group = self.create_status_section()
        layout.addWidget(status_group)
        
        # 提取结果区域
        results_group = self.create_results_section()
        layout.addWidget(results_group)
    
    def create_config_section(self) -> QGroupBox:
        """创建监控配置区域"""
        group = QGroupBox("监控配置")
        layout = QVBoxLayout(group)
        
        # 第一行：启用监控和检查间隔
        row1_layout = QHBoxLayout()
        
        self.enable_checkbox = QCheckBox("启用文件监控")
        self.enable_checkbox.setChecked(self.config.get('file_monitor.enabled', True))
        self.enable_checkbox.stateChanged.connect(self._on_enable_changed)
        row1_layout.addWidget(self.enable_checkbox)
        
        row1_layout.addWidget(QLabel("检查间隔:"))
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(1, 60)
        self.interval_spin.setValue(self.config.get('file_monitor.check_interval', 5))
        self.interval_spin.setSuffix(" 秒")
        row1_layout.addWidget(self.interval_spin)
        
        row1_layout.addStretch()
        layout.addLayout(row1_layout)
        
        # 第二行：文件类型和编码
        row2_layout = QHBoxLayout()
        
        row2_layout.addWidget(QLabel("支持文件类型:"))
        self.extensions_edit = QLineEdit()
        extensions = self.config.get('file_monitor.supported_extensions', ['.txt', '.csv'])
        self.extensions_edit.setText(', '.join(extensions))
        self.extensions_edit.setPlaceholderText("例如: .txt, .csv")
        row2_layout.addWidget(self.extensions_edit)
        
        row2_layout.addWidget(QLabel("文件编码:"))
        self.encoding_combo = QComboBox()
        self.encoding_combo.addItems(['utf-8', 'gbk', 'gb2312', 'utf-16'])
        encoding = self.config.get('file_monitor.encoding', 'utf-8')
        self.encoding_combo.setCurrentText(encoding)
        row2_layout.addWidget(self.encoding_combo)
        
        layout.addLayout(row2_layout)
        
        # 第三行：备份选项
        row3_layout = QHBoxLayout()
        
        self.backup_checkbox = QCheckBox("备份已处理的文件")
        self.backup_checkbox.setChecked(self.config.get('file_monitor.backup_processed_files', True))
        row3_layout.addWidget(self.backup_checkbox)
        
        row3_layout.addStretch()
        layout.addLayout(row3_layout)
        
        return group
    
    def create_paths_section(self) -> QGroupBox:
        """创建监控路径区域"""
        group = QGroupBox("监控路径")
        layout = QVBoxLayout(group)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.add_path_btn = QPushButton("添加路径")
        self.add_path_btn.clicked.connect(self.add_monitor_path)
        toolbar_layout.addWidget(self.add_path_btn)
        
        self.remove_path_btn = QPushButton("移除路径")
        self.remove_path_btn.clicked.connect(self.remove_monitor_path)
        toolbar_layout.addWidget(self.remove_path_btn)
        
        toolbar_layout.addStretch()
        
        self.start_btn = QPushButton("开始监控")
        self.start_btn.clicked.connect(self.start_monitoring)
        toolbar_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止监控")
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.stop_btn.setEnabled(False)
        toolbar_layout.addWidget(self.stop_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 路径列表
        self.paths_table = QTableWidget()
        self.setup_paths_table()
        layout.addWidget(self.paths_table)
        
        return group
    
    def setup_paths_table(self):
        """设置路径表格"""
        headers = ["监控路径", "状态", "文件数量"]
        self.paths_table.setColumnCount(len(headers))
        self.paths_table.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.paths_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.paths_table.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.paths_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 路径
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 状态
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 文件数量
    
    def create_status_section(self) -> QGroupBox:
        """创建监控状态区域"""
        group = QGroupBox("监控状态")
        layout = QVBoxLayout(group)
        
        # 状态信息
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("状态: 未启动")
        self.status_label.setStyleSheet("font-weight: bold;")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.extracted_label = QLabel("已提取邮箱: 0 个")
        status_layout.addWidget(self.extracted_label)
        
        layout.addLayout(status_layout)
        
        # 进度条（可选）
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return group
    
    def create_results_section(self) -> QGroupBox:
        """创建提取结果区域"""
        group = QGroupBox("提取结果")
        layout = QVBoxLayout(group)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.clear_results_btn = QPushButton("清空结果")
        self.clear_results_btn.clicked.connect(self.clear_results)
        toolbar_layout.addWidget(self.clear_results_btn)
        
        self.export_results_btn = QPushButton("导出邮箱")
        self.export_results_btn.clicked.connect(self.export_results)
        toolbar_layout.addWidget(self.export_results_btn)
        
        toolbar_layout.addStretch()
        
        self.auto_send_checkbox = QCheckBox("自动发送邮件")
        self.auto_send_checkbox.setToolTip("检测到新邮箱时自动添加到发送队列")
        toolbar_layout.addWidget(self.auto_send_checkbox)
        
        layout.addLayout(toolbar_layout)
        
        # 结果显示
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setMaximumHeight(200)
        self.results_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.results_text)
        
        return group
    
    def setup_timer(self):
        """设置定时器"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # 每秒更新一次
    
    def add_monitor_path(self):
        """添加监控路径"""
        path = QFileDialog.getExistingDirectory(
            self,
            "选择监控文件夹",
            "",
            QFileDialog.ShowDirsOnly
        )
        
        if not path:
            return
        
        if self.file_monitor.add_monitor_path(path):
            self.update_paths_table()
            QMessageBox.information(self, "成功", f"监控路径添加成功:\n{path}")
        else:
            QMessageBox.warning(self, "失败", f"监控路径添加失败:\n{path}")
    
    def remove_monitor_path(self):
        """移除监控路径"""
        current_row = self.paths_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请选择要移除的监控路径")
            return
        
        path_item = self.paths_table.item(current_row, 0)
        if not path_item:
            return
        
        path = path_item.text()
        
        reply = QMessageBox.question(
            self,
            "确认移除",
            f"确定要移除监控路径吗？\n{path}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.file_monitor.remove_monitor_path(path):
                self.update_paths_table()
                QMessageBox.information(self, "成功", "监控路径移除成功")
            else:
                QMessageBox.warning(self, "失败", "监控路径移除失败")
    
    def start_monitoring(self):
        """开始监控"""
        if not self.file_monitor.get_monitor_paths():
            QMessageBox.warning(self, "提示", "请先添加监控路径")
            return
        
        if self.file_monitor.start_monitoring():
            self.is_monitoring = True
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.add_path_btn.setEnabled(False)
            self.remove_path_btn.setEnabled(False)
            
            self.status_label.setText("状态: 监控中")
            self.status_label.setStyleSheet("font-weight: bold; color: green;")
            
            QMessageBox.information(self, "成功", "文件监控已启动")
        else:
            QMessageBox.warning(self, "失败", "文件监控启动失败")
    
    def stop_monitoring(self):
        """停止监控"""
        self.file_monitor.stop_monitoring()
        
        self.is_monitoring = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.add_path_btn.setEnabled(True)
        self.remove_path_btn.setEnabled(True)
        
        self.status_label.setText("状态: 已停止")
        self.status_label.setStyleSheet("font-weight: bold; color: red;")
        
        QMessageBox.information(self, "成功", "文件监控已停止")
    
    def update_paths_table(self):
        """更新路径表格"""
        paths = self.file_monitor.get_monitor_paths()
        self.paths_table.setRowCount(len(paths))
        
        for row, path in enumerate(paths):
            # 路径
            self.paths_table.setItem(row, 0, QTableWidgetItem(path))
            
            # 状态
            status = "监控中" if self.is_monitoring else "未启动"
            status_item = QTableWidgetItem(status)
            if self.is_monitoring:
                status_item.setForeground(QColor("green"))
            else:
                status_item.setForeground(QColor("red"))
            self.paths_table.setItem(row, 1, status_item)
            
            # 文件数量
            try:
                file_count = self._count_files_in_path(path)
                self.paths_table.setItem(row, 2, QTableWidgetItem(str(file_count)))
            except:
                self.paths_table.setItem(row, 2, QTableWidgetItem("0"))
    
    def _count_files_in_path(self, path: str) -> int:
        """统计路径中的文件数量"""
        try:
            extensions = self.config.get('file_monitor.supported_extensions', ['.txt', '.csv'])
            count = 0
            
            for file_path in Path(path).rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in extensions:
                    count += 1
            
            return count
        except:
            return 0
    
    def update_display(self):
        """更新显示信息"""
        # 更新路径表格
        self.update_paths_table()
        
        # 更新提取计数
        self.extracted_label.setText(f"已提取邮箱: {self.total_extracted} 个")
    
    def clear_results(self):
        """清空结果"""
        reply = QMessageBox.question(
            self,
            "确认清空",
            "确定要清空所有提取结果吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.results_text.clear()
            self.total_extracted = 0
            self.extracted_label.setText("已提取邮箱: 0 个")
    
    def export_results(self):
        """导出结果"""
        content = self.results_text.toPlainText()
        if not content.strip():
            QMessageBox.information(self, "提示", "没有可导出的结果")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存提取结果",
            "extracted_emails.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                QMessageBox.information(self, "成功", f"结果已导出到:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败:\n{str(e)}")
    
    def _on_new_emails(self, emails: List[str]):
        """新邮箱回调"""
        try:
            # 更新结果显示
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            for email in emails:
                self.results_text.append(f"[{timestamp}] {email}")
            
            self.total_extracted += len(emails)
            
            # 发送信号
            self.new_emails_detected.emit(emails)
            
            # 如果启用自动发送，触发发送
            if self.auto_send_checkbox.isChecked():
                # 这里可以连接到邮件发送模块
                logger.info(f"自动发送模式：添加 {len(emails)} 个邮箱到发送队列")
            
            logger.info(f"检测到 {len(emails)} 个新邮箱")
            
        except Exception as e:
            logger.error(f"处理新邮箱回调失败: {e}")
    
    def _on_status_change(self, status: str):
        """状态变化回调"""
        logger.info(f"文件监控状态变化: {status}")
    
    def _on_enable_changed(self, state):
        """启用状态变化"""
        enabled = state == Qt.Checked
        
        # 更新配置
        self.config['file_monitor']['enabled'] = enabled
        
        # 如果禁用且正在监控，停止监控
        if not enabled and self.is_monitoring:
            self.stop_monitoring()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.is_monitoring:
            self.stop_monitoring()
        event.accept()
