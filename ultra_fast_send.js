// 超高速邮件发送器 - 简化版
console.log('⚡ 加载超高速邮件发送器...');

// 高速发送函数
async function ultraFastSend(toEmail, subject, content) {
    console.log(`⚡ 开始超高速发送: ${toEmail}`);
    const startTime = Date.now();
    
    try {
        // 策略1: 直接填写表单
        console.log('📝 填写邮件表单...');
        
        // 填写收件人
        const toField = document.querySelector('input[name="to"]') || 
                       document.querySelector('input[id*="to"]') ||
                       document.querySelector('input[placeholder*="收件人"]');
        
        if (toField) {
            toField.value = toEmail;
            toField.dispatchEvent(new Event('input', {bubbles: true}));
            toField.dispatchEvent(new Event('change', {bubbles: true}));
            console.log('✅ 收件人已填写');
        } else {
            console.log('❌ 未找到收件人字段');
            return false;
        }
        
        // 填写主题
        const subjectField = document.querySelector('input[name="subject"]') ||
                            document.querySelector('input[id*="subject"]') ||
                            document.querySelector('input[placeholder*="主题"]');
        
        if (subjectField) {
            subjectField.value = subject;
            subjectField.dispatchEvent(new Event('input', {bubbles: true}));
            subjectField.dispatchEvent(new Event('change', {bubbles: true}));
            console.log('✅ 主题已填写');
        } else {
            console.log('⚠️ 未找到主题字段');
        }
        
        // 填写内容
        const contentField = document.querySelector('textarea[name="content"]') ||
                            document.querySelector('textarea[id*="content"]') ||
                            document.querySelector('div[contenteditable="true"]');
        
        if (contentField) {
            if (contentField.tagName === 'TEXTAREA') {
                contentField.value = content;
                contentField.dispatchEvent(new Event('input', {bubbles: true}));
            } else {
                contentField.innerHTML = content;
                contentField.dispatchEvent(new Event('input', {bubbles: true}));
            }
            console.log('✅ 内容已填写');
        } else {
            console.log('⚠️ 未找到内容字段');
        }
        
        // 等待一下
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 发送邮件
        console.log('🚀 发送邮件...');
        const sendButton = document.querySelector('input[value*="发送"]') ||
                          document.querySelector('input[type="submit"]') ||
                          document.querySelector('button[type="submit"]') ||
                          document.querySelector('button[onclick*="send"]');
        
        if (sendButton) {
            sendButton.click();
            console.log('✅ 发送按钮已点击');
            
            // 等待发送完成
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 检查发送结果
            const elapsed = (Date.now() - startTime) / 1000;
            const pageText = document.body.innerText.toLowerCase();
            const currentUrl = window.location.href.toLowerCase();
            
            const successIndicators = ['发送成功', '已发送', 'sent successfully', 'message sent'];
            const errorIndicators = ['发送失败', 'send failed', 'error', '错误'];
            
            const hasSuccess = successIndicators.some(indicator => pageText.includes(indicator));
            const hasError = errorIndicators.some(indicator => pageText.includes(indicator));
            const urlSuccess = ['sent', 'success', 'complete'].some(keyword => currentUrl.includes(keyword));
            
            if (hasSuccess || (urlSuccess && !hasError)) {
                console.log(`🎉 邮件发送成功! 耗时: ${elapsed.toFixed(2)}秒`);
                return true;
            } else if (hasError) {
                console.log(`❌ 邮件发送失败! 耗时: ${elapsed.toFixed(2)}秒`);
                return false;
            } else {
                console.log(`🤔 发送状态不明确，假设成功! 耗时: ${elapsed.toFixed(2)}秒`);
                return true;
            }
            
        } else {
            console.log('❌ 未找到发送按钮');
            return false;
        }
        
    } catch (error) {
        const elapsed = (Date.now() - startTime) / 1000;
        console.log(`❌ 发送过程出错: ${error.message} 耗时: ${elapsed.toFixed(2)}秒`);
        return false;
    }
}

// 批量发送函数
async function batchSend(emailList, interval = 2000) {
    console.log(`🚀 开始批量发送 ${emailList.length} 封邮件...`);
    
    let successCount = 0;
    let failCount = 0;
    const startTime = Date.now();
    
    for (let i = 0; i < emailList.length; i++) {
        const { to, subject, content } = emailList[i];
        console.log(`\n📧 发送第 ${i + 1}/${emailList.length} 封邮件: ${to}`);
        
        const success = await ultraFastSend(to, subject, content);
        
        if (success) {
            successCount++;
        } else {
            failCount++;
        }
        
        // 如果不是最后一封邮件，重新加载页面准备下一封
        if (i < emailList.length - 1) {
            console.log('🔄 准备发送下一封邮件...');
            window.location.href = 'https://mail.sina.com.cn/classic/compose.php';
            await new Promise(resolve => setTimeout(resolve, interval));
        }
    }
    
    const totalTime = (Date.now() - startTime) / 1000;
    const successRate = (successCount / emailList.length * 100).toFixed(1);
    const avgTime = (totalTime / emailList.length).toFixed(2);
    const emailsPerMinute = (successCount * 60 / totalTime).toFixed(1);
    
    console.log(`\n🎉 批量发送完成!`);
    console.log(`📊 统计信息:`);
    console.log(`   总邮件数: ${emailList.length}`);
    console.log(`   发送成功: ${successCount}`);
    console.log(`   发送失败: ${failCount}`);
    console.log(`   成功率: ${successRate}%`);
    console.log(`   总耗时: ${totalTime.toFixed(2)}秒`);
    console.log(`   平均速度: ${avgTime}秒/封`);
    console.log(`   发送速率: ${emailsPerMinute}封/分钟`);
    
    return {
        total: emailList.length,
        success: successCount,
        fail: failCount,
        successRate: parseFloat(successRate),
        totalTime: totalTime,
        avgTime: parseFloat(avgTime),
        emailsPerMinute: parseFloat(emailsPerMinute)
    };
}

// 快速测试函数
async function quickTest() {
    console.log('🧪 开始快速测试...');
    
    const testEmails = [
        {
            to: '<EMAIL>',
            subject: '超高速发送测试1',
            content: '这是超高速发送器的测试邮件1，发送时间: ' + new Date().toLocaleString()
        },
        {
            to: '<EMAIL>', 
            subject: '超高速发送测试2',
            content: '这是超高速发送器的测试邮件2，发送时间: ' + new Date().toLocaleString()
        },
        {
            to: '<EMAIL>',
            subject: '超高速发送测试3',
            content: '这是超高速发送器的测试邮件3，发送时间: ' + new Date().toLocaleString()
        }
    ];
    
    return await batchSend(testEmails, 3000);
}

// 检查页面状态
function checkPageStatus() {
    console.log('🔍 检查页面状态...');
    
    const toField = document.querySelector('input[name="to"]');
    const subjectField = document.querySelector('input[name="subject"]');
    const contentField = document.querySelector('textarea[name="content"]');
    const sendButton = document.querySelector('input[value*="发送"]') || document.querySelector('input[type="submit"]');
    
    console.log('📋 页面元素检查:');
    console.log(`   收件人字段: ${toField ? '✅ 找到' : '❌ 未找到'}`);
    console.log(`   主题字段: ${subjectField ? '✅ 找到' : '❌ 未找到'}`);
    console.log(`   内容字段: ${contentField ? '✅ 找到' : '❌ 未找到'}`);
    console.log(`   发送按钮: ${sendButton ? '✅ 找到' : '❌ 未找到'}`);
    
    const readyCount = [toField, subjectField, contentField, sendButton].filter(Boolean).length;
    console.log(`📊 页面就绪度: ${readyCount}/4 (${(readyCount/4*100).toFixed(0)}%)`);
    
    return readyCount >= 3;
}

console.log('✅ 超高速邮件发送器加载完成!');
console.log('📧 使用方法:');
console.log('   ultraFastSend("<EMAIL>", "主题", "内容") - 发送单封邮件');
console.log('   batchSend([{to, subject, content}, ...]) - 批量发送');
console.log('   quickTest() - 快速测试');
console.log('   checkPageStatus() - 检查页面状态');

// 自动检查页面状态
setTimeout(() => {
    checkPageStatus();
}, 1000);
