#!/usr/bin/env python3
"""
超极速邮件发送调度器 - 专为速度优化
基于成功经验，进一步优化主程序浏览器发送邮件速度
目标：将发送时间从7秒优化到3-4秒
"""

import time
import queue
import threading
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

from src.utils.logger import setup_logger
from src.models.account import Account
from src.core.multi_browser_manager import MultiBrowserManager, SendingConfig, BrowserInstance

logger = setup_logger("INFO")

@dataclass
class UltraSpeedTask:
    """超极速邮件任务"""
    task_id: str
    to_email: str
    subject: str
    content: str
    priority: int = 1
    created_time: float = 0
    max_retries: int = 1  # 减少重试次数提升速度
    retry_count: int = 0
    status: str = "pending"

@dataclass
class UltraSpeedStats:
    """超极速发送统计"""
    total_tasks: int = 0
    sent_success: int = 0
    sent_failed: int = 0
    start_time: float = 0
    last_send_time: float = 0
    avg_send_time: float = 0
    fastest_send_time: float = 999
    slowest_send_time: float = 0

class UltraSpeedEmailScheduler:
    """超极速邮件发送调度器"""
    
    def __init__(self, config: SendingConfig):
        # 超极速优化配置
        self.config = config
        self.config.send_interval = 0.1  # 极短发送间隔
        
        self.browser_manager = MultiBrowserManager(config)
        
        # 超极速任务队列 - 使用更快的队列
        self.task_queue = queue.Queue()  # 使用普通队列而非优先队列
        self.completed_tasks: List[UltraSpeedTask] = []
        self.failed_tasks: List[UltraSpeedTask] = []
        
        # 超极速统计
        self.stats = UltraSpeedStats()
        
        # 线程控制 - 优化线程管理
        self.is_running = False
        self.worker_threads: List[threading.Thread] = []
        
        # 超极速发送控制
        self.last_send_times: Dict[str, float] = {}
        self.send_lock = threading.Lock()
        
        # 预热的发送器缓存
        self.sender_cache: Dict[str, Any] = {}
        
        logger.info("⚡ 超极速邮件发送调度器初始化完成")
    
    def initialize(self, accounts: List[Account]) -> bool:
        """超极速初始化调度器"""
        try:
            logger.info("🚀 超极速初始化邮件发送调度器...")
            
            # 快速初始化浏览器管理器
            if not self.browser_manager.initialize_browsers():
                logger.error("❌ 浏览器初始化失败")
                return False
            
            # 设置账号队列
            self.browser_manager.set_account_queue(accounts)
            
            # 预热浏览器和发送器
            self._preheat_browsers_and_senders()
            
            self.stats.start_time = time.time()
            logger.info("✅ 超极速邮件发送调度器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 超极速调度器初始化失败: {e}")
            return False
    
    def _preheat_browsers_and_senders(self):
        """预热浏览器和发送器 - 关键优化"""
        try:
            logger.info("🔥 预热浏览器和发送器...")
            
            for browser_id, browser_instance in self.browser_manager.browsers.items():
                # 为每个浏览器加载初始账号
                account = self.browser_manager.get_next_account()
                if account:
                    if self.browser_manager.load_account_cookies(browser_instance, account):
                        logger.info(f"✅ 浏览器 {browser_id} 预热成功: {account.email}")
                        
                        # 预创建发送器并准备写邮件界面
                        self._preheat_sender_for_browser(browser_instance)
                    else:
                        logger.warning(f"⚠️ 浏览器 {browser_id} 预热失败: {account.email}")
                else:
                    logger.warning(f"⚠️ 没有账号为浏览器 {browser_id} 预热")
            
            logger.info("🔥 浏览器和发送器预热完成")
            
        except Exception as e:
            logger.error(f"❌ 预热失败: {e}")
    
    def _preheat_sender_for_browser(self, browser_instance: BrowserInstance):
        """为浏览器预热发送器"""
        try:
            # 创建并缓存发送器
            from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal
            sender = SinaUltraFastSenderFinal(browser_instance.driver)
            
            # 预准备写邮件界面
            if sender.prepare_compose_page():
                self.sender_cache[browser_instance.browser_id] = sender
                browser_instance.status = "ready"
                logger.info(f"🔥 发送器预热成功: {browser_instance.browser_id}")
            else:
                logger.warning(f"⚠️ 发送器预热失败: {browser_instance.browser_id}")
                
        except Exception as e:
            logger.error(f"❌ 发送器预热异常: {e}")
    
    def add_ultra_speed_task(self, to_email: str, subject: str, content: str) -> str:
        """添加超极速邮件任务"""
        task_id = f"ultra_{int(time.time() * 1000)}_{len(self.completed_tasks) + self.task_queue.qsize()}"
        
        task = UltraSpeedTask(
            task_id=task_id,
            to_email=to_email,
            subject=subject,
            content=content,
            created_time=time.time()
        )
        
        self.task_queue.put(task)
        self.stats.total_tasks += 1
        
        logger.info(f"⚡ 添加超极速任务: {task_id} -> {to_email}")
        return task_id
    
    def send_email_ultra_speed(self, browser_instance: BrowserInstance, task: UltraSpeedTask) -> bool:
        """超极速发送邮件 - 核心优化方法"""
        start_time = time.time()
        
        try:
            logger.info(f"⚡ 超极速发送: {task.to_email}")
            
            # 获取预热的发送器
            sender = self.sender_cache.get(browser_instance.browser_id)
            if not sender:
                logger.warning(f"⚠️ 未找到预热发送器，创建新的: {browser_instance.browser_id}")
                from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal
                sender = SinaUltraFastSenderFinal(browser_instance.driver)
                
                # 快速准备
                if not sender.prepare_compose_page():
                    logger.error(f"❌ 发送器准备失败: {browser_instance.browser_id}")
                    return False
                
                self.sender_cache[browser_instance.browser_id] = sender
            
            # 设置浏览器状态
            browser_instance.status = "busy"
            
            # 超极速发送 - 跳过不必要的检查
            success = sender.send_email_ultra_fast(
                task.to_email,
                task.subject,
                task.content
            )
            
            if success:
                # 最小化统计更新
                browser_instance.sent_count += 1
                browser_instance.last_activity = time.time()
                browser_instance.status = "ready"
                
                self.last_send_times[browser_instance.browser_id] = time.time()
                
                elapsed = time.time() - start_time
                
                # 更新速度统计
                self._update_speed_stats(elapsed)
                
                logger.info(f"⚡ 超极速发送成功: {task.task_id} ({elapsed:.2f}秒)")
                
                # 跳过重置状态以节省时间
                # sender.reset_for_next_email()  # 注释掉以提升速度
                
                return True
            else:
                browser_instance.status = "ready"
                elapsed = time.time() - start_time
                logger.error(f"❌ 超极速发送失败: {task.task_id} ({elapsed:.2f}秒)")
                return False
                
        except Exception as e:
            browser_instance.status = "error"
            elapsed = time.time() - start_time
            logger.error(f"❌ 超极速发送异常: {task.task_id} ({elapsed:.2f}秒) - {e}")
            return False
    
    def _update_speed_stats(self, elapsed_time: float):
        """更新速度统计"""
        self.stats.last_send_time = time.time()
        
        # 更新平均发送时间
        if self.stats.sent_success > 0:
            self.stats.avg_send_time = (
                (self.stats.avg_send_time * (self.stats.sent_success - 1) + elapsed_time) / 
                self.stats.sent_success
            )
        else:
            self.stats.avg_send_time = elapsed_time
        
        # 更新最快和最慢时间
        if elapsed_time < self.stats.fastest_send_time:
            self.stats.fastest_send_time = elapsed_time
        
        if elapsed_time > self.stats.slowest_send_time:
            self.stats.slowest_send_time = elapsed_time
    
    def get_next_ultra_speed_browser(self) -> Optional[BrowserInstance]:
        """获取下一个超极速可用浏览器"""
        current_time = time.time()
        
        for browser_instance in self.browser_manager.browsers.values():
            if browser_instance.status != "ready":
                continue
            
            # 极短发送间隔检查
            last_send_time = self.last_send_times.get(browser_instance.browser_id, 0)
            if current_time - last_send_time < 0.1:  # 仅0.1秒间隔
                continue
            
            return browser_instance
        
        return None
    
    def ultra_speed_worker_thread(self):
        """超极速工作线程"""
        logger.info("⚡ 超极速工作线程启动")
        
        while self.is_running:
            try:
                # 快速获取任务
                try:
                    task = self.task_queue.get(timeout=0.1)  # 极短超时
                except queue.Empty:
                    continue
                
                # 快速获取可用浏览器
                browser_instance = self.get_next_ultra_speed_browser()
                if not browser_instance:
                    # 立即重新放回队列
                    self.task_queue.put(task)
                    time.sleep(0.01)  # 极短等待
                    continue
                
                # 超极速发送
                task.status = "sending"
                success = self.send_email_ultra_speed(browser_instance, task)
                
                if success:
                    task.status = "sent"
                    self.completed_tasks.append(task)
                    self.stats.sent_success += 1
                else:
                    task.retry_count += 1
                    if task.retry_count < task.max_retries:
                        task.status = "pending"
                        self.task_queue.put(task)
                        logger.info(f"⚡ 超极速重试: {task.task_id}")
                    else:
                        task.status = "failed"
                        self.failed_tasks.append(task)
                        self.stats.sent_failed += 1
                        logger.error(f"❌ 超极速最终失败: {task.task_id}")
                
                self.task_queue.task_done()
                
                # 无额外等待，立即处理下一个任务
                
            except Exception as e:
                logger.error(f"❌ 超极速工作线程异常: {e}")
                time.sleep(0.01)
        
        logger.info("⚡ 超极速工作线程结束")
    
    def start_ultra_speed_sending(self, num_workers: int = 1):
        """启动超极速发送"""
        if self.is_running:
            logger.warning("⚠️ 超极速调度器已在运行")
            return
        
        logger.info(f"⚡ 启动超极速邮件发送调度器 ({num_workers} 个工作线程)")
        
        self.is_running = True
        
        # 启动超极速工作线程
        for i in range(num_workers):
            worker = threading.Thread(target=self.ultra_speed_worker_thread, name=f"UltraSpeedWorker-{i+1}")
            worker.daemon = True
            worker.start()
            self.worker_threads.append(worker)
        
        logger.info("⚡ 超极速邮件发送调度器启动完成")
    
    def stop_ultra_speed_sending(self):
        """停止超极速发送"""
        if not self.is_running:
            return
        
        logger.info("🛑 停止超极速邮件发送调度器...")
        self.is_running = False
        
        # 等待工作线程结束
        for worker in self.worker_threads:
            worker.join(timeout=1)
        
        self.worker_threads.clear()
        logger.info("✅ 超极速邮件发送调度器已停止")
    
    def get_ultra_speed_stats(self) -> UltraSpeedStats:
        """获取超极速统计信息"""
        return self.stats
    
    def cleanup(self):
        """清理资源"""
        self.stop_ultra_speed_sending()
        
        # 清理发送器缓存
        for sender in self.sender_cache.values():
            try:
                if hasattr(sender, 'cleanup'):
                    sender.cleanup()
            except:
                pass
        
        self.sender_cache.clear()
        
        if self.browser_manager:
            try:
                self.browser_manager.cleanup()
            except:
                pass
        
        logger.info("🧹 超极速调度器资源已清理")
