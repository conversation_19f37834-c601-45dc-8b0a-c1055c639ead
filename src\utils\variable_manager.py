#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
变量管理器
提供邮件变量的管理和替换功能，提高邮件个性化程度和进箱率
"""

import json
import re
import random
from typing import Dict, List, Any, Optional
from datetime import datetime
from src.utils.logger import get_logger

logger = get_logger("VariableManager")


class VariableManager:
    """变量管理器"""
    
    def __init__(self):
        """初始化变量管理器"""
        self.built_in_variables = self._get_built_in_variables()
        self.random_variables = self._get_random_variables()
    
    def _get_built_in_variables(self) -> Dict[str, str]:
        """获取内置变量"""
        now = datetime.now()
        return {
            'date': now.strftime('%Y-%m-%d'),
            'time': now.strftime('%H:%M:%S'),
            'datetime': now.strftime('%Y-%m-%d %H:%M:%S'),
            'year': str(now.year),
            'month': str(now.month),
            'day': str(now.day),
            'weekday': ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][now.weekday()],
        }
    
    def _get_random_variables(self) -> Dict[str, List[str]]:
        """获取随机变量池"""
        return {
            'greeting': [
                '您好', '亲爱的朋友', '尊敬的客户', '亲爱的用户',
                '尊敬的先生/女士', '亲爱的合作伙伴'
            ],
            'closing': [
                '此致敬礼', '谢谢您的关注', '期待您的回复', 
                '祝您工作顺利', '祝您身体健康', '祝您生活愉快'
            ],
            'urgency': [
                '重要通知', '紧急提醒', '特别提醒', '重要信息',
                '限时优惠', '独家消息', '最新资讯'
            ],
            'action': [
                '立即查看', '马上了解', '点击查看', '详情请见',
                '欢迎咨询', '联系我们', '获取更多信息'
            ],
            'benefit': [
                '专属优惠', '特别折扣', '限时特价', '独家福利',
                '会员专享', '新用户特惠', '老客户回馈'
            ]
        }
    
    def extract_variables(self, text: str) -> List[str]:
        """
        从文本中提取变量
        
        Args:
            text: 包含变量的文本
        
        Returns:
            变量名列表
        """
        pattern = r'\{([^}]+)\}'
        variables = re.findall(pattern, text)
        return list(set(variables))  # 去重
    
    def validate_variables(self, text: str, available_vars: Dict[str, str]) -> Dict[str, List[str]]:
        """
        验证文本中的变量
        
        Args:
            text: 包含变量的文本
            available_vars: 可用变量字典
        
        Returns:
            验证结果 {'valid': [...], 'invalid': [...]}
        """
        variables = self.extract_variables(text)
        valid_vars = []
        invalid_vars = []
        
        for var in variables:
            if var in available_vars or var in self.built_in_variables:
                valid_vars.append(var)
            else:
                invalid_vars.append(var)
        
        return {
            'valid': valid_vars,
            'invalid': invalid_vars
        }
    
    def replace_variables(self, text: str, variables: Dict[str, str], 
                         use_random: bool = True) -> str:
        """
        替换文本中的变量
        
        Args:
            text: 包含变量的文本
            variables: 变量值字典
            use_random: 是否使用随机变量
        
        Returns:
            替换后的文本
        """
        result = text
        
        # 合并所有可用变量
        all_vars = {**self.built_in_variables, **variables}
        
        # 添加随机变量
        if use_random:
            for var_type, values in self.random_variables.items():
                if f'{{{var_type}}}' in result:
                    all_vars[var_type] = random.choice(values)
        
        # 执行替换
        for var_name, var_value in all_vars.items():
            placeholder = f'{{{var_name}}}'
            result = result.replace(placeholder, str(var_value))
        
        return result
    
    def generate_personalized_content(self, template: str, recipient_info: Dict[str, str],
                                    randomize_level: int = 1) -> str:
        """
        生成个性化内容
        
        Args:
            template: 邮件模板
            recipient_info: 收件人信息
            randomize_level: 随机化级别 (0-3)
        
        Returns:
            个性化后的内容
        """
        # 基础变量替换
        content = self.replace_variables(template, recipient_info, use_random=True)
        
        # 根据随机化级别添加额外的个性化
        if randomize_level >= 1:
            content = self._add_random_elements(content)
        
        if randomize_level >= 2:
            content = self._vary_sentence_structure(content)
        
        if randomize_level >= 3:
            content = self._add_dynamic_content(content)
        
        return content
    
    def _add_random_elements(self, content: str) -> str:
        """添加随机元素"""
        # 随机添加一些变化
        variations = {
            '您好': random.choice(['您好', '你好', '亲爱的朋友']),
            '谢谢': random.choice(['谢谢', '感谢', '非常感谢']),
            '请': random.choice(['请', '烦请', '敬请']),
        }
        
        for original, replacement in variations.items():
            if original in content and random.random() < 0.3:  # 30%概率替换
                content = content.replace(original, replacement, 1)
        
        return content
    
    def _vary_sentence_structure(self, content: str) -> str:
        """变化句子结构"""
        # 简单的句子结构变化
        sentences = content.split('。')
        varied_sentences = []
        
        for sentence in sentences:
            if sentence.strip():
                # 随机调整句子顺序或添加连接词
                if random.random() < 0.2:  # 20%概率添加连接词
                    connectors = ['另外，', '此外，', '同时，', '而且，']
                    sentence = random.choice(connectors) + sentence.strip()
                varied_sentences.append(sentence)
        
        return '。'.join(varied_sentences)
    
    def _add_dynamic_content(self, content: str) -> str:
        """添加动态内容"""
        # 根据时间添加动态问候
        now = datetime.now()
        hour = now.hour
        
        if 6 <= hour < 12:
            time_greeting = "早上好！"
        elif 12 <= hour < 18:
            time_greeting = "下午好！"
        else:
            time_greeting = "晚上好！"
        
        # 在内容开头添加时间问候
        if not any(greeting in content for greeting in ["早上好", "下午好", "晚上好"]):
            content = time_greeting + content
        
        return content
    
    def get_variable_suggestions(self, context: str = "") -> Dict[str, List[str]]:
        """
        获取变量建议
        
        Args:
            context: 上下文信息
        
        Returns:
            变量建议分类
        """
        suggestions = {
            '基础信息': ['name', 'email', 'company', 'phone', 'title'],
            '时间相关': ['date', 'time', 'datetime', 'year', 'month', 'day', 'weekday'],
            '个性化': ['greeting', 'closing', 'urgency', 'action', 'benefit'],
            '产品相关': ['product', 'service', 'price', 'discount', 'feature'],
            '营销相关': ['offer', 'deadline', 'bonus', 'exclusive', 'limited']
        }
        
        return suggestions
    
    def create_variable_template(self, variables: List[str]) -> str:
        """
        创建变量模板
        
        Args:
            variables: 变量列表
        
        Returns:
            JSON格式的变量模板
        """
        template = {}
        
        for var in variables:
            if var in ['name', 'email', 'company', 'phone']:
                template[var] = f"请输入{var}"
            elif var in ['product', 'service']:
                template[var] = f"请输入{var}名称"
            elif var in ['price', 'discount']:
                template[var] = f"请输入{var}信息"
            else:
                template[var] = f"请输入{var}的值"
        
        return json.dumps(template, ensure_ascii=False, indent=2)
    
    def analyze_template_effectiveness(self, template: str) -> Dict[str, Any]:
        """
        分析模板有效性
        
        Args:
            template: 邮件模板
        
        Returns:
            分析结果
        """
        variables = self.extract_variables(template)
        
        # 计算个性化程度
        personalization_score = len(variables) * 10
        if personalization_score > 100:
            personalization_score = 100
        
        # 分析变量类型
        var_types = {
            'personal': 0,  # 个人信息
            'temporal': 0,  # 时间相关
            'random': 0,    # 随机变量
            'custom': 0     # 自定义变量
        }
        
        for var in variables:
            if var in ['name', 'email', 'company', 'phone', 'title']:
                var_types['personal'] += 1
            elif var in self.built_in_variables:
                var_types['temporal'] += 1
            elif var in self.random_variables:
                var_types['random'] += 1
            else:
                var_types['custom'] += 1
        
        # 生成建议
        suggestions = []
        if var_types['personal'] == 0:
            suggestions.append("建议添加个人信息变量如{name}提高个性化")
        if var_types['temporal'] == 0:
            suggestions.append("建议添加时间变量如{date}增加时效性")
        if len(variables) < 3:
            suggestions.append("建议增加更多变量提高个性化程度")
        
        return {
            'personalization_score': personalization_score,
            'variable_count': len(variables),
            'variable_types': var_types,
            'suggestions': suggestions,
            'variables': variables
        }
