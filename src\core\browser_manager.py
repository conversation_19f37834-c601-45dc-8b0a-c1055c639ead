#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器管理模块
负责浏览器的创建、配置和管理，支持代理设置和多浏览器实例
"""

import os
import time
import random
from pathlib import Path
from typing import Optional, Dict, Any, List
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from src.utils.logger import get_logger
from src.models.account import Account

logger = get_logger("BrowserManager")


class BrowserManager:
    """浏览器管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化浏览器管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.drivers: Dict[str, webdriver.Chrome] = {}
        self.driver_count = 0
        self.max_drivers = config.get('performance.max_concurrent_browsers', 3)
        
        # 浏览器配置
        self.browser_config = config.get('browser', {})
        self.sina_config = config.get('sina_email', {})
        
        logger.info("浏览器管理器初始化完成")
    
    def create_driver(self, account: Account, driver_id: Optional[str] = None) -> str:
        """
        创建浏览器驱动实例
        
        Args:
            account: 账号信息（包含代理配置）
            driver_id: 驱动ID，如果为None则自动生成
        
        Returns:
            驱动ID
        """
        if self.driver_count >= self.max_drivers:
            raise Exception(f"已达到最大浏览器数量限制: {self.max_drivers}")
        
        if driver_id is None:
            driver_id = f"driver_{int(time.time())}_{random.randint(1000, 9999)}"
        
        try:
            # 创建Chrome选项
            options = self._create_chrome_options(account)
            
            # 创建服务
            service = Service(ChromeDriverManager().install())
            
            # 创建驱动
            driver = webdriver.Chrome(service=service, options=options)
            
            # 设置超时 - 增加稳定性
            driver.implicitly_wait(self.browser_config.get('implicit_wait', 15))  # 增加等待时间
            driver.set_page_load_timeout(self.browser_config.get('page_load_timeout', 60))  # 增加页面加载超时
            driver.set_script_timeout(30)  # 设置脚本执行超时

            # 设置窗口大小
            window_size = self.browser_config.get('window_size', [1920, 1080])
            try:
                driver.set_window_size(window_size[0], window_size[1])
                logger.info(f"窗口大小设置为: {window_size[0]}x{window_size[1]}")
            except Exception as e:
                logger.warning(f"设置窗口大小失败: {e}")

            # 添加错误恢复机制
            try:
                # 测试浏览器连接
                driver.get("about:blank")
                logger.info("浏览器连接测试成功")
            except Exception as e:
                logger.error(f"浏览器连接测试失败: {e}")
                raise
            
            # 存储驱动
            self.drivers[driver_id] = driver
            self.driver_count += 1
            
            logger.info(f"浏览器驱动创建成功: {driver_id}")
            return driver_id
            
        except Exception as e:
            logger.error(f"创建浏览器驱动失败: {e}")
            raise
    
    def _create_chrome_options(self, account: Account) -> ChromeOptions:
        """
        创建Chrome选项
        
        Args:
            account: 账号信息
        
        Returns:
            Chrome选项对象
        """
        options = ChromeOptions()
        
        # 基本选项 - 隐藏式登录优化
        headless_mode = self.browser_config.get('headless', False)
        window_size = self.browser_config.get('window_size', [1920, 1080])

        if headless_mode:
            # 无头模式 - 完全隐藏，最轻量
            options.add_argument('--headless')
            options.add_argument('--disable-gpu')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            # 无头模式专用优化
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-images')  # 无头模式可以禁用图片
            options.add_argument('--disable-css')     # 禁用CSS加载
            options.add_argument('--disable-fonts')   # 禁用字体加载
            options.add_argument('--disable-audio')   # 禁用音频
            options.add_argument('--disable-video')   # 禁用视频
            logger.info("🥷 无头模式已启用 - 完全后台运行，最轻量配置")
        else:
            # 可见模式
            if window_size[0] <= 500 and window_size[1] <= 400:
                # 最小化模式
                options.add_argument(f'--window-size={window_size[0]},{window_size[1]}')
                options.add_argument('--window-position=0,0')
                options.add_argument('--disable-extensions')
                options.add_argument('--disable-plugins')
                # options.add_argument('--disable-images')  # 最小化模式保留图片，可能需要看验证码
                logger.info(f"📱 最小化模式已启用 - 窗口大小: {window_size[0]}x{window_size[1]}")
            else:
                # 普通模式
                logger.info(f"🖥️ 普通模式 - 窗口大小: {window_size[0]}x{window_size[1]}")

        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')

        # SSL和网络连接优化 - 修复SSL握手错误
        options.add_argument('--ignore-ssl-errors')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-certificate-errors-spki-list')
        options.add_argument('--disable-ssl-false-start')
        options.add_argument('--disable-background-networking')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-background-mode')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-sync')
        options.add_argument('--disable-translate')
        options.add_argument('--disable-ipc-flooding-protection')

        # 网络超时和重试设置
        options.add_argument('--aggressive-cache-discard')
        options.add_argument('--disable-hang-monitor')
        options.add_argument('--disable-prompt-on-repost')
        options.add_argument('--disable-domain-reliability')
        options.add_argument('--disable-component-extensions-with-background-pages')
        
        # 设置User-Agent
        user_agent = self.browser_config.get('user_agent')
        if user_agent:
            options.add_argument(f'--user-agent={user_agent}')
        
        # 代理设置
        if account.has_proxy():
            proxy_info = account.get_proxy_info()
            if proxy_info:
                proxy_string = f"{proxy_info['ip']}:{proxy_info['port']}"
                
                # 如果有用户名密码，需要使用插件方式
                if 'username' in proxy_info and 'password' in proxy_info:
                    # 创建代理插件
                    plugin_path = self._create_proxy_plugin(proxy_info)
                    options.add_extension(plugin_path)
                else:
                    options.add_argument(f'--proxy-server=http://{proxy_string}')
                
                logger.info(f"配置代理: {proxy_string}")
        
        # 下载设置
        download_path = self.browser_config.get('download_path', 'data/downloads')
        if not os.path.isabs(download_path):
            project_root = Path(__file__).parent.parent.parent
            download_path = project_root / download_path
        
        download_path = Path(download_path)
        download_path.mkdir(parents=True, exist_ok=True)
        
        prefs = {
            'download.default_directory': str(download_path),
            'download.prompt_for_download': False,
            'download.directory_upgrade': True,
            'safebrowsing.enabled': True
        }
        options.add_experimental_option('prefs', prefs)
        
        # 禁用通知
        options.add_experimental_option('useAutomationExtension', False)
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_argument('--disable-blink-features=AutomationControlled')
        
        return options
    
    def _create_proxy_plugin(self, proxy_info: Dict[str, Any]) -> str:
        """
        创建代理认证插件
        
        Args:
            proxy_info: 代理信息
        
        Returns:
            插件文件路径
        """
        import zipfile
        import tempfile
        
        # 插件代码
        manifest_json = """
        {
            "version": "1.0.0",
            "manifest_version": 2,
            "name": "Chrome Proxy",
            "permissions": [
                "proxy",
                "tabs",
                "unlimitedStorage",
                "storage",
                "<all_urls>",
                "webRequest",
                "webRequestBlocking"
            ],
            "background": {
                "scripts": ["background.js"]
            },
            "minimum_chrome_version":"22.0.0"
        }
        """
        
        background_js = f"""
        var config = {{
                mode: "fixed_servers",
                rules: {{
                  singleProxy: {{
                    scheme: "http",
                    host: "{proxy_info['ip']}",
                    port: parseInt({proxy_info['port']})
                  }},
                  bypassList: ["localhost"]
                }}
              }};

        chrome.proxy.settings.set({{value: config, scope: "regular"}}, function() {{}});

        function callbackFn(details) {{
            return {{
                authCredentials: {{
                    username: "{proxy_info['username']}",
                    password: "{proxy_info['password']}"
                }}
            }};
        }}

        chrome.webRequest.onAuthRequired.addListener(
                    callbackFn,
                    {{urls: ["<all_urls>"]}},
                    ['blocking']
        );
        """
        
        # 创建临时插件文件
        plugin_file = tempfile.NamedTemporaryFile(suffix='.zip', delete=False)
        
        with zipfile.ZipFile(plugin_file.name, 'w') as zf:
            zf.writestr("manifest.json", manifest_json)
            zf.writestr("background.js", background_js)
        
        return plugin_file.name
    
    def get_driver(self, driver_id: str) -> Optional[webdriver.Chrome]:
        """
        获取浏览器驱动
        
        Args:
            driver_id: 驱动ID
        
        Returns:
            浏览器驱动实例或None
        """
        return self.drivers.get(driver_id)
    
    def close_driver(self, driver_id: str) -> bool:
        """
        关闭浏览器驱动
        
        Args:
            driver_id: 驱动ID
        
        Returns:
            是否关闭成功
        """
        try:
            driver = self.drivers.get(driver_id)
            if driver:
                logger.info(f"开始关闭浏览器驱动: {driver_id}")

                # 尝试优雅关闭
                try:
                    # 先关闭所有标签页
                    for handle in driver.window_handles:
                        try:
                            driver.switch_to.window(handle)
                            driver.close()
                        except:
                            pass
                except:
                    pass

                # 退出驱动
                try:
                    driver.quit()
                    logger.info(f"浏览器驱动优雅关闭成功: {driver_id}")
                except Exception as e:
                    logger.warning(f"优雅关闭失败: {e}")

                # 从字典中移除
                del self.drivers[driver_id]
                self.driver_count -= 1
                logger.info(f"浏览器驱动清理完成: {driver_id}")
                return True
            else:
                logger.warning(f"未找到浏览器驱动: {driver_id}")
                return False
        except Exception as e:
            logger.error(f"关闭浏览器驱动失败: {driver_id}, 错误: {e}")
            # 即使关闭失败，也要从字典中移除
            if driver_id in self.drivers:
                del self.drivers[driver_id]
                self.driver_count -= 1
                logger.info(f"强制清理驱动引用: {driver_id}")
            return False
    
    def close_all_drivers(self):
        """关闭所有浏览器驱动"""
        driver_ids = list(self.drivers.keys())
        for driver_id in driver_ids:
            self.close_driver(driver_id)
        
        logger.info("所有浏览器驱动已关闭")
    
    def wait_for_element(self, driver: webdriver.Chrome, by: By, value: str, 
                        timeout: int = 10) -> bool:
        """
        等待元素出现
        
        Args:
            driver: 浏览器驱动
            by: 定位方式
            value: 定位值
            timeout: 超时时间（秒）
        
        Returns:
            是否找到元素
        """
        try:
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return True
        except TimeoutException:
            logger.warning(f"等待元素超时: {by}={value}")
            return False
    
    def safe_click(self, driver: webdriver.Chrome, by: By, value: str, 
                   timeout: int = 10) -> bool:
        """
        安全点击元素
        
        Args:
            driver: 浏览器驱动
            by: 定位方式
            value: 定位值
            timeout: 超时时间（秒）
        
        Returns:
            是否点击成功
        """
        try:
            element = WebDriverWait(driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            element.click()
            return True
        except TimeoutException:
            logger.warning(f"点击元素超时: {by}={value}")
            return False
        except Exception as e:
            logger.error(f"点击元素失败: {by}={value}, 错误: {e}")
            return False
    
    def safe_send_keys(self, driver: webdriver.Chrome, by: By, value: str, 
                      text: str, timeout: int = 10, clear_first: bool = True) -> bool:
        """
        安全输入文本
        
        Args:
            driver: 浏览器驱动
            by: 定位方式
            value: 定位值
            text: 要输入的文本
            timeout: 超时时间（秒）
            clear_first: 是否先清空输入框
        
        Returns:
            是否输入成功
        """
        try:
            element = WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            
            if clear_first:
                element.clear()
            
            # 模拟人工输入，添加随机延迟
            for char in text:
                element.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))
            
            return True
        except TimeoutException:
            logger.warning(f"输入文本超时: {by}={value}")
            return False
        except Exception as e:
            logger.error(f"输入文本失败: {by}={value}, 错误: {e}")
            return False
    
    def random_delay(self, min_seconds: float = 1.0, max_seconds: float = 3.0):
        """
        随机延迟
        
        Args:
            min_seconds: 最小延迟时间
            max_seconds: 最大延迟时间
        """
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def get_driver_count(self) -> int:
        """获取当前驱动数量"""
        return self.driver_count
    
    def get_max_drivers(self) -> int:
        """获取最大驱动数量"""
        return self.max_drivers
