#!/usr/bin/env python3
"""
邮件发送调度器
管理多浏览器轮换发送邮件，支持间隔控制和任务调度
"""

import time
import threading
import queue
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from src.utils.logger import setup_logger
from src.models.account import Account
from src.core.multi_browser_manager import MultiBrowserManager, SendingConfig, BrowserInstance

logger = setup_logger("INFO")

@dataclass
class EmailTask:
    """邮件任务"""
    task_id: str
    to_email: str
    subject: str
    content: str
    content_type: str = "text/plain"
    priority: int = 1
    created_time: float = 0
    scheduled_time: float = 0
    max_retries: int = 3
    retry_count: int = 0
    status: str = "pending"  # pending, sending, sent, failed, cancelled

@dataclass
class SendingStats:
    """发送统计"""
    total_tasks: int = 0
    sent_success: int = 0
    sent_failed: int = 0
    pending_tasks: int = 0
    start_time: float = 0
    last_send_time: float = 0
    emails_per_minute: float = 0

class EmailSendingScheduler:
    """邮件发送调度器"""
    
    def __init__(self, config: SendingConfig):
        self.config = config
        self.browser_manager = MultiBrowserManager(config)
        
        # 任务队列
        self.task_queue = queue.PriorityQueue()
        self.completed_tasks: List[EmailTask] = []
        self.failed_tasks: List[EmailTask] = []
        
        # 发送统计
        self.stats = SendingStats()
        
        # 线程控制
        self.is_running = False
        self.worker_threads: List[threading.Thread] = []
        self.scheduler_thread: Optional[threading.Thread] = None
        
        # 发送控制
        self.last_send_times: Dict[str, float] = {}  # 每个浏览器的最后发送时间
        self.send_lock = threading.Lock()
        
        logger.info("📧 邮件发送调度器初始化完成")
    
    def initialize(self, accounts: List[Account]) -> bool:
        """初始化调度器"""
        try:
            logger.info("🚀 初始化邮件发送调度器...")
            
            # 初始化浏览器管理器
            if not self.browser_manager.initialize_browsers():
                logger.error("❌ 浏览器初始化失败")
                return False
            
            # 设置账号队列
            self.browser_manager.set_account_queue(accounts)
            
            # 为每个浏览器加载初始账号
            for browser_id, browser_instance in self.browser_manager.browsers.items():
                account = self.browser_manager.get_next_account()
                if account:
                    if self.browser_manager.load_account_cookies(browser_instance, account):
                        logger.info(f"✅ 浏览器 {browser_id} 初始账号加载成功: {account.email}")
                    else:
                        logger.warning(f"⚠️ 浏览器 {browser_id} 初始账号加载失败: {account.email}")
                else:
                    logger.warning(f"⚠️ 没有足够的账号为浏览器 {browser_id} 分配")
            
            self.stats.start_time = time.time()
            logger.info("✅ 邮件发送调度器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 调度器初始化失败: {e}")
            return False
    
    def add_email_task(self, to_email: str, subject: str, content: str, 
                      content_type: str = "text/plain", priority: int = 1,
                      scheduled_time: Optional[float] = None) -> str:
        """添加邮件任务"""
        task_id = f"task_{int(time.time() * 1000)}_{len(self.completed_tasks) + self.task_queue.qsize()}"
        
        task = EmailTask(
            task_id=task_id,
            to_email=to_email,
            subject=subject,
            content=content,
            content_type=content_type,
            priority=priority,
            created_time=time.time(),
            scheduled_time=scheduled_time or time.time()
        )
        
        # 使用负优先级，因为PriorityQueue是最小堆
        self.task_queue.put((-priority, task.scheduled_time, task))
        self.stats.total_tasks += 1
        self.stats.pending_tasks += 1
        
        logger.info(f"📝 添加邮件任务: {task_id} -> {to_email}")
        return task_id
    
    def add_batch_tasks(self, email_list: List[Tuple[str, str, str]], 
                       content_type: str = "text/plain", 
                       send_interval: Optional[float] = None) -> List[str]:
        """批量添加邮件任务"""
        task_ids = []
        current_time = time.time()
        interval = send_interval or self.config.send_interval
        
        for i, (to_email, subject, content) in enumerate(email_list):
            scheduled_time = current_time + (i * interval)
            task_id = self.add_email_task(
                to_email, subject, content, content_type, 
                priority=1, scheduled_time=scheduled_time
            )
            task_ids.append(task_id)
        
        logger.info(f"📝 批量添加 {len(email_list)} 个邮件任务")
        return task_ids
    
    def send_email_with_browser(self, browser_instance: BrowserInstance, task: EmailTask) -> bool:
        """使用指定浏览器发送邮件 - 高速版本"""
        try:
            logger.info(f"⚡ 浏览器 {browser_instance.browser_id} 高速发送邮件: {task.to_email}")
            start_time = time.time()

            # 检查并切换账号
            if not self.browser_manager.switch_account_if_needed(browser_instance):
                logger.error(f"❌ 浏览器 {browser_instance.browser_id} 账号切换失败")
                return False

            # 设置浏览器状态
            browser_instance.status = "busy"

            # 创建新浪专用超高速发送器 - 使用最新成功版本
            from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal
            ultra_fast_sender = SinaUltraFastSenderFinal(browser_instance.driver)

            # 准备发送环境
            if not ultra_fast_sender.prepare_compose_page():
                logger.error(f"❌ 新浪超高速发送器准备失败: {browser_instance.browser_id}")
                browser_instance.status = "error"
                return False

            # 超高速发送邮件
            success = ultra_fast_sender.send_email_ultra_fast(
                task.to_email,
                task.subject,
                task.content
            )

            if success:
                # 更新统计
                browser_instance.sent_count += 1
                browser_instance.last_activity = time.time()
                browser_instance.status = "ready"

                self.last_send_times[browser_instance.browser_id] = time.time()

                elapsed = time.time() - start_time
                logger.info(f"✅ 超高速邮件发送成功: {task.task_id} ({elapsed:.2f}秒)")

                # 获取发送统计
                stats = ultra_fast_sender.get_stats()
                logger.info(f"📈 发送统计: 成功率 {stats['success_rate']}%, 总计 {stats['send_count']} 封")

                # 超极速优化：使用快速重置（如果需要）
                if hasattr(ultra_fast_sender, 'quick_reset_for_continuous_sending'):
                    ultra_fast_sender.quick_reset_for_continuous_sending()
                elif hasattr(ultra_fast_sender, 'reset_for_next_email'):
                    ultra_fast_sender.reset_for_next_email()
                return True
            else:
                browser_instance.status = "ready"
                elapsed = time.time() - start_time
                logger.error(f"❌ 超高速邮件发送失败: {task.task_id} ({elapsed:.2f}秒)")

                # 获取发送统计
                stats = ultra_fast_sender.get_stats()
                logger.error(f"📈 发送统计: 成功率 {stats['success_rate']}%, 总计 {stats['send_count']} 封")
                return False

        except Exception as e:
            browser_instance.status = "error"
            elapsed = time.time() - start_time if 'start_time' in locals() else 0
            logger.error(f"❌ 高速发送邮件异常: {task.task_id} - {e} ({elapsed:.2f}秒)")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def _check_send_result(self, driver) -> bool:
        """检查发送结果"""
        try:
            # 检查页面内容判断是否发送成功
            page_source = driver.page_source.lower()
            
            success_indicators = ['发送成功', '已发送', 'sent successfully', 'message sent']
            error_indicators = ['发送失败', 'send failed', 'error', '错误']
            
            has_success = any(indicator in page_source for indicator in success_indicators)
            has_error = any(indicator in page_source for indicator in error_indicators)
            
            if has_success and not has_error:
                return True
            elif has_error:
                return False
            else:
                # 如果没有明确的成功或失败标识，检查URL变化
                current_url = driver.current_url
                if 'sent' in current_url or 'success' in current_url:
                    return True
                else:
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 检查发送结果失败: {e}")
            return False
    
    def get_next_available_browser(self) -> Optional[BrowserInstance]:
        """获取下一个可用的浏览器"""
        current_time = time.time()
        
        for browser_instance in self.browser_manager.browsers.values():
            if browser_instance.status != "ready":
                continue
            
            # 超极速优化：检查发送间隔（优化为更短间隔）
            last_send_time = self.last_send_times.get(browser_instance.browser_id, 0)
            ultra_speed_interval = min(self.config.send_interval, 0.5)  # 最大0.5秒间隔
            if current_time - last_send_time < ultra_speed_interval:
                continue
            
            return browser_instance
        
        return None
    
    def worker_thread_func(self):
        """工作线程函数"""
        logger.info("🔄 邮件发送工作线程启动")
        
        while self.is_running:
            try:
                # 获取任务
                try:
                    priority, scheduled_time, task = self.task_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # 检查是否到了发送时间
                current_time = time.time()
                if current_time < scheduled_time:
                    # 重新放回队列
                    self.task_queue.put((priority, scheduled_time, task))
                    time.sleep(0.1)
                    continue
                
                # 获取可用浏览器
                browser_instance = self.get_next_available_browser()
                if not browser_instance:
                    # 重新放回队列
                    self.task_queue.put((priority, scheduled_time, task))
                    time.sleep(0.5)
                    continue
                
                # 发送邮件
                task.status = "sending"
                success = self.send_email_with_browser(browser_instance, task)
                
                if success:
                    task.status = "sent"
                    self.completed_tasks.append(task)
                    self.stats.sent_success += 1
                    self.stats.last_send_time = time.time()
                else:
                    task.retry_count += 1
                    if task.retry_count < task.max_retries:
                        task.status = "pending"
                        # 重新放回队列，延迟重试
                        retry_time = current_time + (task.retry_count * 5)
                        self.task_queue.put((priority, retry_time, task))
                        logger.info(f"🔄 任务重试: {task.task_id} (第{task.retry_count}次)")
                    else:
                        task.status = "failed"
                        self.failed_tasks.append(task)
                        self.stats.sent_failed += 1
                        logger.error(f"❌ 任务最终失败: {task.task_id}")
                
                self.stats.pending_tasks -= 1
                self.task_queue.task_done()
                
                # 更新发送速率
                self._update_sending_rate()
                
            except Exception as e:
                logger.error(f"❌ 工作线程异常: {e}")
                time.sleep(1)
        
        logger.info("🔄 邮件发送工作线程结束")
    
    def _update_sending_rate(self):
        """更新发送速率"""
        if self.stats.start_time > 0:
            elapsed_time = time.time() - self.stats.start_time
            if elapsed_time > 0:
                self.stats.emails_per_minute = (self.stats.sent_success * 60) / elapsed_time

    def get_stats(self) -> SendingStats:
        """获取发送统计信息"""
        self._update_sending_rate()
        return self.stats
    
    def start_sending(self, num_workers: int = 2):
        """开始发送邮件"""
        if self.is_running:
            logger.warning("⚠️ 发送调度器已在运行")
            return
        
        logger.info(f"🚀 启动邮件发送调度器 ({num_workers} 个工作线程)")
        
        self.is_running = True
        
        # 启动工作线程
        for i in range(num_workers):
            worker = threading.Thread(target=self.worker_thread_func, name=f"EmailWorker-{i+1}")
            worker.daemon = True
            worker.start()
            self.worker_threads.append(worker)
        
        logger.info("✅ 邮件发送调度器启动完成")
    
    def stop_sending(self):
        """停止发送邮件"""
        logger.info("🛑 停止邮件发送调度器...")
        
        self.is_running = False
        
        # 等待工作线程结束
        for worker in self.worker_threads:
            worker.join(timeout=5)
        
        self.worker_threads.clear()
        logger.info("✅ 邮件发送调度器已停止")
    
    def get_sending_stats(self) -> Dict[str, Any]:
        """获取发送统计"""
        browser_stats = self.browser_manager.get_status_summary()
        
        return {
            'total_tasks': self.stats.total_tasks,
            'sent_success': self.stats.sent_success,
            'sent_failed': self.stats.sent_failed,
            'pending_tasks': self.task_queue.qsize(),
            'emails_per_minute': round(self.stats.emails_per_minute, 2),
            'running_time': round(time.time() - self.stats.start_time, 2) if self.stats.start_time > 0 else 0,
            'browser_stats': browser_stats,
            'is_running': self.is_running
        }
    
    def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理邮件发送调度器...")
        
        # 停止发送
        self.stop_sending()
        
        # 清理浏览器
        self.browser_manager.cleanup_browsers()
        
        logger.info("✅ 邮件发送调度器清理完成")
