#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号数据模型
定义新浪邮箱账号的数据结构和操作方法
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, List, Dict, Any
from src.models.database import DatabaseManager
from src.utils.logger import get_logger

logger = get_logger("AccountModel")


@dataclass
class Account:
    """账号数据类"""
    id: Optional[int] = None
    email: str = ""
    password: str = ""
    proxy_ip: Optional[str] = None
    proxy_port: Optional[int] = None
    proxy_user: Optional[str] = None
    proxy_pass: Optional[str] = None
    status: str = "active"  # active, disabled, error
    last_used: Optional[datetime] = None
    send_count: int = 0
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'email': self.email,
            'password': self.password,
            'proxy_ip': self.proxy_ip,
            'proxy_port': self.proxy_port,
            'proxy_user': self.proxy_user,
            'proxy_pass': self.proxy_pass,
            'status': self.status,
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'send_count': self.send_count,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Account':
        """从字典创建账号对象"""
        account = cls()
        account.id = data.get('id')
        account.email = data.get('email', '')
        account.password = data.get('password', '')
        account.proxy_ip = data.get('proxy_ip')
        account.proxy_port = data.get('proxy_port')
        account.proxy_user = data.get('proxy_user')
        account.proxy_pass = data.get('proxy_pass')
        account.status = data.get('status', 'active')
        account.send_count = data.get('send_count', 0)
        
        # 处理时间字段
        if data.get('last_used'):
            account.last_used = datetime.fromisoformat(data['last_used'])
        if data.get('create_time'):
            account.create_time = datetime.fromisoformat(data['create_time'])
        if data.get('update_time'):
            account.update_time = datetime.fromisoformat(data['update_time'])
        
        return account
    
    def has_proxy(self) -> bool:
        """检查是否配置了代理"""
        return bool(self.proxy_ip and self.proxy_port)
    
    def get_proxy_info(self) -> Optional[Dict[str, Any]]:
        """获取代理信息"""
        if not self.has_proxy():
            return None
        
        proxy_info = {
            'ip': self.proxy_ip,
            'port': self.proxy_port
        }
        
        if self.proxy_user:
            proxy_info['username'] = self.proxy_user
        if self.proxy_pass:
            proxy_info['password'] = self.proxy_pass
        
        return proxy_info


class AccountManager:
    """账号管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化账号管理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db = db_manager
    
    def add_account(self, account: Account) -> int:
        """
        添加账号
        
        Args:
            account: 账号对象
        
        Returns:
            新添加账号的ID
        """
        try:
            query = """
                INSERT INTO accounts (
                    email, password, proxy_ip, proxy_port, 
                    proxy_user, proxy_pass, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                account.email,
                account.password,
                account.proxy_ip,
                account.proxy_port,
                account.proxy_user,
                account.proxy_pass,
                account.status
            )
            
            account_id = self.db.execute_insert(query, params)
            logger.info(f"账号添加成功: {account.email}, ID: {account_id}")
            return account_id
            
        except Exception as e:
            logger.error(f"添加账号失败: {account.email}, 错误: {e}")
            raise
    
    def get_account_by_id(self, account_id: int) -> Optional[Account]:
        """
        根据ID获取账号
        
        Args:
            account_id: 账号ID
        
        Returns:
            账号对象或None
        """
        try:
            query = "SELECT * FROM accounts WHERE id = ?"
            results = self.db.execute_query(query, (account_id,))
            
            if results:
                return Account.from_dict(results[0])
            return None
            
        except Exception as e:
            logger.error(f"获取账号失败: ID {account_id}, 错误: {e}")
            return None
    
    def get_account_by_email(self, email: str) -> Optional[Account]:
        """
        根据邮箱地址获取账号
        
        Args:
            email: 邮箱地址
        
        Returns:
            账号对象或None
        """
        try:
            query = "SELECT * FROM accounts WHERE email = ?"
            results = self.db.execute_query(query, (email,))
            
            if results:
                return Account.from_dict(results[0])
            return None
            
        except Exception as e:
            logger.error(f"获取账号失败: {email}, 错误: {e}")
            return None
    
    def get_all_accounts(self, status: Optional[str] = None) -> List[Account]:
        """
        获取所有账号
        
        Args:
            status: 账号状态过滤，None表示获取所有状态
        
        Returns:
            账号列表
        """
        try:
            if status:
                query = "SELECT * FROM accounts WHERE status = ? ORDER BY create_time DESC"
                results = self.db.execute_query(query, (status,))
            else:
                query = "SELECT * FROM accounts ORDER BY create_time DESC"
                results = self.db.execute_query(query)
            
            return [Account.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"获取账号列表失败: {e}")
            return []
    
    def update_account(self, account: Account) -> bool:
        """
        更新账号信息
        
        Args:
            account: 账号对象
        
        Returns:
            是否更新成功
        """
        try:
            query = """
                UPDATE accounts SET 
                    email = ?, password = ?, proxy_ip = ?, proxy_port = ?,
                    proxy_user = ?, proxy_pass = ?, status = ?,
                    update_time = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            params = (
                account.email,
                account.password,
                account.proxy_ip,
                account.proxy_port,
                account.proxy_user,
                account.proxy_pass,
                account.status,
                account.id
            )
            
            rows_affected = self.db.execute_update(query, params)
            success = rows_affected > 0
            
            if success:
                logger.info(f"账号更新成功: {account.email}")
            else:
                logger.warning(f"账号更新失败，未找到记录: ID {account.id}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新账号失败: {account.email}, 错误: {e}")
            return False
    
    def delete_account(self, account_id: int) -> bool:
        """
        删除账号
        
        Args:
            account_id: 账号ID
        
        Returns:
            是否删除成功
        """
        try:
            query = "DELETE FROM accounts WHERE id = ?"
            rows_affected = self.db.execute_update(query, (account_id,))
            success = rows_affected > 0
            
            if success:
                logger.info(f"账号删除成功: ID {account_id}")
            else:
                logger.warning(f"账号删除失败，未找到记录: ID {account_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"删除账号失败: ID {account_id}, 错误: {e}")
            return False
    
    def update_last_used(self, account_id: int) -> bool:
        """
        更新账号最后使用时间
        
        Args:
            account_id: 账号ID
        
        Returns:
            是否更新成功
        """
        try:
            query = """
                UPDATE accounts SET 
                    last_used = CURRENT_TIMESTAMP,
                    update_time = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            rows_affected = self.db.execute_update(query, (account_id,))
            return rows_affected > 0
            
        except Exception as e:
            logger.error(f"更新账号使用时间失败: ID {account_id}, 错误: {e}")
            return False
    
    def increment_send_count(self, account_id: int) -> bool:
        """
        增加账号发送计数
        
        Args:
            account_id: 账号ID
        
        Returns:
            是否更新成功
        """
        try:
            query = """
                UPDATE accounts SET 
                    send_count = send_count + 1,
                    last_used = CURRENT_TIMESTAMP,
                    update_time = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            rows_affected = self.db.execute_update(query, (account_id,))
            return rows_affected > 0
            
        except Exception as e:
            logger.error(f"更新账号发送计数失败: ID {account_id}, 错误: {e}")
            return False
    
    def get_available_accounts(self) -> List[Account]:
        """
        获取可用的账号列表（状态为active）
        
        Returns:
            可用账号列表
        """
        return self.get_all_accounts(status='active')
    
    def batch_import_accounts(self, accounts_data: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        批量导入账号
        
        Args:
            accounts_data: 账号数据列表
        
        Returns:
            导入结果统计 {'success': 成功数量, 'failed': 失败数量, 'duplicate': 重复数量}
        """
        result = {'success': 0, 'failed': 0, 'duplicate': 0}
        
        for account_data in accounts_data:
            try:
                # 检查邮箱是否已存在
                existing_account = self.get_account_by_email(account_data.get('email', ''))
                if existing_account:
                    result['duplicate'] += 1
                    logger.warning(f"账号已存在，跳过: {account_data.get('email')}")
                    continue
                
                # 创建账号对象
                account = Account.from_dict(account_data)
                
                # 添加账号
                self.add_account(account)
                result['success'] += 1
                
            except Exception as e:
                result['failed'] += 1
                logger.error(f"导入账号失败: {account_data.get('email', 'Unknown')}, 错误: {e}")
        
        logger.info(f"批量导入完成: 成功 {result['success']}, 失败 {result['failed']}, 重复 {result['duplicate']}")
        return result
