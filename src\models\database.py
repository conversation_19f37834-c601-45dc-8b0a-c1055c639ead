#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
负责数据库连接、表创建和基本操作
"""

import sqlite3
import threading
from pathlib import Path
from typing import Optional, List, Dict, Any
from contextlib import contextmanager
from src.utils.logger import get_logger

logger = get_logger("Database")


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._local = threading.local()
        self._init_database()
    
    def _get_connection(self) -> sqlite3.Connection:
        """获取线程本地的数据库连接"""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                str(self.db_path),
                check_same_thread=False,
                timeout=30.0
            )
            self._local.connection.row_factory = sqlite3.Row
            # 启用外键约束
            self._local.connection.execute("PRAGMA foreign_keys = ON")
        
        return self._local.connection
    
    @contextmanager
    def get_cursor(self):
        """获取数据库游标的上下文管理器"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            yield cursor
            conn.commit()
        except Exception as e:
            conn.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            cursor.close()
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with self.get_cursor() as cursor:
                # 创建账号表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS accounts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email TEXT NOT NULL UNIQUE,
                        password TEXT NOT NULL,
                        proxy_ip TEXT,
                        proxy_port INTEGER,
                        proxy_user TEXT,
                        proxy_pass TEXT,
                        status TEXT DEFAULT 'active',
                        last_used DATETIME,
                        send_count INTEGER DEFAULT 0,
                        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        update_time DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建邮件模板表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS email_templates (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        template_name TEXT NOT NULL UNIQUE,
                        subject TEXT NOT NULL,
                        content TEXT NOT NULL,
                        content_type TEXT DEFAULT 'text/plain',
                        is_default BOOLEAN DEFAULT 0,
                        variables TEXT DEFAULT '',
                        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        update_time DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建发送记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS send_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        task_id TEXT,
                        from_email TEXT NOT NULL,
                        to_email TEXT NOT NULL,
                        subject TEXT,
                        content TEXT,
                        content_type TEXT DEFAULT 'text/plain',
                        status TEXT DEFAULT 'pending',
                        error_msg TEXT,
                        send_time DATETIME,
                        response_time REAL,
                        browser_id TEXT,
                        proxy_ip TEXT,
                        retry_count INTEGER DEFAULT 0,
                        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        update_time DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建代理IP表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS proxy_ips (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ip_address TEXT NOT NULL,
                        port INTEGER NOT NULL,
                        username TEXT,
                        password TEXT,
                        proxy_type TEXT DEFAULT 'http',
                        status TEXT DEFAULT 'active',
                        last_used DATETIME,
                        success_count INTEGER DEFAULT 0,
                        fail_count INTEGER DEFAULT 0,
                        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(ip_address, port)
                    )
                """)
                
                # 创建系统配置表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS system_config (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        config_key TEXT NOT NULL UNIQUE,
                        config_value TEXT,
                        description TEXT,
                        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        update_time DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建文件监控记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS file_monitor_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        file_path TEXT NOT NULL,
                        file_size INTEGER,
                        file_hash TEXT,
                        processed_count INTEGER DEFAULT 0,
                        last_processed DATETIME,
                        create_time DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_accounts_email ON accounts(email)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_accounts_status ON accounts(status)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_send_records_status ON send_records(status)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_send_records_time ON send_records(send_time)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_proxy_ips_status ON proxy_ips(status)")
                
            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """
        执行查询语句
        
        Args:
            query: SQL查询语句
            params: 查询参数
        
        Returns:
            查询结果列表
        """
        try:
            with self.get_cursor() as cursor:
                cursor.execute(query, params)
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"查询执行失败: {query}, 参数: {params}, 错误: {e}")
            raise
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """
        执行更新语句
        
        Args:
            query: SQL更新语句
            params: 更新参数
        
        Returns:
            影响的行数
        """
        try:
            with self.get_cursor() as cursor:
                cursor.execute(query, params)
                return cursor.rowcount
        except Exception as e:
            logger.error(f"更新执行失败: {query}, 参数: {params}, 错误: {e}")
            raise
    
    def execute_insert(self, query: str, params: tuple = ()) -> int:
        """
        执行插入语句
        
        Args:
            query: SQL插入语句
            params: 插入参数
        
        Returns:
            新插入记录的ID
        """
        try:
            with self.get_cursor() as cursor:
                cursor.execute(query, params)
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"插入执行失败: {query}, 参数: {params}, 错误: {e}")
            raise
    
    def close(self):
        """关闭数据库连接"""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')
        logger.info("数据库连接已关闭")
