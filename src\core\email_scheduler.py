#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件发送调度模块
负责邮件发送任务的调度、队列管理和发送状态跟踪
"""

import time
import random
import threading
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from queue import Queue, Empty
from dataclasses import dataclass
from enum import Enum
from src.core.browser_manager import BrowserManager
from src.core.sina_email_automation import SinaEmailAutomation
from src.models.account import Account, AccountManager
from src.models.email_template import EmailTemplate, EmailTemplateManager
from src.models.database import DatabaseManager
from src.utils.logger import get_logger

logger = get_logger("EmailScheduler")


class SendStatus(Enum):
    """发送状态枚举"""
    PENDING = "pending"
    SENDING = "sending"
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"


@dataclass
class EmailTask:
    """邮件发送任务"""
    id: str
    to_email: str
    subject: str
    content: str
    template_id: Optional[int] = None
    template_variables: Optional[Dict[str, str]] = None
    priority: int = 0  # 优先级，数字越大优先级越高
    max_retry: int = 3
    retry_count: int = 0
    status: SendStatus = SendStatus.PENDING
    create_time: datetime = None
    send_time: Optional[datetime] = None
    error_message: str = ""
    
    def __post_init__(self):
        if self.create_time is None:
            self.create_time = datetime.now()


class EmailScheduler:
    """邮件发送调度器"""
    
    def __init__(self, config: Dict[str, Any], db_manager: DatabaseManager):
        """
        初始化邮件调度器
        
        Args:
            config: 配置字典
            db_manager: 数据库管理器
        """
        self.config = config
        self.db_manager = db_manager
        
        # 管理器实例
        self.account_manager = AccountManager(db_manager)
        self.template_manager = EmailTemplateManager(db_manager)
        self.browser_manager = BrowserManager(config)
        self.sina_automation = SinaEmailAutomation(config, self.browser_manager)
        
        # 任务队列
        self.task_queue = Queue()
        self.failed_queue = Queue()
        
        # 发送配置
        self.email_config = config.get('email', {})
        self.send_interval_min = self.email_config.get('send_interval_min', 5)
        self.send_interval_max = self.email_config.get('send_interval_max', 15)
        self.max_retry_count = self.email_config.get('max_retry_count', 3)
        self.batch_size = self.email_config.get('batch_size', 10)
        self.daily_limit = self.email_config.get('daily_limit', 100)
        
        # 状态跟踪
        self.is_running = False
        self.worker_threads = []
        self.max_workers = config.get('performance.max_concurrent_emails', 5)
        self.current_account_index = 0
        self.daily_send_count = 0
        self.last_reset_date = datetime.now().date()
        
        # 统计信息
        self.stats = {
            'total_sent': 0,
            'total_failed': 0,
            'total_retry': 0,
            'daily_sent': 0
        }
        
        # 回调函数
        self.status_callback: Optional[Callable] = None
        self.progress_callback: Optional[Callable] = None
        
        logger.info("邮件发送调度器初始化完成")
    
    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def add_task(self, to_email: str, subject: str = None, content: str = None,
                template_id: int = None, template_variables: Dict[str, str] = None,
                priority: int = 0) -> str:
        """
        添加邮件发送任务
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题（如果使用模板则可为None）
            content: 邮件内容（如果使用模板则可为None）
            template_id: 模板ID
            template_variables: 模板变量
            priority: 优先级
        
        Returns:
            任务ID
        """
        task_id = f"task_{int(time.time())}_{random.randint(1000, 9999)}"
        
        # 如果使用模板，渲染模板内容
        if template_id:
            template = self.template_manager.get_template_by_id(template_id)
            if template:
                variables = template_variables or {}
                rendered = template.render(variables)
                subject = rendered['subject']
                content = rendered['content']
            else:
                logger.warning(f"未找到模板: {template_id}")
        
        # 如果仍然没有主题和内容，使用默认模板
        if not subject or not content:
            default_template = self.template_manager.get_default_template()
            if default_template:
                variables = template_variables or {}
                rendered = default_template.render(variables)
                subject = subject or rendered['subject']
                content = content or rendered['content']
                template_id = default_template.id
        
        task = EmailTask(
            id=task_id,
            to_email=to_email,
            subject=subject or "无主题",
            content=content or "无内容",
            template_id=template_id,
            template_variables=template_variables,
            priority=priority
        )
        
        self.task_queue.put(task)
        logger.info(f"邮件任务已添加: {task_id} -> {to_email}")
        
        return task_id
    
    def add_batch_tasks(self, email_list: List[str], template_id: int = None,
                       template_variables: Dict[str, str] = None) -> List[str]:
        """
        批量添加邮件发送任务
        
        Args:
            email_list: 邮箱列表
            template_id: 模板ID
            template_variables: 模板变量
        
        Returns:
            任务ID列表
        """
        task_ids = []
        
        for email in email_list:
            if email and "@" in email:
                task_id = self.add_task(
                    to_email=email,
                    template_id=template_id,
                    template_variables=template_variables
                )
                task_ids.append(task_id)
        
        logger.info(f"批量添加邮件任务完成: {len(task_ids)} 个任务")
        return task_ids
    
    def start(self):
        """启动邮件发送调度器"""
        if self.is_running:
            logger.warning("邮件调度器已在运行")
            return
        
        self.is_running = True
        self._reset_daily_count()
        
        # 启动工作线程
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker_thread,
                name=f"EmailWorker-{i+1}",
                daemon=True
            )
            worker.start()
            self.worker_threads.append(worker)
        
        logger.info(f"邮件发送调度器已启动，工作线程数: {self.max_workers}")
        
        if self.status_callback:
            self.status_callback("邮件调度器已启动")
    
    def stop(self):
        """停止邮件发送调度器"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 等待工作线程结束
        for worker in self.worker_threads:
            worker.join(timeout=5)
        
        self.worker_threads.clear()
        
        # 关闭所有浏览器
        self.browser_manager.close_all_drivers()
        
        logger.info("邮件发送调度器已停止")
        
        if self.status_callback:
            self.status_callback("邮件调度器已停止")
    
    def _worker_thread(self):
        """工作线程主循环"""
        thread_name = threading.current_thread().name
        logger.info(f"邮件发送工作线程启动: {thread_name}")
        
        while self.is_running:
            try:
                # 检查每日发送限制
                if self._check_daily_limit():
                    time.sleep(60)  # 达到每日限制，等待1分钟
                    continue
                
                # 获取任务
                task = self._get_next_task()
                if not task:
                    time.sleep(1)  # 没有任务，短暂等待
                    continue
                
                # 执行发送任务
                self._execute_task(task)
                
                # 发送间隔
                interval = random.uniform(self.send_interval_min, self.send_interval_max)
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"工作线程异常: {thread_name}, 错误: {e}")
                time.sleep(5)
        
        logger.info(f"邮件发送工作线程结束: {thread_name}")
    
    def _get_next_task(self) -> Optional[EmailTask]:
        """获取下一个任务"""
        try:
            # 优先处理重试任务
            if not self.failed_queue.empty():
                return self.failed_queue.get_nowait()
            
            # 获取普通任务
            return self.task_queue.get_nowait()
            
        except Empty:
            return None
    
    def _execute_task(self, task: EmailTask):
        """
        执行邮件发送任务
        
        Args:
            task: 邮件任务
        """
        task.status = SendStatus.SENDING
        task.send_time = datetime.now()
        
        try:
            # 获取可用账号
            account = self._get_next_account()
            if not account:
                raise Exception("没有可用的发送账号")
            
            # 登录账号
            login_result = self.sina_automation.login(account)
            if not login_result['success']:
                raise Exception(f"登录失败: {login_result['message']}")
            
            driver_id = login_result['driver_id']
            
            try:
                # 发送邮件
                send_result = self.sina_automation.send_email(
                    driver_id=driver_id,
                    to_email=task.to_email,
                    subject=task.subject,
                    content=task.content
                )
                
                if send_result['success']:
                    # 发送成功
                    task.status = SendStatus.SUCCESS
                    self.stats['total_sent'] += 1
                    self.stats['daily_sent'] += 1
                    self.daily_send_count += 1
                    
                    # 更新账号使用统计
                    self.account_manager.increment_send_count(account.id)
                    
                    # 记录发送记录
                    self._record_send_result(task, account.email, True, "")
                    
                    logger.info(f"邮件发送成功: {task.id} -> {task.to_email}")
                    
                else:
                    raise Exception(send_result['message'])
                
            finally:
                # 关闭浏览器
                self.browser_manager.close_driver(driver_id)
            
        except Exception as e:
            # 发送失败
            task.error_message = str(e)
            task.retry_count += 1
            
            if task.retry_count < task.max_retry:
                # 重试
                task.status = SendStatus.RETRY
                self.failed_queue.put(task)
                self.stats['total_retry'] += 1
                logger.warning(f"邮件发送失败，将重试: {task.id} -> {task.to_email}, 错误: {e}")
            else:
                # 彻底失败
                task.status = SendStatus.FAILED
                self.stats['total_failed'] += 1
                
                # 记录发送记录
                account_email = self._get_current_account_email()
                self._record_send_result(task, account_email, False, str(e))
                
                logger.error(f"邮件发送失败: {task.id} -> {task.to_email}, 错误: {e}")
        
        # 更新进度
        if self.progress_callback:
            self.progress_callback(self.stats)
    
    def _get_next_account(self) -> Optional[Account]:
        """获取下一个可用账号（轮换）"""
        accounts = self.account_manager.get_available_accounts()
        if not accounts:
            return None
        
        # 轮换选择账号
        account = accounts[self.current_account_index % len(accounts)]
        self.current_account_index += 1
        
        return account
    
    def _get_current_account_email(self) -> str:
        """获取当前账号邮箱"""
        accounts = self.account_manager.get_available_accounts()
        if accounts:
            index = (self.current_account_index - 1) % len(accounts)
            return accounts[index].email
        return "unknown"
    
    def _check_daily_limit(self) -> bool:
        """检查是否达到每日发送限制"""
        self._reset_daily_count()
        return self.daily_send_count >= self.daily_limit
    
    def _reset_daily_count(self):
        """重置每日计数"""
        today = datetime.now().date()
        if today != self.last_reset_date:
            self.daily_send_count = 0
            self.stats['daily_sent'] = 0
            self.last_reset_date = today
            logger.info("每日发送计数已重置")
    
    def _record_send_result(self, task: EmailTask, from_email: str, 
                           success: bool, error_msg: str):
        """记录发送结果到数据库"""
        try:
            query = """
                INSERT INTO send_records (
                    from_email, to_email, subject, template_id, 
                    status, error_msg
                ) VALUES (?, ?, ?, ?, ?, ?)
            """
            status = "success" if success else "failed"
            params = (
                from_email,
                task.to_email,
                task.subject,
                task.template_id,
                status,
                error_msg
            )
            
            self.db_manager.execute_insert(query, params)
            
        except Exception as e:
            logger.error(f"记录发送结果失败: {e}")
    
    def get_queue_size(self) -> Dict[str, int]:
        """获取队列大小"""
        return {
            'pending': self.task_queue.qsize(),
            'failed': self.failed_queue.qsize()
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def clear_queue(self):
        """清空任务队列"""
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
            except Empty:
                break
        
        while not self.failed_queue.empty():
            try:
                self.failed_queue.get_nowait()
            except Empty:
                break
        
        logger.info("任务队列已清空")
