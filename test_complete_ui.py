#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整界面功能测试
验证所有功能模块是否完整展现
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer
from src.gui.multi_browser_sender_widget import MultiBrowserSenderWidget


class CompleteUITestWindow(QMainWindow):
    """完整界面测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_test_data()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("🔍 完整界面功能展示测试")
        self.setGeometry(50, 50, 1600, 1000)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 添加测试说明
        info_layout = QHBoxLayout()
        
        info_label = QLabel("""
🎯 完整界面功能展示测试

检查项目：
✅ 左侧快速配置区域是否完整显示
✅ 高级设置是否默认展开并显示所有选项
✅ 账号管理区域是否正常显示
✅ 控制按钮区域是否完整
✅ 实时状态区域是否显示
✅ 右侧邮件发送区域是否有足够空间
✅ 邮件内容编辑框是否足够大
✅ 所有选项卡是否正常切换
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                border: 2px solid #3498db;
                border-radius: 8px;
                padding: 10px;
                font-size: 10pt;
                color: #2c3e50;
            }
        """)
        info_layout.addWidget(info_label)
        
        # 添加测试按钮
        test_buttons_layout = QVBoxLayout()
        
        self.test_expand_btn = QPushButton("🔧 测试高级设置展开")
        self.test_expand_btn.clicked.connect(self.test_advanced_settings)
        test_buttons_layout.addWidget(self.test_expand_btn)
        
        self.test_data_btn = QPushButton("📊 填充测试数据")
        self.test_data_btn.clicked.connect(self.fill_test_data)
        test_buttons_layout.addWidget(self.test_data_btn)
        
        self.test_tabs_btn = QPushButton("📋 测试所有选项卡")
        self.test_tabs_btn.clicked.connect(self.test_all_tabs)
        test_buttons_layout.addWidget(self.test_tabs_btn)
        
        test_buttons_layout.addStretch()
        info_layout.addLayout(test_buttons_layout)
        
        layout.addLayout(info_layout)
        
        # 创建多浏览器发送组件
        self.sender_widget = MultiBrowserSenderWidget()
        layout.addWidget(self.sender_widget)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f6fa;
            }
        """)
    
    def setup_test_data(self):
        """设置测试数据"""
        # 延迟执行，确保界面完全加载
        QTimer.singleShot(1000, self.fill_test_data)
    
    def test_advanced_settings(self):
        """测试高级设置展开"""
        try:
            if hasattr(self.sender_widget, 'advanced_group'):
                # 切换高级设置的展开状态
                current_state = self.sender_widget.advanced_group.isChecked()
                self.sender_widget.advanced_group.setChecked(not current_state)
                
                state_text = "展开" if not current_state else "折叠"
                print(f"✅ 高级设置已{state_text}")
            else:
                print("❌ 未找到高级设置组件")
        except Exception as e:
            print(f"❌ 测试高级设置失败: {e}")
    
    def fill_test_data(self):
        """填充测试数据"""
        try:
            # 填充邮件内容
            if hasattr(self.sender_widget, 'to_email_edit'):
                self.sender_widget.to_email_edit.setText("<EMAIL>;<EMAIL>;<EMAIL>")
            
            if hasattr(self.sender_widget, 'subject_edit'):
                self.sender_widget.subject_edit.setText("测试邮件主题 - {name}您好")
            
            if hasattr(self.sender_widget, 'content_edit'):
                test_content = """亲爱的 {name}，

您好！这是一封测试邮件。

我们来自 {company}，希望与您建立合作关系。

邮件内容支持多种变量：
- 姓名：{name}
- 公司：{company}
- 日期：{date}
- 时间：{time}

如果您对我们的产品感兴趣，请随时联系我们。

谢谢！

此致
敬礼！

测试团队
{date}"""
                self.sender_widget.content_edit.setPlainText(test_content)
            
            # 设置一些配置项
            if hasattr(self.sender_widget, 'max_browsers_spin'):
                self.sender_widget.max_browsers_spin.setValue(5)
            
            if hasattr(self.sender_widget, 'send_interval_spin'):
                self.sender_widget.send_interval_spin.setValue(3.0)
            
            print("✅ 测试数据填充完成")
            
        except Exception as e:
            print(f"❌ 填充测试数据失败: {e}")
    
    def test_all_tabs(self):
        """测试所有选项卡"""
        try:
            if hasattr(self.sender_widget, 'task_tab_widget'):
                tab_widget = self.sender_widget.task_tab_widget
                tab_count = tab_widget.count()
                
                print(f"📋 开始测试 {tab_count} 个选项卡...")
                
                for i in range(tab_count):
                    tab_widget.setCurrentIndex(i)
                    tab_text = tab_widget.tabText(i)
                    print(f"  ✅ 选项卡 {i+1}: {tab_text}")
                    
                    # 短暂延迟以便观察
                    QTimer.singleShot(500 * (i+1), lambda idx=i: self.switch_to_tab(idx))
                
                print("✅ 所有选项卡测试完成")
            else:
                print("❌ 未找到选项卡组件")
                
        except Exception as e:
            print(f"❌ 测试选项卡失败: {e}")
    
    def switch_to_tab(self, index):
        """切换到指定选项卡"""
        try:
            if hasattr(self.sender_widget, 'task_tab_widget'):
                self.sender_widget.task_tab_widget.setCurrentIndex(index)
        except Exception as e:
            print(f"❌ 切换选项卡失败: {e}")


def main():
    """主函数"""
    print("🔍 启动完整界面功能展示测试...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("完整界面功能测试")
    app.setApplicationVersion("2.0")
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    try:
        # 创建测试窗口
        window = CompleteUITestWindow()
        window.show()
        
        print("✅ 完整界面功能测试启动成功！")
        print("\n🔍 请仔细检查以下内容：")
        print("   1. 左侧快速配置区域是否完整显示")
        print("   2. 高级设置是否默认展开")
        print("   3. 所有配置选项是否可见")
        print("   4. 右侧邮件编辑区域是否足够大")
        print("   5. 邮件内容编辑框是否有足够空间")
        print("   6. 所有功能模块是否完整展现")
        print("\n🎮 可以使用测试按钮来验证各项功能")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
