#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发送模式管理器
管理不同的邮件发送模式和策略
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Callable
from enum import Enum
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
from src.utils.logger import get_logger

logger = get_logger("SendModeManager")


class SendMode(Enum):
    """发送模式枚举"""
    SEQUENTIAL = "sequential"    # 单个逐渐发送
    BATCH = "batch"             # 批量逐渐发送
    CONCURRENT = "concurrent"   # 并发发送
    SMART = "smart"             # 智能发送


class Priority(Enum):
    """优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    HIGHEST = "highest"


@dataclass
class EmailTask:
    """邮件任务"""
    id: str
    to_email: str
    subject: str
    content: str
    content_type: str = "text/plain"
    priority: Priority = Priority.NORMAL
    cc_emails: List[str] = None
    variables: Dict[str, str] = None
    retry_count: int = 0
    max_retries: int = 3
    created_time: float = None
    
    def __post_init__(self):
        if self.cc_emails is None:
            self.cc_emails = []
        if self.variables is None:
            self.variables = {}
        if self.created_time is None:
            self.created_time = time.time()


@dataclass
class SendModeConfig:
    """发送模式配置"""
    mode: SendMode = SendMode.SEQUENTIAL
    batch_size: int = 10
    concurrent_count: int = 5
    send_interval: float = 2.0
    priority_enabled: bool = True
    smart_throttling: bool = True
    max_retries: int = 3
    retry_interval: float = 5.0


class SendModeManager:
    """发送模式管理器"""
    
    def __init__(self, config: SendModeConfig):
        """
        初始化发送模式管理器
        
        Args:
            config: 发送模式配置
        """
        self.config = config
        self.tasks: List[EmailTask] = []
        self.completed_tasks: List[EmailTask] = []
        self.failed_tasks: List[EmailTask] = []
        self.is_running = False
        self.is_paused = False
        
        # 回调函数
        self.send_callback: Optional[Callable] = None
        self.progress_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed': 0,
            'failed': 0,
            'in_progress': 0,
            'start_time': None,
            'end_time': None
        }
    
    def set_send_callback(self, callback: Callable):
        """设置发送回调函数"""
        self.send_callback = callback
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback
    
    def add_task(self, task: EmailTask):
        """添加任务"""
        self.tasks.append(task)
        self.stats['total_tasks'] += 1
        logger.debug(f"添加任务: {task.to_email}")
    
    def add_tasks(self, tasks: List[EmailTask]):
        """批量添加任务"""
        self.tasks.extend(tasks)
        self.stats['total_tasks'] += len(tasks)
        logger.info(f"批量添加 {len(tasks)} 个任务")
    
    def clear_tasks(self):
        """清空任务"""
        self.tasks.clear()
        self.completed_tasks.clear()
        self.failed_tasks.clear()
        self.stats = {
            'total_tasks': 0,
            'completed': 0,
            'failed': 0,
            'in_progress': 0,
            'start_time': None,
            'end_time': None
        }
        logger.info("任务列表已清空")
    
    def get_pending_tasks(self) -> List[EmailTask]:
        """获取待发送任务"""
        if self.config.priority_enabled:
            # 按优先级排序
            priority_order = {
                Priority.HIGHEST: 0,
                Priority.HIGH: 1,
                Priority.NORMAL: 2,
                Priority.LOW: 3
            }
            return sorted(self.tasks, key=lambda t: (priority_order[t.priority], t.created_time))
        else:
            # 按创建时间排序
            return sorted(self.tasks, key=lambda t: t.created_time)
    
    async def start_sending(self):
        """开始发送"""
        if self.is_running:
            logger.warning("发送已在进行中")
            return
        
        if not self.tasks:
            logger.warning("没有待发送的任务")
            return
        
        self.is_running = True
        self.is_paused = False
        self.stats['start_time'] = time.time()
        
        logger.info(f"开始发送，模式: {self.config.mode.value}, 任务数: {len(self.tasks)}")
        
        try:
            if self.config.mode == SendMode.SEQUENTIAL:
                await self._send_sequential()
            elif self.config.mode == SendMode.BATCH:
                await self._send_batch()
            elif self.config.mode == SendMode.CONCURRENT:
                await self._send_concurrent()
            elif self.config.mode == SendMode.SMART:
                await self._send_smart()
        
        except Exception as e:
            logger.error(f"发送过程中出错: {e}")
        
        finally:
            self.is_running = False
            self.stats['end_time'] = time.time()
            logger.info("发送完成")
    
    async def _send_sequential(self):
        """单个逐渐发送"""
        pending_tasks = self.get_pending_tasks()
        
        for task in pending_tasks:
            if not self.is_running:
                break
            
            while self.is_paused:
                await asyncio.sleep(0.1)
            
            await self._send_single_task(task)
            await asyncio.sleep(self.config.send_interval)
    
    async def _send_batch(self):
        """批量逐渐发送"""
        pending_tasks = self.get_pending_tasks()
        
        for i in range(0, len(pending_tasks), self.config.batch_size):
            if not self.is_running:
                break
            
            while self.is_paused:
                await asyncio.sleep(0.1)
            
            batch = pending_tasks[i:i + self.config.batch_size]
            
            # 并发发送批次内的任务
            tasks = [self._send_single_task(task) for task in batch]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # 批次间间隔
            if i + self.config.batch_size < len(pending_tasks):
                await asyncio.sleep(self.config.send_interval)
    
    async def _send_concurrent(self):
        """并发发送"""
        pending_tasks = self.get_pending_tasks()
        
        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(self.config.concurrent_count)
        
        async def send_with_semaphore(task):
            async with semaphore:
                while self.is_paused:
                    await asyncio.sleep(0.1)
                return await self._send_single_task(task)
        
        # 并发发送所有任务
        tasks = [send_with_semaphore(task) for task in pending_tasks]
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _send_smart(self):
        """智能发送"""
        # 智能发送结合了多种策略
        pending_tasks = self.get_pending_tasks()
        
        # 根据任务数量和优先级动态选择策略
        if len(pending_tasks) <= 10:
            # 少量任务使用顺序发送
            await self._send_sequential()
        elif len(pending_tasks) <= 100:
            # 中等数量使用批量发送
            await self._send_batch()
        else:
            # 大量任务使用并发发送
            await self._send_concurrent()
    
    async def _send_single_task(self, task: EmailTask) -> bool:
        """发送单个任务"""
        if not self.send_callback:
            logger.error("未设置发送回调函数")
            return False
        
        self.stats['in_progress'] += 1
        self._update_progress()
        
        try:
            # 调用发送回调
            success = await self._call_send_callback(task)
            
            if success:
                self.completed_tasks.append(task)
                self.tasks.remove(task)
                self.stats['completed'] += 1
                logger.debug(f"任务发送成功: {task.to_email}")
            else:
                # 重试逻辑
                if task.retry_count < self.config.max_retries:
                    task.retry_count += 1
                    logger.warning(f"任务发送失败，准备重试 ({task.retry_count}/{self.config.max_retries}): {task.to_email}")
                    await asyncio.sleep(self.config.retry_interval)
                    return await self._send_single_task(task)
                else:
                    self.failed_tasks.append(task)
                    self.tasks.remove(task)
                    self.stats['failed'] += 1
                    logger.error(f"任务发送失败，已达最大重试次数: {task.to_email}")
            
            return success
        
        except Exception as e:
            logger.error(f"发送任务时出错: {task.to_email}, 错误: {e}")
            self.failed_tasks.append(task)
            self.tasks.remove(task)
            self.stats['failed'] += 1
            return False
        
        finally:
            self.stats['in_progress'] -= 1
            self._update_progress()
    
    async def _call_send_callback(self, task: EmailTask) -> bool:
        """调用发送回调函数"""
        try:
            if asyncio.iscoroutinefunction(self.send_callback):
                return await self.send_callback(task)
            else:
                # 在线程池中运行同步回调
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, self.send_callback, task)
        except Exception as e:
            logger.error(f"发送回调函数执行失败: {e}")
            return False
    
    def _update_progress(self):
        """更新进度"""
        if self.progress_callback:
            progress = {
                'total': self.stats['total_tasks'],
                'completed': self.stats['completed'],
                'failed': self.stats['failed'],
                'in_progress': self.stats['in_progress'],
                'pending': len(self.tasks)
            }
            
            try:
                self.progress_callback(progress)
            except Exception as e:
                logger.error(f"进度回调函数执行失败: {e}")
    
    def pause(self):
        """暂停发送"""
        self.is_paused = True
        logger.info("发送已暂停")
    
    def resume(self):
        """恢复发送"""
        self.is_paused = False
        logger.info("发送已恢复")
    
    def stop(self):
        """停止发送"""
        self.is_running = False
        self.is_paused = False
        logger.info("发送已停止")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        if stats['start_time']:
            if stats['end_time']:
                stats['duration'] = stats['end_time'] - stats['start_time']
            else:
                stats['duration'] = time.time() - stats['start_time']
            
            # 计算发送速率
            if stats['duration'] > 0:
                stats['send_rate'] = stats['completed'] / stats['duration'] * 60  # 每分钟
            else:
                stats['send_rate'] = 0
        else:
            stats['duration'] = 0
            stats['send_rate'] = 0
        
        # 计算成功率
        total_processed = stats['completed'] + stats['failed']
        if total_processed > 0:
            stats['success_rate'] = (stats['completed'] / total_processed) * 100
        else:
            stats['success_rate'] = 0
        
        return stats
