/**
 * 超高速浏览器邮件发送器 - JavaScript版本
 * 直接在浏览器中运行的高速邮件发送脚本
 */

class UltraFastEmailSender {
    constructor() {
        this.isReady = false;
        this.currentUrl = '';
        this.sendCount = 0;
        this.successCount = 0;
        this.failCount = 0;
    }

    // 日志函数
    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const prefix = {
            'info': '✅',
            'warn': '⚠️', 
            'error': '❌',
            'success': '🎉'
        }[type] || 'ℹ️';
        
        console.log(`[${timestamp}] ${prefix} ${message}`);
    }

    // 准备发送环境
    async prepareForSending() {
        this.log('🚀 准备超高速邮件发送环境...');
        
        try {
            // 方法1: 直接导航到写邮件页面
            const composeUrls = [
                'https://mail.sina.com.cn/classic/compose.php',
                'https://m0.mail.sina.com.cn/classic/compose.php',
                'https://m1.mail.sina.com.cn/classic/compose.php',
                'https://m2.mail.sina.com.cn/classic/compose.php'
            ];

            for (const url of composeUrls) {
                try {
                    this.log(`🔗 尝试访问: ${url}`);
                    window.location.href = url;
                    
                    // 等待页面加载
                    await this.waitForPageLoad();
                    
                    if (this.checkComposePage()) {
                        this.currentUrl = url;
                        this.isReady = true;
                        this.log(`✅ 写邮件页面准备完成: ${url}`, 'success');
                        return true;
                    }
                } catch (e) {
                    this.log(`⚠️ URL ${url} 访问失败: ${e.message}`, 'warn');
                    continue;
                }
            }

            // 方法2: 从当前页面查找写邮件链接
            this.log('🔄 尝试从当前页面导航到写邮件...');
            return await this.navigateFromCurrentPage();

        } catch (e) {
            this.log(`❌ 准备发送环境失败: ${e.message}`, 'error');
            return false;
        }
    }

    // 等待页面加载
    async waitForPageLoad(timeout = 5000) {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
                return;
            }

            const timer = setTimeout(() => resolve(), timeout);
            
            window.addEventListener('load', () => {
                clearTimeout(timer);
                resolve();
            }, { once: true });
        });
    }

    // 检查是否在写邮件页面
    checkComposePage() {
        const indicators = [
            'input[name="to"]',
            'input[name="subject"]',
            'textarea[name="content"]',
            'input[value*="发送"]',
            'input[type="submit"]'
        ];

        let foundCount = 0;
        for (const selector of indicators) {
            if (document.querySelector(selector)) {
                foundCount++;
            }
        }

        return foundCount >= 3;
    }

    // 从当前页面导航
    async navigateFromCurrentPage() {
        const composeSelectors = [
            'a[href*="compose"]',
            'a:contains("写邮件")',
            'a:contains("写信")',
            'button:contains("写邮件")',
            'input[value="写邮件"]'
        ];

        for (const selector of composeSelectors) {
            try {
                const element = document.querySelector(selector);
                if (element) {
                    this.log(`🔗 点击写邮件链接: ${selector}`);
                    element.click();
                    
                    await this.waitForPageLoad();
                    
                    if (this.checkComposePage()) {
                        this.currentUrl = window.location.href;
                        this.isReady = true;
                        this.log('✅ 成功导航到写邮件页面', 'success');
                        return true;
                    }
                }
            } catch (e) {
                this.log(`⚠️ 导航失败: ${selector} - ${e.message}`, 'warn');
                continue;
            }
        }

        return false;
    }

    // 超高速发送邮件
    async sendEmailUltraFast(toEmail, subject, content, contentType = 'text/plain') {
        if (!this.isReady) {
            this.log('⚠️ 发送器未准备就绪，尝试重新准备...', 'warn');
            if (!(await this.prepareForSending())) {
                return false;
            }
        }

        this.log(`⚡ 超高速发送邮件: ${toEmail}`);
        const startTime = Date.now();

        try {
            // 策略1: 直接DOM操作 (最快)
            if (await this.sendWithDirectDOM(toEmail, subject, content)) {
                const elapsed = (Date.now() - startTime) / 1000;
                this.log(`✅ 直接DOM发送成功 (${elapsed.toFixed(2)}秒)`, 'success');
                this.successCount++;
                return true;
            }

            // 策略2: 表单填写
            if (await this.sendWithFormFill(toEmail, subject, content)) {
                const elapsed = (Date.now() - startTime) / 1000;
                this.log(`✅ 表单填写发送成功 (${elapsed.toFixed(2)}秒)`, 'success');
                this.successCount++;
                return true;
            }

            // 策略3: 事件模拟
            if (await this.sendWithEventSimulation(toEmail, subject, content)) {
                const elapsed = (Date.now() - startTime) / 1000;
                this.log(`✅ 事件模拟发送成功 (${elapsed.toFixed(2)}秒)`, 'success');
                this.successCount++;
                return true;
            }

            this.log('❌ 所有发送策略都失败了', 'error');
            this.failCount++;
            return false;

        } catch (e) {
            const elapsed = (Date.now() - startTime) / 1000;
            this.log(`❌ 超高速发送失败: ${e.message} (${elapsed.toFixed(2)}秒)`, 'error');
            this.failCount++;
            return false;
        } finally {
            this.sendCount++;
        }
    }

    // 直接DOM操作发送
    async sendWithDirectDOM(toEmail, subject, content) {
        try {
            this.log('⚡ 尝试直接DOM操作发送...');

            // 填写收件人
            const toField = this.findElement([
                'input[name="to"]',
                'input[id*="to"]',
                'input[placeholder*="收件人"]'
            ]);

            if (toField) {
                toField.value = toEmail;
                toField.dispatchEvent(new Event('input', { bubbles: true }));
                toField.dispatchEvent(new Event('change', { bubbles: true }));
            } else {
                this.log('⚠️ 未找到收件人字段', 'warn');
                return false;
            }

            // 填写主题
            const subjectField = this.findElement([
                'input[name="subject"]',
                'input[id*="subject"]',
                'input[placeholder*="主题"]'
            ]);

            if (subjectField) {
                subjectField.value = subject;
                subjectField.dispatchEvent(new Event('input', { bubbles: true }));
                subjectField.dispatchEvent(new Event('change', { bubbles: true }));
            }

            // 填写内容
            const contentField = this.findElement([
                'textarea[name="content"]',
                'textarea[id*="content"]',
                'div[contenteditable="true"]'
            ]);

            if (contentField) {
                if (contentField.tagName === 'TEXTAREA') {
                    contentField.value = content;
                    contentField.dispatchEvent(new Event('input', { bubbles: true }));
                } else {
                    contentField.innerHTML = content;
                    contentField.dispatchEvent(new Event('input', { bubbles: true }));
                }
            }

            // 等待一下然后发送
            await this.sleep(500);

            // 发送邮件
            const sendButton = this.findElement([
                'input[value*="发送"]',
                'input[type="submit"]',
                'button[type="submit"]',
                'button[onclick*="send"]'
            ]);

            if (sendButton) {
                sendButton.click();
                await this.sleep(2000);
                return this.checkSendSuccess();
            } else {
                this.log('⚠️ 未找到发送按钮', 'warn');
                return false;
            }

        } catch (e) {
            this.log(`❌ 直接DOM操作失败: ${e.message}`, 'error');
            return false;
        }
    }

    // 表单填写发送
    async sendWithFormFill(toEmail, subject, content) {
        try {
            this.log('⚡ 尝试表单填写发送...');

            // 查找表单
            const form = document.querySelector('form');
            if (!form) {
                this.log('⚠️ 未找到表单', 'warn');
                return false;
            }

            // 填写表单数据
            const formData = new FormData(form);
            formData.set('to', toEmail);
            formData.set('subject', subject);
            formData.set('content', content);

            // 提交表单
            if (form.action) {
                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    await this.sleep(1000);
                    return this.checkSendSuccess();
                }
            }

            return false;

        } catch (e) {
            this.log(`❌ 表单填写发送失败: ${e.message}`, 'error');
            return false;
        }
    }

    // 事件模拟发送
    async sendWithEventSimulation(toEmail, subject, content) {
        try {
            this.log('⚡ 尝试事件模拟发送...');

            // 模拟用户输入
            const toField = this.findElement(['input[name="to"]']);
            if (toField) {
                this.simulateUserInput(toField, toEmail);
            }

            const subjectField = this.findElement(['input[name="subject"]']);
            if (subjectField) {
                this.simulateUserInput(subjectField, subject);
            }

            const contentField = this.findElement(['textarea[name="content"]']);
            if (contentField) {
                this.simulateUserInput(contentField, content);
            }

            await this.sleep(1000);

            // 模拟点击发送
            const sendButton = this.findElement(['input[type="submit"]']);
            if (sendButton) {
                this.simulateClick(sendButton);
                await this.sleep(2000);
                return this.checkSendSuccess();
            }

            return false;

        } catch (e) {
            this.log(`❌ 事件模拟发送失败: ${e.message}`, 'error');
            return false;
        }
    }

    // 查找元素
    findElement(selectors) {
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && element.offsetParent !== null) {
                return element;
            }
        }
        return null;
    }

    // 模拟用户输入
    simulateUserInput(element, value) {
        element.focus();
        element.value = '';
        
        for (const char of value) {
            element.value += char;
            element.dispatchEvent(new KeyboardEvent('keydown', { key: char }));
            element.dispatchEvent(new KeyboardEvent('keypress', { key: char }));
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new KeyboardEvent('keyup', { key: char }));
        }
        
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.blur();
    }

    // 模拟点击
    simulateClick(element) {
        element.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
        element.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
        element.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    }

    // 检查发送成功
    checkSendSuccess() {
        const currentUrl = window.location.href.toLowerCase();
        const pageSource = document.body.innerText.toLowerCase();

        const successIndicators = [
            '发送成功', '已发送', 'sent successfully', 'message sent',
            '邮件已发送', '发送完成'
        ];

        const errorIndicators = [
            '发送失败', 'send failed', 'error', '错误'
        ];

        const hasSuccess = successIndicators.some(indicator => pageSource.includes(indicator));
        const hasError = errorIndicators.some(indicator => pageSource.includes(indicator));
        const urlSuccess = ['sent', 'success', 'complete'].some(keyword => currentUrl.includes(keyword));

        if (hasSuccess || (urlSuccess && !hasError)) {
            this.log('✅ 邮件发送成功确认', 'success');
            return true;
        } else if (hasError) {
            this.log('⚠️ 检测到发送错误', 'warn');
            return false;
        } else {
            this.log('🤔 无明确指标，假设发送成功');
            return true;
        }
    }

    // 为下一封邮件重置
    async resetForNextEmail() {
        try {
            if (this.currentUrl) {
                window.location.href = this.currentUrl;
                await this.waitForPageLoad();
                return this.checkComposePage();
            } else {
                return await this.prepareForSending();
            }
        } catch (e) {
            this.log(`❌ 重置状态失败: ${e.message}`, 'error');
            return false;
        }
    }

    // 获取统计信息
    getStats() {
        return {
            sendCount: this.sendCount,
            successCount: this.successCount,
            failCount: this.failCount,
            successRate: this.sendCount > 0 ? (this.successCount / this.sendCount * 100).toFixed(1) : 0
        };
    }

    // 睡眠函数
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 全局实例
window.ultraFastSender = new UltraFastEmailSender();

// 快速发送函数
window.quickSend = async function(toEmail, subject, content) {
    return await window.ultraFastSender.sendEmailUltraFast(toEmail, subject, content);
};

// 批量发送函数
window.batchSend = async function(emailList, interval = 2000) {
    console.log(`🚀 开始批量发送 ${emailList.length} 封邮件...`);
    
    const results = [];
    for (let i = 0; i < emailList.length; i++) {
        const { to, subject, content } = emailList[i];
        console.log(`📧 发送第 ${i + 1} 封邮件: ${to}`);
        
        const success = await window.ultraFastSender.sendEmailUltraFast(to, subject, content);
        results.push({ to, success });
        
        if (i < emailList.length - 1) {
            await window.ultraFastSender.resetForNextEmail();
            await window.ultraFastSender.sleep(interval);
        }
    }
    
    const stats = window.ultraFastSender.getStats();
    console.log(`🎉 批量发送完成! 成功: ${stats.successCount}, 失败: ${stats.failCount}, 成功率: ${stats.successRate}%`);
    
    return results;
};

console.log('⚡ 超高速邮件发送器已加载! 使用 quickSend(email, subject, content) 快速发送邮件');
console.log('📧 使用 batchSend([{to, subject, content}, ...]) 批量发送邮件');
