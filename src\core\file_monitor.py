#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件监控模块
监控指定文件夹下的txt文件变化，自动提取QQ号码并转换为邮箱地址
"""

import os
import re
import time
import hashlib
import threading
from pathlib import Path
from typing import Set, List, Dict, Any, Optional, Callable
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from src.utils.logger import get_logger
from src.models.database import DatabaseManager

logger = get_logger("FileMonitor")


class QQNumberExtractor:
    """QQ号码提取器"""
    
    def __init__(self):
        # QQ号码正则表达式（5-11位数字，不以0开头）
        self.qq_pattern = re.compile(r'\b[1-9]\d{4,10}\b')
        # 邮箱正则表达式（用于排除已经是邮箱格式的）
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
    
    def extract_qq_numbers(self, text: str) -> Set[str]:
        """
        从文本中提取QQ号码
        
        Args:
            text: 要处理的文本
        
        Returns:
            QQ号码集合
        """
        qq_numbers = set()
        
        # 先移除已有的邮箱地址，避免误提取
        text_without_emails = self.email_pattern.sub('', text)
        
        # 提取QQ号码
        matches = self.qq_pattern.findall(text_without_emails)
        
        for match in matches:
            # 验证QQ号码的有效性
            if self._is_valid_qq_number(match):
                qq_numbers.add(match)
        
        return qq_numbers
    
    def _is_valid_qq_number(self, qq_number: str) -> bool:
        """
        验证QQ号码的有效性
        
        Args:
            qq_number: QQ号码字符串
        
        Returns:
            是否有效
        """
        try:
            num = int(qq_number)
            # QQ号码范围：10000 - 99999999999
            return 10000 <= num <= 99999999999
        except ValueError:
            return False
    
    def qq_to_email(self, qq_number: str) -> str:
        """
        将QQ号码转换为QQ邮箱
        
        Args:
            qq_number: QQ号码
        
        Returns:
            QQ邮箱地址
        """
        return f"{qq_number}@qq.com"


class FileChangeHandler(FileSystemEventHandler):
    """文件变化处理器"""
    
    def __init__(self, monitor: 'FileMonitor'):
        """
        初始化文件变化处理器
        
        Args:
            monitor: 文件监控器实例
        """
        self.monitor = monitor
        super().__init__()
    
    def on_modified(self, event):
        """文件修改事件"""
        if not event.is_directory:
            self.monitor._handle_file_change(event.src_path, 'modified')
    
    def on_created(self, event):
        """文件创建事件"""
        if not event.is_directory:
            self.monitor._handle_file_change(event.src_path, 'created')
    
    def on_moved(self, event):
        """文件移动事件"""
        if not event.is_directory:
            self.monitor._handle_file_change(event.dest_path, 'moved')


class FileMonitor:
    """文件监控器"""
    
    def __init__(self, config: Dict[str, Any], db_manager: DatabaseManager):
        """
        初始化文件监控器
        
        Args:
            config: 配置字典
            db_manager: 数据库管理器
        """
        self.config = config
        self.db_manager = db_manager
        
        # 监控配置
        self.monitor_config = config.get('file_monitor', {})
        self.enabled = self.monitor_config.get('enabled', True)
        self.check_interval = self.monitor_config.get('check_interval', 5)
        self.supported_extensions = self.monitor_config.get('supported_extensions', ['.txt', '.csv'])
        self.encoding = self.monitor_config.get('encoding', 'utf-8')
        self.backup_processed = self.monitor_config.get('backup_processed_files', True)
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_paths: Set[str] = set()
        self.observers: List[Observer] = []
        self.processed_files: Dict[str, Dict[str, Any]] = {}
        
        # 工具类
        self.qq_extractor = QQNumberExtractor()
        
        # 回调函数
        self.new_emails_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None
        
        # 线程锁
        self.lock = threading.Lock()
        
        logger.info("文件监控器初始化完成")
    
    def set_new_emails_callback(self, callback: Callable[[List[str]], None]):
        """
        设置新邮箱回调函数
        
        Args:
            callback: 回调函数，参数为新邮箱列表
        """
        self.new_emails_callback = callback
    
    def set_status_callback(self, callback: Callable[[str], None]):
        """
        设置状态回调函数
        
        Args:
            callback: 回调函数，参数为状态消息
        """
        self.status_callback = callback
    
    def add_monitor_path(self, path: str) -> bool:
        """
        添加监控路径
        
        Args:
            path: 要监控的文件夹路径
        
        Returns:
            是否添加成功
        """
        try:
            path = Path(path).resolve()
            
            if not path.exists():
                logger.error(f"监控路径不存在: {path}")
                return False
            
            if not path.is_dir():
                logger.error(f"监控路径不是文件夹: {path}")
                return False
            
            path_str = str(path)
            
            with self.lock:
                if path_str in self.monitor_paths:
                    logger.warning(f"监控路径已存在: {path_str}")
                    return True
                
                self.monitor_paths.add(path_str)
                
                # 如果正在监控，立即添加观察器
                if self.is_monitoring:
                    self._add_observer(path_str)
            
            logger.info(f"监控路径添加成功: {path_str}")
            return True
            
        except Exception as e:
            logger.error(f"添加监控路径失败: {path}, 错误: {e}")
            return False
    
    def remove_monitor_path(self, path: str) -> bool:
        """
        移除监控路径
        
        Args:
            path: 要移除的文件夹路径
        
        Returns:
            是否移除成功
        """
        try:
            path_str = str(Path(path).resolve())
            
            with self.lock:
                if path_str not in self.monitor_paths:
                    logger.warning(f"监控路径不存在: {path_str}")
                    return False
                
                self.monitor_paths.remove(path_str)
                
                # 移除对应的观察器
                self.observers = [obs for obs in self.observers 
                                if obs.emitters[0].watch.path != path_str]
            
            logger.info(f"监控路径移除成功: {path_str}")
            return True
            
        except Exception as e:
            logger.error(f"移除监控路径失败: {path}, 错误: {e}")
            return False
    
    def start_monitoring(self) -> bool:
        """
        开始监控
        
        Returns:
            是否启动成功
        """
        if not self.enabled:
            logger.warning("文件监控功能已禁用")
            return False
        
        if self.is_monitoring:
            logger.warning("文件监控已在运行")
            return True
        
        if not self.monitor_paths:
            logger.warning("没有配置监控路径")
            return False
        
        try:
            # 创建观察器
            for path in self.monitor_paths:
                self._add_observer(path)
            
            # 启动所有观察器
            for observer in self.observers:
                observer.start()
            
            self.is_monitoring = True
            logger.info("文件监控已启动")
            
            if self.status_callback:
                self.status_callback("文件监控已启动")
            
            # 初始扫描现有文件
            self._initial_scan()
            
            return True
            
        except Exception as e:
            logger.error(f"启动文件监控失败: {e}")
            self.stop_monitoring()
            return False
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        try:
            # 停止所有观察器
            for observer in self.observers:
                observer.stop()
                observer.join(timeout=5)
            
            self.observers.clear()
            self.is_monitoring = False
            
            logger.info("文件监控已停止")
            
            if self.status_callback:
                self.status_callback("文件监控已停止")
            
        except Exception as e:
            logger.error(f"停止文件监控失败: {e}")
    
    def _add_observer(self, path: str):
        """添加观察器"""
        try:
            observer = Observer()
            event_handler = FileChangeHandler(self)
            observer.schedule(event_handler, path, recursive=True)
            self.observers.append(observer)
            logger.debug(f"观察器添加成功: {path}")
        except Exception as e:
            logger.error(f"添加观察器失败: {path}, 错误: {e}")
    
    def _initial_scan(self):
        """初始扫描现有文件"""
        logger.info("开始初始文件扫描")
        
        for path in self.monitor_paths:
            try:
                self._scan_directory(Path(path))
            except Exception as e:
                logger.error(f"扫描目录失败: {path}, 错误: {e}")
        
        logger.info("初始文件扫描完成")
    
    def _scan_directory(self, directory: Path):
        """扫描目录中的文件"""
        for file_path in directory.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_extensions:
                self._handle_file_change(str(file_path), 'scan')
    
    def _handle_file_change(self, file_path: str, event_type: str):
        """
        处理文件变化
        
        Args:
            file_path: 文件路径
            event_type: 事件类型
        """
        try:
            file_path = Path(file_path)
            
            # 检查文件扩展名
            if file_path.suffix.lower() not in self.supported_extensions:
                return
            
            # 检查文件是否存在
            if not file_path.exists():
                return
            
            # 获取文件信息
            file_info = self._get_file_info(file_path)
            file_key = str(file_path)
            
            # 检查文件是否已处理过
            if file_key in self.processed_files:
                old_info = self.processed_files[file_key]
                if (old_info['size'] == file_info['size'] and 
                    old_info['hash'] == file_info['hash']):
                    return  # 文件未变化，跳过
            
            # 处理文件
            new_emails = self._process_file(file_path)
            
            if new_emails:
                logger.info(f"从文件提取到 {len(new_emails)} 个新邮箱: {file_path.name}")
                
                # 更新处理记录
                self.processed_files[file_key] = file_info
                self._record_file_processing(file_path, len(new_emails))
                
                # 回调通知
                if self.new_emails_callback:
                    self.new_emails_callback(new_emails)
                
                # 备份文件
                if self.backup_processed:
                    self._backup_file(file_path)
            
        except Exception as e:
            logger.error(f"处理文件变化失败: {file_path}, 错误: {e}")
    
    def _get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """获取文件信息"""
        stat = file_path.stat()
        
        # 计算文件哈希
        with open(file_path, 'rb') as f:
            file_hash = hashlib.md5(f.read()).hexdigest()
        
        return {
            'size': stat.st_size,
            'mtime': stat.st_mtime,
            'hash': file_hash
        }
    
    def _process_file(self, file_path: Path) -> List[str]:
        """
        处理文件，提取QQ号码并转换为邮箱
        
        Args:
            file_path: 文件路径
        
        Returns:
            新邮箱列表
        """
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding=self.encoding, errors='ignore') as f:
                content = f.read()
            
            # 提取QQ号码
            qq_numbers = self.qq_extractor.extract_qq_numbers(content)
            
            if not qq_numbers:
                return []
            
            # 转换为邮箱
            new_emails = []
            for qq in qq_numbers:
                email = self.qq_extractor.qq_to_email(qq)
                new_emails.append(email)
            
            logger.debug(f"从文件 {file_path.name} 提取到 {len(qq_numbers)} 个QQ号码")
            return new_emails
            
        except Exception as e:
            logger.error(f"处理文件失败: {file_path}, 错误: {e}")
            return []
    
    def _record_file_processing(self, file_path: Path, processed_count: int):
        """记录文件处理结果到数据库"""
        try:
            query = """
                INSERT OR REPLACE INTO file_monitor_records (
                    file_path, file_size, file_hash, processed_count, last_processed
                ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            """
            
            file_info = self._get_file_info(file_path)
            params = (
                str(file_path),
                file_info['size'],
                file_info['hash'],
                processed_count
            )
            
            self.db_manager.execute_insert(query, params)
            
        except Exception as e:
            logger.error(f"记录文件处理结果失败: {e}")
    
    def _backup_file(self, file_path: Path):
        """备份已处理的文件"""
        try:
            # 创建备份目录
            backup_dir = file_path.parent / "processed_backup"
            backup_dir.mkdir(exist_ok=True)
            
            # 生成备份文件名
            timestamp = int(time.time())
            backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
            backup_path = backup_dir / backup_name
            
            # 复制文件
            import shutil
            shutil.copy2(file_path, backup_path)
            
            logger.debug(f"文件备份成功: {backup_path}")
            
        except Exception as e:
            logger.error(f"备份文件失败: {file_path}, 错误: {e}")
    
    def get_monitor_paths(self) -> List[str]:
        """获取监控路径列表"""
        return list(self.monitor_paths)
    
    def get_processed_files(self) -> Dict[str, Dict[str, Any]]:
        """获取已处理文件信息"""
        return self.processed_files.copy()
    
    def is_running(self) -> bool:
        """检查是否正在监控"""
        return self.is_monitoring
