#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入模板管理界面组件
提供导入模板的管理和下载功能
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QGroupBox, QComboBox, QTextEdit, QMessageBox,
    QFileDialog, QDialog, QFormLayout, QDialogButtonBox, QSplitter,
    QHeaderView, QTabWidget
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from typing import List, Dict, Any, Optional
from src.core.import_template_manager import ImportTemplateManager, ImportTemplate
from src.utils.logger import get_logger

logger = get_logger("ImportTemplateWidget")


class TemplatePreviewDialog(QDialog):
    """模板预览对话框"""
    
    def __init__(self, parent=None, template: ImportTemplate = None):
        super().__init__(parent)
        self.template = template
        
        self.init_ui()
        if self.template:
            self.load_template_preview()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("模板预览")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout()
        
        # 模板信息
        if self.template:
            info_text = f"""
模板名称: {self.template.name}
描述: {self.template.description}
文件格式: {self.template.file_format.upper()}
编码: {self.template.encoding}
包含表头: {'是' if self.template.has_header else '否'}
            """.strip()
            
            info_label = QLabel(info_text)
            info_label.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
            layout.addWidget(info_label)
        
        # 选项卡
        tab_widget = QTabWidget()
        
        # 字段定义选项卡
        fields_tab = QWidget()
        fields_layout = QVBoxLayout()
        
        self.fields_table = QTableWidget()
        self.fields_table.setColumnCount(4)
        self.fields_table.setHorizontalHeaderLabels(["字段名", "显示名称", "类型", "必填"])
        self.fields_table.horizontalHeader().setStretchLastSection(True)
        fields_layout.addWidget(self.fields_table)
        
        fields_tab.setLayout(fields_layout)
        tab_widget.addTab(fields_tab, "字段定义")
        
        # 示例数据选项卡
        sample_tab = QWidget()
        sample_layout = QVBoxLayout()
        
        self.sample_text = QTextEdit()
        self.sample_text.setReadOnly(True)
        self.sample_text.setFont(QFont("Consolas", 10))
        sample_layout.addWidget(self.sample_text)
        
        sample_tab.setLayout(sample_layout)
        tab_widget.addTab(sample_tab, "示例数据")
        
        layout.addWidget(tab_widget)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.download_btn = QPushButton("下载模板")
        self.download_btn.clicked.connect(self.download_template)
        button_layout.addWidget(self.download_btn)
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def load_template_preview(self):
        """加载模板预览"""
        if not self.template:
            return
        
        # 加载字段定义
        self.fields_table.setRowCount(len(self.template.columns))
        
        for row, col in enumerate(self.template.columns):
            self.fields_table.setItem(row, 0, QTableWidgetItem(col['name']))
            self.fields_table.setItem(row, 1, QTableWidgetItem(col['display_name']))
            self.fields_table.setItem(row, 2, QTableWidgetItem(col.get('type', 'text')))
            self.fields_table.setItem(row, 3, QTableWidgetItem('是' if col.get('required', False) else '否'))
        
        # 加载示例数据
        if self.template.file_format == "csv":
            sample_text = self._generate_csv_sample()
        elif self.template.file_format == "json":
            sample_text = self._generate_json_sample()
        elif self.template.file_format == "txt":
            sample_text = self._generate_txt_sample()
        else:
            sample_text = "暂不支持此格式的预览"
        
        self.sample_text.setPlainText(sample_text)
    
    def _generate_csv_sample(self) -> str:
        """生成CSV示例"""
        lines = []
        
        if self.template.has_header:
            # 表头
            headers = [col['display_name'] for col in self.template.columns]
            lines.append(self.template.delimiter.join(headers))
        
        # 示例数据
        for sample in self.template.sample_data[:3]:  # 只显示前3行
            row_data = []
            for col in self.template.columns:
                value = sample.get(col['name'], '')
                row_data.append(str(value))
            lines.append(self.template.delimiter.join(row_data))
        
        return '\n'.join(lines)
    
    def _generate_json_sample(self) -> str:
        """生成JSON示例"""
        import json
        sample_data = self.template.sample_data[:3]  # 只显示前3行
        return json.dumps(sample_data, ensure_ascii=False, indent=2)
    
    def _generate_txt_sample(self) -> str:
        """生成TXT示例"""
        lines = []
        
        if self.template.has_header:
            lines.append(f"# {self.template.name}")
            lines.append(f"# {self.template.description}")
            lines.append("# 每行一个邮箱地址")
            lines.append("")
        
        # 示例数据
        for sample in self.template.sample_data[:5]:  # 显示前5行
            if 'email' in sample:
                lines.append(sample['email'])
        
        return '\n'.join(lines)
    
    def download_template(self):
        """下载模板"""
        if not self.template:
            return
        
        # 选择保存路径
        file_extension = self.template.file_format
        filter_text = f"{file_extension.upper()} Files (*.{file_extension})"
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存模板文件", 
            f"{self.template.name}.{file_extension}",
            filter_text
        )
        
        if file_path:
            try:
                template_manager = ImportTemplateManager()
                template_manager.create_template_file(self.template, file_path)
                
                QMessageBox.information(
                    self, "成功", 
                    f"模板文件已保存到: {file_path}"
                )
                
            except Exception as e:
                QMessageBox.critical(
                    self, "错误", 
                    f"保存模板文件失败: {e}"
                )


class ImportTemplateWidget(QWidget):
    """导入模板管理主界面"""
    
    # 信号定义
    template_selected = pyqtSignal(ImportTemplate)
    
    def __init__(self):
        super().__init__()
        self.template_manager = ImportTemplateManager()
        
        self.init_ui()
        self.load_templates()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("导入模板管理")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        toolbar_layout.addWidget(QLabel("文件格式:"))
        self.format_filter = QComboBox()
        self.format_filter.addItem("全部", None)
        self.format_filter.addItems(["csv", "xlsx", "json", "txt"])
        self.format_filter.currentIndexChanged.connect(self.filter_templates)
        toolbar_layout.addWidget(self.format_filter)
        
        toolbar_layout.addStretch()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_templates)
        toolbar_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 模板列表
        self.template_table = QTableWidget()
        self.template_table.setColumnCount(4)
        self.template_table.setHorizontalHeaderLabels([
            "模板名称", "描述", "文件格式", "字段数量"
        ])
        
        # 设置列宽
        header = self.template_table.horizontalHeader()
        header.resizeSection(0, 200)  # 模板名称
        header.resizeSection(1, 300)  # 描述
        header.resizeSection(2, 100)  # 文件格式
        header.setStretchLastSection(True)  # 字段数量
        
        self.template_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.template_table.setAlternatingRowColors(True)
        self.template_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.template_table.itemDoubleClicked.connect(self.preview_template)
        
        layout.addWidget(self.template_table)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.preview_btn = QPushButton("预览模板")
        self.preview_btn.clicked.connect(self.preview_template)
        self.preview_btn.setEnabled(False)
        button_layout.addWidget(self.preview_btn)
        
        self.download_btn = QPushButton("下载模板")
        self.download_btn.clicked.connect(self.download_template)
        self.download_btn.setEnabled(False)
        button_layout.addWidget(self.download_btn)
        
        self.use_template_btn = QPushButton("使用模板")
        self.use_template_btn.clicked.connect(self.use_template)
        self.use_template_btn.setEnabled(False)
        button_layout.addWidget(self.use_template_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 模板信息显示
        info_group = QGroupBox("模板信息")
        info_layout = QVBoxLayout()
        
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        self.info_text.setMaximumHeight(150)
        self.info_text.setPlaceholderText("选择模板查看详细信息...")
        info_layout.addWidget(self.info_text)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        self.setLayout(layout)
    
    def load_templates(self):
        """加载模板列表"""
        try:
            templates = self.template_manager.get_all_templates()
            self.update_template_table(templates)
            logger.info(f"加载了 {len(templates)} 个导入模板")
        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            QMessageBox.critical(self, "错误", f"加载模板失败: {e}")
    
    def update_template_table(self, templates: List[ImportTemplate]):
        """更新模板表格"""
        self.template_table.setRowCount(len(templates))
        
        for row, template in enumerate(templates):
            # 模板名称
            self.template_table.setItem(row, 0, QTableWidgetItem(template.name))
            
            # 描述
            self.template_table.setItem(row, 1, QTableWidgetItem(template.description))
            
            # 文件格式
            format_item = QTableWidgetItem(template.file_format.upper())
            self.template_table.setItem(row, 2, format_item)
            
            # 字段数量
            self.template_table.setItem(row, 3, QTableWidgetItem(str(len(template.columns))))
            
            # 存储模板对象
            self.template_table.item(row, 0).setData(Qt.UserRole, template)
    
    def on_selection_changed(self):
        """选择改变时的处理"""
        selected_rows = self.template_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0
        
        self.preview_btn.setEnabled(has_selection)
        self.download_btn.setEnabled(has_selection)
        self.use_template_btn.setEnabled(has_selection)
        
        if has_selection:
            row = selected_rows[0].row()
            template = self.template_table.item(row, 0).data(Qt.UserRole)
            if template:
                self.show_template_info(template)
        else:
            self.info_text.clear()
    
    def show_template_info(self, template: ImportTemplate):
        """显示模板信息"""
        info = self.template_manager.get_template_info(template)
        self.info_text.setPlainText(info)
    
    def filter_templates(self):
        """过滤模板"""
        format_filter = self.format_filter.currentData()
        
        if format_filter:
            templates = self.template_manager.get_templates_by_format(format_filter)
        else:
            templates = self.template_manager.get_all_templates()
        
        self.update_template_table(templates)
    
    def preview_template(self):
        """预览模板"""
        selected_rows = self.template_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        template = self.template_table.item(row, 0).data(Qt.UserRole)
        
        if template:
            dialog = TemplatePreviewDialog(self, template)
            dialog.exec_()
    
    def download_template(self):
        """下载模板"""
        selected_rows = self.template_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        template = self.template_table.item(row, 0).data(Qt.UserRole)
        
        if template:
            # 选择保存路径
            file_extension = template.file_format
            filter_text = f"{file_extension.upper()} Files (*.{file_extension})"
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存模板文件", 
                f"{template.name}.{file_extension}",
                filter_text
            )
            
            if file_path:
                try:
                    self.template_manager.create_template_file(template, file_path)
                    
                    QMessageBox.information(
                        self, "成功", 
                        f"模板文件已保存到: {file_path}\n\n"
                        f"使用说明:\n"
                        f"1. 按照模板格式填写数据\n"
                        f"2. 保存文件后在导入功能中选择该文件\n"
                        f"3. 系统会自动识别格式并导入数据"
                    )
                    
                except Exception as e:
                    logger.error(f"下载模板失败: {e}")
                    QMessageBox.critical(self, "错误", f"下载模板失败: {e}")
    
    def use_template(self):
        """使用模板"""
        selected_rows = self.template_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        template = self.template_table.item(row, 0).data(Qt.UserRole)
        
        if template:
            self.template_selected.emit(template)
            QMessageBox.information(
                self, "成功", 
                f"已选择模板: {template.name}\n"
                f"您可以根据此模板格式准备数据文件进行导入。"
            )
