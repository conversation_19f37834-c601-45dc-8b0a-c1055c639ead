#!/usr/bin/env python3
"""
多浏览器Cookie复用发送器测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import time
from src.core.email_sending_scheduler import EmailSendingScheduler, SendingConfig
from src.models.account import Account
from src.utils.logger import setup_logger

logger = setup_logger("INFO")

def test_multi_browser_sender():
    """测试多浏览器发送器"""
    print("🧪 多浏览器Cookie复用发送器测试")
    print("="*60)
    
    # 创建发送配置
    config = SendingConfig(
        max_browsers=2,              # 2个浏览器窗口
        emails_per_account=3,        # 每个账号发送3封邮件
        send_interval=2.0,           # 发送间隔2秒
        browser_window_width=600,    # 窗口宽度
        browser_window_height=400,   # 窗口高度
        minimize_browsers=True,      # 最小化浏览器
        rotate_accounts=True,        # 轮换账号
        max_retries=2               # 最大重试次数
    )
    
    print(f"📊 配置信息:")
    print(f"   浏览器数量: {config.max_browsers}")
    print(f"   每账号发送: {config.emails_per_account} 封")
    print(f"   发送间隔: {config.send_interval} 秒")
    print(f"   窗口大小: {config.browser_window_width}x{config.browser_window_height}")
    print(f"   最小化浏览器: {config.minimize_browsers}")
    print()
    
    # 创建测试账号
    accounts = [
        Account(email="<EMAIL>", password="your_password", status="active"),
        Account(email="<EMAIL>", password="your_password", status="active"),
        Account(email="<EMAIL>", password="your_password", status="active"),
    ]
    
    print(f"👥 测试账号: {len(accounts)} 个")
    for account in accounts:
        print(f"   - {account.email}")
    print()
    
    # 创建调度器
    scheduler = EmailSendingScheduler(config)
    
    try:
        # 初始化调度器
        print("🚀 初始化多浏览器发送器...")
        if not scheduler.initialize(accounts):
            print("❌ 初始化失败")
            return
        
        print("✅ 初始化成功")
        print()
        
        # 添加测试邮件任务
        print("📝 添加测试邮件任务...")
        
        test_emails = [
            ("<EMAIL>", "多浏览器测试邮件1", "这是第一封测试邮件"),
            ("<EMAIL>", "多浏览器测试邮件2", "这是第二封测试邮件"),
            ("<EMAIL>", "多浏览器测试邮件3", "这是第三封测试邮件"),
            ("<EMAIL>", "多浏览器测试邮件4", "这是第四封测试邮件"),
            ("<EMAIL>", "多浏览器测试邮件5", "这是第五封测试邮件"),
        ]
        
        task_ids = scheduler.add_batch_tasks(test_emails, send_interval=config.send_interval)
        
        print(f"✅ 添加了 {len(task_ids)} 个邮件任务")
        for task_id in task_ids:
            print(f"   - {task_id}")
        print()
        
        # 启动发送
        print("🚀 启动邮件发送...")
        scheduler.start_sending(num_workers=2)
        
        # 监控发送状态
        print("📊 监控发送状态...")
        print("-" * 80)
        
        start_time = time.time()
        last_stats = None
        
        while True:
            try:
                # 获取发送统计
                stats = scheduler.get_sending_stats()
                
                # 如果统计有变化，显示更新
                if stats != last_stats:
                    elapsed_time = time.time() - start_time
                    
                    print(f"\r⏱️  运行时间: {elapsed_time:.1f}s | "
                          f"总任务: {stats['total_tasks']} | "
                          f"成功: {stats['sent_success']} | "
                          f"失败: {stats['sent_failed']} | "
                          f"待发送: {stats['pending_tasks']} | "
                          f"速率: {stats['emails_per_minute']:.1f}/分钟", end="")
                    
                    # 显示浏览器状态
                    browser_stats = stats.get('browser_stats', {})
                    if browser_stats.get('browsers_detail'):
                        print(f"\n📱 浏览器状态:")
                        for browser_id, browser_info in browser_stats['browsers_detail'].items():
                            account_info = browser_info['current_account'] or "无账号"
                            print(f"   {browser_id}: {browser_info['status']} | "
                                  f"账号: {account_info} | "
                                  f"已发送: {browser_info['sent_count']}")
                    
                    last_stats = stats
                
                # 检查是否完成
                if (stats['pending_tasks'] == 0 and 
                    stats['total_tasks'] > 0 and 
                    stats['sent_success'] + stats['sent_failed'] >= stats['total_tasks']):
                    print(f"\n\n🎉 所有任务完成!")
                    break
                
                # 检查是否超时
                if elapsed_time > 300:  # 5分钟超时
                    print(f"\n\n⏰ 测试超时，停止发送")
                    break
                
                time.sleep(2)
                
            except KeyboardInterrupt:
                print(f"\n\n⚠️ 用户中断，停止发送")
                break
            except Exception as e:
                print(f"\n❌ 监控异常: {e}")
                break
        
        # 最终统计
        final_stats = scheduler.get_sending_stats()
        print("\n" + "="*60)
        print("📊 最终发送统计:")
        print(f"   总任务数: {final_stats['total_tasks']}")
        print(f"   发送成功: {final_stats['sent_success']}")
        print(f"   发送失败: {final_stats['sent_failed']}")
        print(f"   成功率: {(final_stats['sent_success'] / max(final_stats['total_tasks'], 1) * 100):.1f}%")
        print(f"   平均速率: {final_stats['emails_per_minute']:.1f} 封/分钟")
        print(f"   总运行时间: {final_stats['running_time']:.1f} 秒")
        
        # 浏览器统计
        browser_stats = final_stats.get('browser_stats', {})
        print(f"\n🌐 浏览器统计:")
        print(f"   总浏览器数: {browser_stats.get('total_browsers', 0)}")
        print(f"   活跃浏览器: {browser_stats.get('active_browsers', 0)}")
        
        if browser_stats.get('browsers_detail'):
            print(f"\n📱 各浏览器发送情况:")
            for browser_id, browser_info in browser_stats['browsers_detail'].items():
                print(f"   {browser_id}:")
                print(f"     状态: {browser_info['status']}")
                print(f"     当前账号: {browser_info['current_account'] or '无'}")
                print(f"     发送数量: {browser_info['sent_count']}")
                print(f"     是否最小化: {browser_info['is_minimized']}")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        print(f"\n🧹 清理资源...")
        try:
            scheduler.stop_sending()
            scheduler.cleanup()
            print("✅ 资源清理完成")
        except Exception as e:
            print(f"❌ 清理失败: {e}")
    
    print("\n🎯 测试完成")

def test_config_validation():
    """测试配置验证"""
    print("🧪 配置验证测试")
    print("="*40)
    
    # 测试不同配置
    configs = [
        SendingConfig(max_browsers=1, emails_per_account=1, send_interval=1.0),
        SendingConfig(max_browsers=3, emails_per_account=5, send_interval=2.0),
        SendingConfig(max_browsers=5, emails_per_account=10, send_interval=0.5),
    ]
    
    for i, config in enumerate(configs, 1):
        print(f"配置 {i}:")
        print(f"  浏览器数: {config.max_browsers}")
        print(f"  每账号发送: {config.emails_per_account}")
        print(f"  发送间隔: {config.send_interval}s")
        print(f"  窗口大小: {config.browser_window_width}x{config.browser_window_height}")
        print(f"  最小化: {config.minimize_browsers}")
        print(f"  轮换账号: {config.rotate_accounts}")
        print()

if __name__ == "__main__":
    print("🚀 多浏览器Cookie复用发送器测试套件")
    print()
    
    # 配置验证测试
    test_config_validation()
    
    print("⚠️ 注意：完整测试需要：")
    print("1. 有效的新浪邮箱账号和密码")
    print("2. 已通过隐藏式登录获取的Cookie")
    print("3. Chrome浏览器和WebDriver")
    print("4. 可选的代理IP配置")
    print()
    
    # 询问是否运行完整测试
    response = input("是否运行完整的多浏览器发送测试？(y/N): ")
    if response.lower() in ['y', 'yes']:
        print()
        test_multi_browser_sender()
    else:
        print("跳过完整测试")
    
    print("\n测试套件完成")
