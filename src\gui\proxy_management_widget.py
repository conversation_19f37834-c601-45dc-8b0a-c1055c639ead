#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理管理界面
集成代理IP管理和轻量化发送的完整GUI
"""

import json
from typing import List, Dict, Any
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                           QHeaderView, QGroupBox, QComboBox, QSpinBox,
                           QMessageBox, QFileDialog, QTabWidget, QTextEdit,
                           QProgressBar, QCheckBox)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont

from ..core.proxy_manager import ProxyManager
from ..models.account import AccountManager
from ..utils.logger import get_logger

logger = get_logger("ProxyManagementWidget")


class ProxyHealthCheckThread(QThread):
    """代理健康检查线程"""
    
    progress_updated = pyqtSignal(int, int)  # 当前进度, 总数
    health_result = pyqtSignal(dict)  # 健康检查结果
    finished = pyqtSignal()
    
    def __init__(self, proxy_manager: ProxyManager):
        super().__init__()
        self.proxy_manager = proxy_manager
    
    def run(self):
        """运行健康检查"""
        try:
            results = self.proxy_manager.check_all_proxies_health()
            self.health_result.emit(results)
        except Exception as e:
            logger.error(f"代理健康检查异常: {e}")
        finally:
            self.finished.emit()


class ProxyManagementWidget(QWidget):
    """代理管理界面"""
    
    def __init__(self, account_manager: AccountManager, parent=None):
        super().__init__(parent)
        self.account_manager = account_manager
        self.proxy_manager = ProxyManager()
        
        # 健康检查线程
        self.health_check_thread = None
        
        # 自动刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_proxy_list)
        
        self.init_ui()
        self.load_proxy_data()
        
        logger.info("代理管理界面初始化完成")
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("🌐 代理IP管理系统")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # 代理管理选项卡
        proxy_tab = self.create_proxy_management_tab()
        tab_widget.addTab(proxy_tab, "🌐 代理管理")
        
        # 账号绑定选项卡
        binding_tab = self.create_account_binding_tab()
        tab_widget.addTab(binding_tab, "🔗 账号绑定")
        
        # 统计监控选项卡
        stats_tab = self.create_statistics_tab()
        tab_widget.addTab(stats_tab, "📊 统计监控")
        
        layout.addWidget(tab_widget)
    
    def create_proxy_management_tab(self) -> QWidget:
        """创建代理管理选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 添加代理组
        add_group = QGroupBox("添加代理")
        add_layout = QVBoxLayout(add_group)
        
        # 单个代理添加
        single_layout = QHBoxLayout()
        
        self.ip_input = QLineEdit()
        self.ip_input.setPlaceholderText("IP地址")
        single_layout.addWidget(QLabel("IP:"))
        single_layout.addWidget(self.ip_input)
        
        self.port_input = QSpinBox()
        self.port_input.setRange(1, 65535)
        self.port_input.setValue(8080)
        single_layout.addWidget(QLabel("端口:"))
        single_layout.addWidget(self.port_input)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("用户名(可选)")
        single_layout.addWidget(QLabel("用户名:"))
        single_layout.addWidget(self.username_input)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("密码(可选)")
        self.password_input.setEchoMode(QLineEdit.Password)
        single_layout.addWidget(QLabel("密码:"))
        single_layout.addWidget(self.password_input)
        
        self.proxy_type_combo = QComboBox()
        self.proxy_type_combo.addItems(["http", "https", "socks4", "socks5"])
        single_layout.addWidget(QLabel("类型:"))
        single_layout.addWidget(self.proxy_type_combo)
        
        add_layout.addLayout(single_layout)
        
        # 添加按钮
        button_layout = QHBoxLayout()
        
        self.add_single_btn = QPushButton("➕ 添加代理")
        self.add_single_btn.clicked.connect(self.add_single_proxy)
        button_layout.addWidget(self.add_single_btn)
        
        self.import_btn = QPushButton("📁 批量导入")
        self.import_btn.clicked.connect(self.import_proxies)
        button_layout.addWidget(self.import_btn)
        
        self.clear_form_btn = QPushButton("🗑️ 清空表单")
        self.clear_form_btn.clicked.connect(self.clear_proxy_form)
        button_layout.addWidget(self.clear_form_btn)
        
        add_layout.addLayout(button_layout)
        layout.addWidget(add_group)
        
        # 代理列表
        list_group = QGroupBox("代理列表")
        list_layout = QVBoxLayout(list_group)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.health_check_btn = QPushButton("🔍 健康检查")
        self.health_check_btn.clicked.connect(self.start_health_check)
        control_layout.addWidget(self.health_check_btn)
        
        self.auto_rotate_btn = QPushButton("🔄 自动轮换")
        self.auto_rotate_btn.clicked.connect(self.auto_rotate_proxies)
        control_layout.addWidget(self.auto_rotate_btn)
        
        self.balance_load_btn = QPushButton("⚖️ 负载均衡")
        self.balance_load_btn.clicked.connect(self.balance_proxy_load)
        control_layout.addWidget(self.balance_load_btn)
        
        self.cleanup_btn = QPushButton("🧹 清理无效")
        self.cleanup_btn.clicked.connect(self.cleanup_inactive_proxies)
        control_layout.addWidget(self.cleanup_btn)
        
        control_layout.addStretch()
        
        self.auto_refresh_cb = QCheckBox("自动刷新")
        self.auto_refresh_cb.toggled.connect(self.toggle_auto_refresh)
        control_layout.addWidget(self.auto_refresh_cb)
        
        list_layout.addLayout(control_layout)
        
        # 代理表格
        self.proxy_table = QTableWidget()
        self.proxy_table.setColumnCount(9)
        self.proxy_table.setHorizontalHeaderLabels([
            "IP地址", "端口", "类型", "状态", "响应时间", "成功率", "使用次数", "绑定账号", "操作"
        ])
        
        # 设置表格列宽
        header = self.proxy_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        for i in range(1, 9):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)
        
        list_layout.addWidget(self.proxy_table)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        list_layout.addWidget(self.progress_bar)
        
        layout.addWidget(list_group)
        
        return widget
    
    def create_account_binding_tab(self) -> QWidget:
        """创建账号绑定选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 绑定控制
        control_group = QGroupBox("绑定控制")
        control_layout = QVBoxLayout(control_group)
        
        # 自动绑定
        auto_layout = QHBoxLayout()
        self.auto_bind_btn = QPushButton("🔗 自动绑定所有账号")
        self.auto_bind_btn.clicked.connect(self.auto_bind_all_accounts)
        auto_layout.addWidget(self.auto_bind_btn)
        
        self.unbind_all_btn = QPushButton("🔓 解除所有绑定")
        self.unbind_all_btn.clicked.connect(self.unbind_all_accounts)
        auto_layout.addWidget(self.unbind_all_btn)
        
        control_layout.addLayout(auto_layout)
        layout.addWidget(control_group)
        
        # 绑定状态表格
        binding_group = QGroupBox("账号代理绑定状态")
        binding_layout = QVBoxLayout(binding_group)
        
        self.binding_table = QTableWidget()
        self.binding_table.setColumnCount(6)
        self.binding_table.setHorizontalHeaderLabels([
            "账号邮箱", "代理IP", "代理端口", "绑定时间", "状态", "操作"
        ])
        
        # 设置表格列宽
        header = self.binding_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        for i in range(1, 6):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)
        
        binding_layout.addWidget(self.binding_table)
        layout.addWidget(binding_group)
        
        return widget
    
    def create_statistics_tab(self) -> QWidget:
        """创建统计监控选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 总体统计
        stats_group = QGroupBox("代理统计")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_label = QLabel("等待统计数据...")
        stats_layout.addWidget(self.stats_label)
        
        layout.addWidget(stats_group)
        
        # 详细信息
        details_group = QGroupBox("详细信息")
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(200)
        details_layout.addWidget(self.details_text)
        
        layout.addWidget(details_group)
        
        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新统计")
        refresh_btn.clicked.connect(self.refresh_statistics)
        layout.addWidget(refresh_btn)
        
        return widget
    
    def add_single_proxy(self):
        """添加单个代理"""
        try:
            ip = self.ip_input.text().strip()
            port = self.port_input.value()
            username = self.username_input.text().strip() or None
            password = self.password_input.text().strip() or None
            proxy_type = self.proxy_type_combo.currentText()
            
            if not ip:
                QMessageBox.warning(self, "警告", "请输入IP地址")
                return
            
            proxy_id = self.proxy_manager.add_proxy(
                ip=ip,
                port=port,
                username=username,
                password=password,
                proxy_type=proxy_type
            )
            
            if proxy_id:
                QMessageBox.information(self, "成功", f"代理添加成功: {proxy_id}")
                self.clear_proxy_form()
                self.refresh_proxy_list()
            else:
                QMessageBox.warning(self, "失败", "代理添加失败")
                
        except Exception as e:
            logger.error(f"添加代理失败: {e}")
            QMessageBox.critical(self, "错误", f"添加代理失败: {e}")
    
    def clear_proxy_form(self):
        """清空代理表单"""
        self.ip_input.clear()
        self.port_input.setValue(8080)
        self.username_input.clear()
        self.password_input.clear()
        self.proxy_type_combo.setCurrentIndex(0)
    
    def import_proxies(self):
        """批量导入代理"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "导入代理列表", "", "JSON文件 (*.json);;文本文件 (*.txt);;所有文件 (*)"
            )
            
            if not file_path:
                return
            
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.endswith('.json'):
                    proxies = json.load(f)
                else:
                    # 文本格式: ip:port:username:password:type
                    lines = f.readlines()
                    proxies = []
                    for line in lines:
                        parts = line.strip().split(':')
                        if len(parts) >= 2:
                            proxy = {
                                'ip': parts[0],
                                'port': int(parts[1]),
                                'username': parts[2] if len(parts) > 2 else None,
                                'password': parts[3] if len(parts) > 3 else None,
                                'proxy_type': parts[4] if len(parts) > 4 else 'http'
                            }
                            proxies.append(proxy)
            
            success_count = self.proxy_manager.add_proxies_batch(proxies)
            
            QMessageBox.information(self, "导入完成", 
                                  f"成功导入 {success_count}/{len(proxies)} 个代理")
            
            self.refresh_proxy_list()
            
        except Exception as e:
            logger.error(f"导入代理失败: {e}")
            QMessageBox.critical(self, "错误", f"导入代理失败: {e}")
    
    def start_health_check(self):
        """开始健康检查"""
        try:
            if self.health_check_thread and self.health_check_thread.isRunning():
                QMessageBox.warning(self, "警告", "健康检查正在进行中")
                return
            
            self.health_check_thread = ProxyHealthCheckThread(self.proxy_manager)
            self.health_check_thread.health_result.connect(self.on_health_check_finished)
            self.health_check_thread.finished.connect(self.on_health_check_complete)
            
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度
            self.health_check_btn.setEnabled(False)
            self.health_check_btn.setText("检查中...")
            
            self.health_check_thread.start()
            
        except Exception as e:
            logger.error(f"启动健康检查失败: {e}")
            QMessageBox.critical(self, "错误", f"启动健康检查失败: {e}")
    
    def on_health_check_finished(self, results: Dict[str, bool]):
        """健康检查完成"""
        try:
            healthy_count = sum(1 for is_healthy in results.values() if is_healthy)
            total_count = len(results)
            
            QMessageBox.information(self, "健康检查完成", 
                                  f"检查完成: {healthy_count}/{total_count} 个代理健康")
            
            self.refresh_proxy_list()
            
        except Exception as e:
            logger.error(f"处理健康检查结果失败: {e}")
    
    def on_health_check_complete(self):
        """健康检查线程完成"""
        self.progress_bar.setVisible(False)
        self.health_check_btn.setEnabled(True)
        self.health_check_btn.setText("🔍 健康检查")
    
    def auto_rotate_proxies(self):
        """自动轮换代理"""
        try:
            rotated_count = self.proxy_manager.auto_rotate_overused_proxies()
            QMessageBox.information(self, "轮换完成", f"已轮换 {rotated_count} 个过度使用的代理")
            self.refresh_proxy_list()
            self.refresh_binding_list()
            
        except Exception as e:
            logger.error(f"自动轮换失败: {e}")
            QMessageBox.critical(self, "错误", f"自动轮换失败: {e}")
    
    def balance_proxy_load(self):
        """平衡代理负载"""
        try:
            balanced_count = self.proxy_manager.balance_proxy_load()
            QMessageBox.information(self, "负载均衡完成", f"已调整 {balanced_count} 个账号的代理分配")
            self.refresh_binding_list()
            
        except Exception as e:
            logger.error(f"负载均衡失败: {e}")
            QMessageBox.critical(self, "错误", f"负载均衡失败: {e}")
    
    def cleanup_inactive_proxies(self):
        """清理无效代理"""
        try:
            reply = QMessageBox.question(self, "确认清理", 
                                       "确定要清理所有无效的代理吗？\n此操作不可撤销。",
                                       QMessageBox.Yes | QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                cleaned_count = self.proxy_manager.cleanup_inactive_proxies()
                QMessageBox.information(self, "清理完成", f"已清理 {cleaned_count} 个无效代理")
                self.refresh_proxy_list()
                self.refresh_binding_list()
                
        except Exception as e:
            logger.error(f"清理无效代理失败: {e}")
            QMessageBox.critical(self, "错误", f"清理失败: {e}")
    
    def toggle_auto_refresh(self, enabled: bool):
        """切换自动刷新"""
        if enabled:
            self.refresh_timer.start(30000)  # 30秒刷新一次
            logger.info("启用自动刷新")
        else:
            self.refresh_timer.stop()
            logger.info("禁用自动刷新")
    
    def auto_bind_all_accounts(self):
        """自动绑定所有账号"""
        try:
            accounts = self.account_manager.get_all_accounts()
            success_count = 0
            
            for account in accounts:
                if self.proxy_manager.bind_account_proxy(account.email):
                    success_count += 1
            
            QMessageBox.information(self, "绑定完成", 
                                  f"成功绑定 {success_count}/{len(accounts)} 个账号")
            
            self.refresh_binding_list()
            
        except Exception as e:
            logger.error(f"自动绑定失败: {e}")
            QMessageBox.critical(self, "错误", f"自动绑定失败: {e}")
    
    def unbind_all_accounts(self):
        """解除所有绑定"""
        try:
            reply = QMessageBox.question(self, "确认解绑", 
                                       "确定要解除所有账号的代理绑定吗？",
                                       QMessageBox.Yes | QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                # 这里需要实现解绑逻辑
                QMessageBox.information(self, "解绑完成", "所有账号代理绑定已解除")
                self.refresh_binding_list()
                
        except Exception as e:
            logger.error(f"解除绑定失败: {e}")
            QMessageBox.critical(self, "错误", f"解除绑定失败: {e}")
    
    def load_proxy_data(self):
        """加载代理数据"""
        self.refresh_proxy_list()
        self.refresh_binding_list()
        self.refresh_statistics()
    
    def refresh_proxy_list(self):
        """刷新代理列表"""
        try:
            # 实现代理列表刷新逻辑
            logger.debug("刷新代理列表")
            
        except Exception as e:
            logger.error(f"刷新代理列表失败: {e}")
    
    def refresh_binding_list(self):
        """刷新绑定列表"""
        try:
            # 实现绑定列表刷新逻辑
            logger.debug("刷新绑定列表")
            
        except Exception as e:
            logger.error(f"刷新绑定列表失败: {e}")
    
    def refresh_statistics(self):
        """刷新统计信息"""
        try:
            stats = self.proxy_manager.get_proxy_statistics()
            
            stats_text = f"""
📊 代理统计:
• 总代理数: {stats.get('total_proxies', 0)}
• 活跃代理: {stats.get('active_proxies', 0)}
• 不活跃代理: {stats.get('inactive_proxies', 0)}
• 绑定账号: {stats.get('bound_accounts', 0)}
• 平均响应时间: {stats.get('avg_response_time', 0):.3f}s
• 平均成功率: {stats.get('avg_success_rate', 0):.1f}%
• 健康率: {stats.get('health_rate', 0):.1f}%
            """.strip()
            
            self.stats_label.setText(stats_text)
            
        except Exception as e:
            logger.error(f"刷新统计信息失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        try:
            # 停止定时器
            self.refresh_timer.stop()
            
            # 停止健康检查线程
            if self.health_check_thread and self.health_check_thread.isRunning():
                self.health_check_thread.terminate()
                self.health_check_thread.wait()
            
            event.accept()
            
        except Exception as e:
            logger.error(f"关闭代理管理界面失败: {e}")
            event.accept()
