#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件模板数据模型
定义邮件模板的数据结构和操作方法
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any
from src.models.database import DatabaseManager
from src.utils.logger import get_logger

logger = get_logger("EmailTemplateModel")


@dataclass
class EmailTemplate:
    """邮件模板数据类"""
    id: Optional[int] = None
    template_name: str = ""
    subject: str = ""
    content: str = ""
    content_type: str = "text/plain"  # 新增：内容类型 text/plain 或 text/html
    is_default: bool = False
    variables: str = ""  # 新增：模板变量说明，JSON格式存储
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'template_name': self.template_name,
            'subject': self.subject,
            'content': self.content,
            'content_type': self.content_type,
            'is_default': self.is_default,
            'variables': self.variables,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EmailTemplate':
        """从字典创建邮件模板对象"""
        template = cls()
        template.id = data.get('id')
        template.template_name = data.get('template_name', '')
        template.subject = data.get('subject', '')
        template.content = data.get('content', '')
        template.content_type = data.get('content_type', 'text/plain')
        template.is_default = bool(data.get('is_default', False))
        template.variables = data.get('variables', '')

        # 处理时间字段
        if data.get('create_time'):
            template.create_time = datetime.fromisoformat(data['create_time'])
        if data.get('update_time'):
            template.update_time = datetime.fromisoformat(data['update_time'])

        return template
    
    def render(self, variables: Dict[str, str]) -> Dict[str, str]:
        """
        渲染模板，替换变量
        
        Args:
            variables: 变量字典，如 {'name': '张三', 'company': 'ABC公司'}
        
        Returns:
            渲染后的主题和内容
        """
        rendered_subject = self.subject
        rendered_content = self.content
        
        # 替换变量
        for key, value in variables.items():
            placeholder = f"{{{key}}}"  # 格式: {变量名}
            rendered_subject = rendered_subject.replace(placeholder, str(value))
            rendered_content = rendered_content.replace(placeholder, str(value))
        
        return {
            'subject': rendered_subject,
            'content': rendered_content
        }


class EmailTemplateManager:
    """邮件模板管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化邮件模板管理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db = db_manager
    
    def add_template(self, template: EmailTemplate) -> int:
        """
        添加邮件模板
        
        Args:
            template: 邮件模板对象
        
        Returns:
            新添加模板的ID
        """
        try:
            # 如果设置为默认模板，先取消其他默认模板
            if template.is_default:
                self._clear_default_templates()
            
            query = """
                INSERT INTO email_templates (
                    template_name, subject, content, content_type, is_default, variables
                ) VALUES (?, ?, ?, ?, ?, ?)
            """
            params = (
                template.template_name,
                template.subject,
                template.content,
                template.content_type,
                template.is_default,
                template.variables
            )
            
            template_id = self.db.execute_insert(query, params)
            logger.info(f"邮件模板添加成功: {template.template_name}, ID: {template_id}")
            return template_id
            
        except Exception as e:
            logger.error(f"添加邮件模板失败: {template.template_name}, 错误: {e}")
            raise
    
    def get_template_by_id(self, template_id: int) -> Optional[EmailTemplate]:
        """
        根据ID获取邮件模板
        
        Args:
            template_id: 模板ID
        
        Returns:
            邮件模板对象或None
        """
        try:
            query = "SELECT * FROM email_templates WHERE id = ?"
            results = self.db.execute_query(query, (template_id,))
            
            if results:
                return EmailTemplate.from_dict(results[0])
            return None
            
        except Exception as e:
            logger.error(f"获取邮件模板失败: ID {template_id}, 错误: {e}")
            return None
    
    def get_template_by_name(self, template_name: str) -> Optional[EmailTemplate]:
        """
        根据名称获取邮件模板
        
        Args:
            template_name: 模板名称
        
        Returns:
            邮件模板对象或None
        """
        try:
            query = "SELECT * FROM email_templates WHERE template_name = ?"
            results = self.db.execute_query(query, (template_name,))
            
            if results:
                return EmailTemplate.from_dict(results[0])
            return None
            
        except Exception as e:
            logger.error(f"获取邮件模板失败: {template_name}, 错误: {e}")
            return None
    
    def get_all_templates(self) -> List[EmailTemplate]:
        """
        获取所有邮件模板
        
        Returns:
            邮件模板列表
        """
        try:
            query = "SELECT * FROM email_templates ORDER BY is_default DESC, create_time DESC"
            results = self.db.execute_query(query)
            
            return [EmailTemplate.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"获取邮件模板列表失败: {e}")
            return []
    
    def get_default_template(self) -> Optional[EmailTemplate]:
        """
        获取默认邮件模板
        
        Returns:
            默认邮件模板或None
        """
        try:
            query = "SELECT * FROM email_templates WHERE is_default = 1 LIMIT 1"
            results = self.db.execute_query(query)
            
            if results:
                return EmailTemplate.from_dict(results[0])
            return None
            
        except Exception as e:
            logger.error(f"获取默认邮件模板失败: {e}")
            return None
    
    def update_template(self, template: EmailTemplate) -> bool:
        """
        更新邮件模板
        
        Args:
            template: 邮件模板对象
        
        Returns:
            是否更新成功
        """
        try:
            # 如果设置为默认模板，先取消其他默认模板
            if template.is_default:
                self._clear_default_templates(exclude_id=template.id)
            
            query = """
                UPDATE email_templates SET
                    template_name = ?, subject = ?, content = ?, content_type = ?,
                    is_default = ?, variables = ?, update_time = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            params = (
                template.template_name,
                template.subject,
                template.content,
                template.content_type,
                template.is_default,
                template.variables,
                template.id
            )
            
            rows_affected = self.db.execute_update(query, params)
            success = rows_affected > 0
            
            if success:
                logger.info(f"邮件模板更新成功: {template.template_name}")
            else:
                logger.warning(f"邮件模板更新失败，未找到记录: ID {template.id}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新邮件模板失败: {template.template_name}, 错误: {e}")
            return False
    
    def delete_template(self, template_id: int) -> bool:
        """
        删除邮件模板
        
        Args:
            template_id: 模板ID
        
        Returns:
            是否删除成功
        """
        try:
            query = "DELETE FROM email_templates WHERE id = ?"
            rows_affected = self.db.execute_update(query, (template_id,))
            success = rows_affected > 0
            
            if success:
                logger.info(f"邮件模板删除成功: ID {template_id}")
            else:
                logger.warning(f"邮件模板删除失败，未找到记录: ID {template_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"删除邮件模板失败: ID {template_id}, 错误: {e}")
            return False
    
    def set_default_template(self, template_id: int) -> bool:
        """
        设置默认模板
        
        Args:
            template_id: 模板ID
        
        Returns:
            是否设置成功
        """
        try:
            # 先取消所有默认模板
            self._clear_default_templates()
            
            # 设置指定模板为默认
            query = """
                UPDATE email_templates SET 
                    is_default = 1,
                    update_time = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            rows_affected = self.db.execute_update(query, (template_id,))
            success = rows_affected > 0
            
            if success:
                logger.info(f"默认邮件模板设置成功: ID {template_id}")
            else:
                logger.warning(f"默认邮件模板设置失败，未找到记录: ID {template_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"设置默认邮件模板失败: ID {template_id}, 错误: {e}")
            return False
    
    def _clear_default_templates(self, exclude_id: Optional[int] = None):
        """
        清除所有默认模板标记
        
        Args:
            exclude_id: 排除的模板ID
        """
        try:
            if exclude_id:
                query = "UPDATE email_templates SET is_default = 0 WHERE id != ?"
                self.db.execute_update(query, (exclude_id,))
            else:
                query = "UPDATE email_templates SET is_default = 0"
                self.db.execute_update(query)
                
        except Exception as e:
            logger.error(f"清除默认模板标记失败: {e}")
    
    def create_default_templates(self):
        """创建默认邮件模板"""
        try:
            # 检查是否已有模板
            existing_templates = self.get_all_templates()
            if existing_templates:
                return
            
            # 创建默认模板
            default_templates = [
                EmailTemplate(
                    template_name="默认模板",
                    subject="重要通知",
                    content="您好，\n\n这是一封重要通知邮件。\n\n谢谢！",
                    is_default=True
                ),
                EmailTemplate(
                    template_name="产品推广",
                    subject="【{company}】新产品上线通知",
                    content="亲爱的{name}，\n\n我们很高兴地通知您，{company}推出了全新产品{product}。\n\n产品特点：\n{features}\n\n欢迎了解更多详情。\n\n此致\n敬礼！\n{company}团队"
                ),
                EmailTemplate(
                    template_name="活动邀请",
                    subject="诚邀参加{event_name}",
                    content="尊敬的{name}，\n\n我们诚挚邀请您参加{event_name}。\n\n活动时间：{event_time}\n活动地点：{event_location}\n\n期待您的参与！\n\n主办方：{organizer}"
                )
            ]
            
            for template in default_templates:
                self.add_template(template)
            
            logger.info("默认邮件模板创建完成")
            
        except Exception as e:
            logger.error(f"创建默认邮件模板失败: {e}")
