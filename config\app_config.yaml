# 新浪邮箱自动化程序配置文件

# 应用程序基本配置
app:
  name: "新浪邮箱自动化程序"
  version: "1.0.0"
  author: "AI Assistant"
  description: "功能强大的新浪邮箱批量自动化操作工具"

# 数据库配置
database:
  type: "sqlite"
  path: "data/sina_email_automation.db"
  backup_enabled: true
  backup_interval: 24  # 小时

# 浏览器配置
browser:
  default_browser: "chrome"
  headless: false  # 是否无头模式
  window_size: [1920, 1080]
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
  page_load_timeout: 30  # 秒
  implicit_wait: 10  # 秒
  download_path: "data/downloads"

# 邮件发送配置
email:
  send_interval_min: 5  # 最小发送间隔(秒)
  send_interval_max: 15  # 最大发送间隔(秒)
  max_retry_count: 3  # 最大重试次数
  retry_delay: 60  # 重试延迟(秒)
  batch_size: 10  # 批量发送数量
  daily_limit: 100  # 每日发送限制

# 代理配置
proxy:
  enabled: true
  rotation_enabled: true  # 是否启用代理轮换
  test_url: "http://httpbin.org/ip"  # 代理测试URL
  timeout: 10  # 代理连接超时(秒)
  max_retry: 3  # 代理重试次数

# 文件监控配置
file_monitor:
  enabled: true
  check_interval: 5  # 检查间隔(秒)
  supported_extensions: [".txt", ".csv"]
  encoding: "utf-8"
  backup_processed_files: true

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  max_file_size: "10MB"
  backup_count: 5
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  
# 安全配置
security:
  encryption_key_file: "config/.encryption_key"
  password_encryption: true
  session_timeout: 3600  # 会话超时(秒)
  max_login_attempts: 3

# GUI配置
gui:
  theme: "default"
  window_size: [1200, 800]
  window_position: "center"
  auto_save_settings: true
  language: "zh_CN"

# 性能配置
performance:
  max_concurrent_browsers: 3
  max_concurrent_emails: 5
  memory_limit_mb: 512
  cpu_usage_limit: 80  # 百分比

# 新浪邮箱特定配置
sina_email:
  login_url: "https://mail.sina.com.cn/"
  compose_url: "https://mail.sina.com.cn/classic/compose.php"
  login_selectors:
    username_input: "input[name='username']"
    password_input: "input[name='password']"
    login_button: "input[type='submit']"
    captcha_input: "input[name='door']"
  compose_selectors:
    to_input: "input[name='to']"
    subject_input: "input[name='subject']"
    content_textarea: "textarea[name='content']"
    send_button: "input[value='发送']"
