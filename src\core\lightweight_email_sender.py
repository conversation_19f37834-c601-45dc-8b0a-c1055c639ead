#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轻量化邮件发送引擎
基于HTTP请求和Cookie的超轻量邮件发送方案
- 无需浏览器，纯HTTP请求
- 支持100+账号并发发送
- 内存占用极低
- 发送速度极快
"""

import json
import time
import asyncio
import aiohttp
import requests
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

from ..models.account import Account
from ..utils.logger import get_logger
from .cookie_manager import CookieManager

logger = get_logger("LightweightEmailSender")


@dataclass
class EmailMessage:
    """邮件消息数据类"""
    to_email: str
    subject: str
    content: str
    content_type: str = "text/html"
    attachments: List[str] = None
    priority: int = 1  # 1=高, 2=中, 3=低
    
    def __post_init__(self):
        if self.attachments is None:
            self.attachments = []


class LightweightEmailSender:
    """轻量化邮件发送引擎"""
    
    def __init__(self, cookie_manager: <PERSON>ieManager):
        self.cookie_manager = cookie_manager
        self.session_pool = {}  # 会话池
        self.send_queue = asyncio.Queue()  # 发送队列
        self.stats = {
            'total_sent': 0,
            'success_count': 0,
            'failed_count': 0,
            'start_time': datetime.now()
        }

        # 代理管理器
        from .proxy_manager import ProxyManager
        self.proxy_manager = ProxyManager()

        # Cookie管理器
        cookie_config = {
            'cookie_dir': 'data/cookies',
            'encryption_enabled': True,
            'max_age_days': 30
        }
        self.cookie_manager = CookieManager(cookie_config)

        # 新浪邮箱API端点
        self.sina_endpoints = {
            'compose': 'https://mail.sina.com.cn/classic/compose.php',
            'send': 'https://mail.sina.com.cn/classic/send.php',
            'ajax_send': 'https://mail.sina.com.cn/classic/ajax_send.php',
            # 备用发送接口
            'send_alt': 'https://mail.sina.com.cn/cgi-bin/compose',
            'ajax_send_alt': 'https://mail.sina.com.cn/cgi-bin/ajax_send'
        }

        logger.info("轻量化邮件发送引擎初始化完成")
    
    def create_lightweight_session(self, account: Account) -> Optional[requests.Session]:
        """创建轻量化会话"""
        try:
            logger.info(f"🚀 创建轻量化会话: {account.email}")
            
            # 获取保存的Cookie
            cookie_data = self.cookie_manager.get_cookies(account.email)
            if not cookie_data:
                logger.warning(f"未找到Cookie，需要先登录: {account.email}")
                return None
            
            # 创建requests会话
            session = requests.Session()
            
            # 设置轻量化请求头
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'X-Requested-With': 'XMLHttpRequest',  # AJAX请求标识
                'Referer': 'https://mail.sina.com.cn/',
                'Origin': 'https://mail.sina.com.cn'
            })
            
            # 设置代理（支持直连模式）
            proxy_info = self.proxy_manager.get_proxy_for_account(account.email)
            if proxy_info:
                try:
                    session.proxies = proxy_info.proxy_dict
                    logger.info(f"🌐 为账号 {account.email} 设置代理: {proxy_info.ip}:{proxy_info.port}")
                except Exception as proxy_error:
                    logger.warning(f"⚠️ 代理配置失败: {proxy_error}，使用直连模式")
                    session.proxies.clear()  # 清除代理配置，使用直连
            else:
                logger.info(f"✅ 账号 {account.email} 使用直连模式")

            # 增强Cookie设置机制
            logger.info(f"🍪 开始应用Cookie: {len(cookie_data['cookies'])} 个")

            # 清除现有Cookie
            session.cookies.clear()

            applied_count = 0
            for cookie in cookie_data['cookies']:
                try:
                    # 确保Cookie格式正确
                    name = cookie.get('name', '')
                    value = cookie.get('value', '')
                    domain = cookie.get('domain', '.sina.com.cn')
                    path = cookie.get('path', '/')

                    if name and value:
                        session.cookies.set(
                            name=name,
                            value=value,
                            domain=domain,
                            path=path,
                            secure=cookie.get('secure', False)
                        )
                        applied_count += 1
                        logger.debug(f"✅ 应用Cookie: {name}")
                    else:
                        logger.warning(f"⚠️ 跳过无效Cookie: {cookie}")

                except Exception as cookie_error:
                    logger.warning(f"⚠️ Cookie应用失败: {cookie}, 错误: {cookie_error}")
                    continue

            logger.info(f"✅ 成功应用 {applied_count}/{len(cookie_data['cookies'])} 个Cookie到会话")
            
            # 验证会话有效性
            if self._validate_session(session, account):
                self.session_pool[account.email] = session
                logger.info(f"✅ 轻量化会话创建成功: {account.email}")
                return session
            else:
                logger.warning(f"❌ 会话验证失败: {account.email}")
                logger.info(f"💡 建议：Cookie可能已过期，请重新进行隐藏式登录以更新Cookie")

                # 清理无效的Cookie文件
                try:
                    if hasattr(self.cookie_manager, 'cookie_dir'):
                        cookie_file = self.cookie_manager.cookie_dir / f"{account.email.replace('@', '_').replace('.', '_')}.cookies"
                        if cookie_file.exists():
                            cookie_file.unlink()
                            logger.info(f"🗑️ 已清理过期Cookie文件: {account.email}")
                except Exception as cleanup_error:
                    logger.warning(f"⚠️ 清理Cookie文件失败: {cleanup_error}")

                return None
                
        except Exception as e:
            logger.error(f"❌ 创建轻量化会话失败: {account.email}, 错误: {e}")
            return None
    
    def _validate_session(self, session: requests.Session, account: Account) -> bool:
        """验证会话有效性 - 增强版本"""
        try:
            logger.info(f"🔍 验证会话有效性: {account.email}")

            # 设置更完整的请求头
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Referer': 'https://mail.sina.com.cn/'
            })

            # 多个验证URL，提高成功率
            validation_urls = [
                'https://mail.sina.com.cn/classic/inbox.php',
                'https://mail.sina.com.cn/classic/index.php',
                'https://mail.sina.com.cn'
            ]

            for url in validation_urls:
                try:
                    logger.info(f"🔗 尝试验证URL: {url}")

                    response = session.get(
                        url,
                        timeout=15,
                        allow_redirects=True
                    )

                    if response.status_code == 200:
                        content = response.text.lower()

                        # 扩展成功指标
                        success_indicators = [
                            '收件箱', 'inbox', 'mailbox', 'compose', '写邮件',
                            '新邮件', '通讯录', 'contacts', '设置', 'settings'
                        ]

                        # 检查登录状态（避免在登录页面）
                        login_indicators = [
                            '用户名', '密码', '登录', 'login', 'sign in', 'username', 'password'
                        ]

                        success_count = sum(1 for indicator in success_indicators if indicator in content)
                        login_count = sum(1 for indicator in login_indicators if indicator in content)

                        # 详细调试信息
                        logger.info(f"🔍 URL验证详情: {url}")
                        logger.info(f"📊 成功指标: {success_count}, 登录指标: {login_count}")
                        logger.info(f"📍 最终URL: {response.url}")

                        # 显示找到的指标
                        found_success = [indicator for indicator in success_indicators if indicator in content]
                        found_login = [indicator for indicator in login_indicators if indicator in content]

                        if found_success:
                            logger.info(f"✅ 找到成功指标: {found_success}")
                        if found_login:
                            logger.info(f"⚠️ 找到登录指标: {found_login}")

                        if success_count > 0 and login_count == 0:
                            logger.info(f"✅ 会话验证成功: {account.email} (URL: {url})")
                            return True
                        elif success_count >= 5:  # 如果成功指标足够多，认为是有效的
                            logger.info(f"✅ 部分验证成功但可接受: {account.email} (成功指标: {success_count})")
                            return True
                        elif success_count > 0:
                            logger.info(f"🔄 部分验证成功: {account.email} (可能需要重新登录)")
                            continue
                        else:
                            logger.warning(f"⚠️ URL验证失败: {url} (无成功指标)")
                            # 显示页面内容片段用于调试
                            content_snippet = content[:200] if content else "无内容"
                            logger.debug(f"页面内容片段: {content_snippet}")
                            continue
                    else:
                        logger.warning(f"⚠️ URL响应失败: {url}, 状态码: {response.status_code}")
                        continue

                except Exception as url_error:
                    logger.warning(f"⚠️ URL验证异常: {url}, 错误: {url_error}")
                    continue

            # 所有URL都验证失败
            logger.warning(f"❌ 所有验证URL都失败: {account.email}")
            return False

        except Exception as e:
            logger.error(f"❌ 会话验证异常: {account.email}, 错误: {e}")
            return False
    
    def send_email_lightweight(self, account: Account, message: EmailMessage) -> Dict[str, Any]:
        """轻量化发送单封邮件"""
        try:
            logger.info(f"📧 轻量化发送邮件: {account.email} -> {message.to_email}")
            
            # 获取或创建会话
            session = self.session_pool.get(account.email)
            if not session:
                session = self.create_lightweight_session(account)
                if not session:
                    return {'success': False, 'message': '无法创建有效会话'}
            
            # 获取发送表单的必要参数
            form_data = self._get_send_form_data(session, message)
            if not form_data:
                return {'success': False, 'message': '无法获取发送表单数据'}
            
            # 发送邮件
            result = self._submit_email(session, form_data)

            # 处理代理相关错误
            if not result['success']:
                error_msg = result.get('message', '').lower()
                if any(keyword in error_msg for keyword in ['proxy', 'connection', 'timeout', 'network']):
                    logger.warning(f"🔄 检测到代理相关错误，尝试轮换代理: {account.email}")
                    if self.proxy_manager.rotate_proxy_for_account(account.email, 'failure'):
                        logger.info(f"✅ 代理轮换成功，建议重试发送: {account.email}")
                        result['retry_suggested'] = True

            # 更新统计
            self.stats['total_sent'] += 1
            if result['success']:
                self.stats['success_count'] += 1
                # 更新代理成功统计
                self._update_proxy_success(account.email)
            else:
                self.stats['failed_count'] += 1
                # 更新代理失败统计
                self._update_proxy_failure(account.email)

            return result
            
        except Exception as e:
            logger.error(f"❌ 轻量化发送邮件失败: {account.email}, 错误: {e}")
            self.stats['total_sent'] += 1
            self.stats['failed_count'] += 1
            return {'success': False, 'message': f'发送异常: {e}'}
    
    def _get_send_form_data(self, session: requests.Session, message: EmailMessage) -> Optional[Dict]:
        """获取发送表单数据"""
        try:
            logger.info("🔍 开始获取发送表单数据...")

            # 访问写邮件页面获取表单参数
            compose_url = self.sina_endpoints['compose']
            logger.info(f"📝 访问写邮件页面: {compose_url}")

            response = session.get(compose_url, timeout=10)
            logger.info(f"📊 写邮件页面响应: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"❌ 无法访问写邮件页面: {response.status_code}")
                logger.debug(f"响应内容: {response.text[:500]}")
                return None

            # 分析页面内容，寻找真实的发送接口
            self._discover_send_endpoints(response.text)

            # 解析HTML获取必要的表单参数
            form_data = self._parse_compose_form(response.text, message)

            if form_data:
                logger.info(f"✅ 表单数据获取成功: {len(form_data)} 个字段")
                logger.debug(f"表单字段: {list(form_data.keys())}")
            else:
                logger.error("❌ 表单数据解析失败")

            return form_data

        except Exception as e:
            logger.error(f"❌ 获取发送表单数据失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return None

    def _discover_send_endpoints(self, html_content: str):
        """从页面内容中发现真实的发送接口"""
        try:
            import re
            logger.info("🔍 开始发现真实发送接口...")

            # 寻找表单action属性
            form_action_patterns = [
                r'<form[^>]*action=["\']([^"\']*send[^"\']*)["\']',
                r'<form[^>]*action=["\']([^"\']*compose[^"\']*)["\']',
                r'action=["\']([^"\']*send[^"\']*)["\']',
                r'action=["\']([^"\']*compose[^"\']*)["\']'
            ]

            discovered_endpoints = []
            for pattern in form_action_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    if match and match not in discovered_endpoints:
                        discovered_endpoints.append(match)
                        logger.info(f"🎯 发现表单接口: {match}")

            # 寻找JavaScript中的发送URL
            js_url_patterns = [
                r'["\']([^"\']*send[^"\']*\.php[^"\']*)["\']',
                r'["\']([^"\']*ajax[^"\']*send[^"\']*)["\']',
                r'url\s*:\s*["\']([^"\']*send[^"\']*)["\']',
                r'action\s*:\s*["\']([^"\']*send[^"\']*)["\']'
            ]

            for pattern in js_url_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    if match and match not in discovered_endpoints:
                        # 补全相对URL
                        if match.startswith('/'):
                            match = 'https://mail.sina.com.cn' + match
                        elif not match.startswith('http'):
                            match = 'https://mail.sina.com.cn/' + match

                        discovered_endpoints.append(match)
                        logger.info(f"🎯 发现JS接口: {match}")

            # 更新发送接口列表
            if discovered_endpoints:
                logger.info(f"✅ 发现 {len(discovered_endpoints)} 个潜在发送接口")
                # 将发现的接口添加到端点列表
                for i, endpoint in enumerate(discovered_endpoints):
                    self.sina_endpoints[f'discovered_{i}'] = endpoint
            else:
                logger.warning("⚠️ 未发现新的发送接口")

        except Exception as e:
            logger.error(f"❌ 发现发送接口失败: {e}")
    
    def _parse_compose_form(self, html_content: str, message: EmailMessage) -> Dict:
        """解析写邮件表单"""
        try:
            import re

            # 基础表单数据
            form_data = {
                'to': message.to_email,
                'subject': message.subject,
                'content': message.content,
                'action': 'send',
                'func': 'send',  # 新浪邮箱可能需要的参数
                'charset': 'utf-8'
            }

            # 根据内容类型设置相应参数
            if message.content_type == 'text/html':
                form_data['html'] = '1'
                form_data['content_type'] = 'text/html'
            else:
                form_data['html'] = '0'
                form_data['content_type'] = 'text/plain'

            # 提取各种可能的隐藏字段
            hidden_patterns = [
                # CSRF和安全相关
                (r'name=["\']csrf_token["\'][^>]*value=["\']([^"\']+)["\']', 'csrf_token'),
                (r'name=["\']_token["\'][^>]*value=["\']([^"\']+)["\']', '_token'),
                (r'name=["\']token["\'][^>]*value=["\']([^"\']+)["\']', 'token'),

                # 会话相关
                (r'name=["\']nonce["\'][^>]*value=["\']([^"\']+)["\']', 'nonce'),
                (r'name=["\']session_id["\'][^>]*value=["\']([^"\']+)["\']', 'session_id'),
                (r'name=["\']sid["\'][^>]*value=["\']([^"\']+)["\']', 'sid'),

                # 时间戳相关
                (r'name=["\']timestamp["\'][^>]*value=["\']([^"\']+)["\']', 'timestamp'),
                (r'name=["\']time["\'][^>]*value=["\']([^"\']+)["\']', 'time'),

                # 新浪特有字段
                (r'name=["\']uid["\'][^>]*value=["\']([^"\']+)["\']', 'uid'),
                (r'name=["\']user_id["\'][^>]*value=["\']([^"\']+)["\']', 'user_id'),
                (r'name=["\']folder["\'][^>]*value=["\']([^"\']+)["\']', 'folder'),
            ]

            for pattern, field_name in hidden_patterns:
                match = re.search(pattern, html_content, re.IGNORECASE)
                if match:
                    form_data[field_name] = match.group(1)
                    logger.debug(f"找到隐藏字段: {field_name} = {match.group(1)}")

            # 添加当前时间戳
            form_data['send_time'] = str(int(time.time()))

            # 添加一些可能需要的默认参数
            form_data.update({
                'cc': '',  # 抄送
                'bcc': '',  # 密送
                'priority': '3',  # 优先级
                'receipt': '0',  # 回执
                'save_sent': '1',  # 保存到已发送
            })

            logger.info(f"表单数据解析完成: {len(form_data)} 个字段")
            logger.debug(f"表单字段详情: {list(form_data.keys())}")
            return form_data

        except Exception as e:
            logger.error(f"解析表单失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {}
    
    def _submit_email(self, session: requests.Session, form_data: Dict) -> Dict[str, Any]:
        """提交邮件发送请求"""
        try:
            logger.info("📤 开始提交邮件发送请求...")

            # 尝试多个发送接口（包括发现的接口）
            send_urls = [
                ('AJAX发送接口', self.sina_endpoints['ajax_send']),
                ('标准发送接口', self.sina_endpoints['send']),
            ]

            # 添加发现的接口
            for key, url in self.sina_endpoints.items():
                if key.startswith('discovered_'):
                    send_urls.append((f'发现的接口{key[-1]}', url))

            # 添加备用接口
            send_urls.extend([
                ('备用AJAX接口', self.sina_endpoints['ajax_send_alt']),
                ('备用发送接口', self.sina_endpoints['send_alt'])
            ])

            logger.info(f"📋 发送数据: 收件人={form_data.get('to')}, 主题={form_data.get('subject')}")
            logger.debug(f"完整表单数据: {form_data}")

            for interface_name, send_url in send_urls:
                try:
                    logger.info(f"🎯 尝试{interface_name}: {send_url}")

                    # 根据接口类型设置不同的请求头
                    if 'ajax' in send_url.lower():
                        headers = {
                            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json, text/javascript, */*; q=0.01'
                        }
                    else:
                        headers = {
                            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                        }

                    # 发送请求
                    response = session.post(send_url, data=form_data, headers=headers, timeout=15)
                    logger.info(f"📊 {interface_name}响应状态: {response.status_code}")

                    if response.status_code == 200:
                        logger.info(f"✅ {interface_name}HTTP请求成功，开始解析响应...")
                        result = self._parse_send_response(response, interface_name)

                        # 如果这个接口返回了有效响应，就使用它
                        if result['success'] or len(response.text.strip()) > 0:
                            logger.info(f"🎯 {interface_name}发送结果: {result}")
                            return result
                        else:
                            logger.warning(f"⚠️ {interface_name}返回空响应，尝试下一个接口...")
                            continue
                    else:
                        logger.warning(f"⚠️ {interface_name}HTTP错误: {response.status_code}")
                        continue

                except Exception as interface_error:
                    logger.warning(f"⚠️ {interface_name}请求失败: {interface_error}")
                    continue

            # 所有接口都失败
            logger.error("❌ 所有发送接口都失败")
            return {'success': False, 'message': '所有发送接口都失败'}

        except Exception as e:
            logger.error(f"❌ 提交邮件请求失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {'success': False, 'message': f'请求异常: {e}'}
    
    def _parse_send_response(self, response: requests.Response, interface_name: str = "未知接口") -> Dict[str, Any]:
        """解析发送响应"""
        try:
            logger.info(f"🔍 开始解析{interface_name}发送响应...")

            content = response.text
            content_lower = content.lower()

            logger.info(f"📄 {interface_name}响应长度: {len(content)} 字符")
            logger.debug(f"{interface_name}响应内容前500字符: {content[:500]}")

            # 检查成功标识
            success_indicators = [
                'success', '成功', 'sent', '已发送', 'ok', '"status":1', '"code":0'
            ]

            # 检查失败标识
            error_indicators = [
                'error', '错误', 'failed', '失败', 'invalid', '"status":0', '"code":-1'
            ]

            # 尝试解析JSON响应
            try:
                json_data = response.json()
                logger.info(f"📊 JSON响应解析成功: {json_data}")

                if isinstance(json_data, dict):
                    if json_data.get('status') == 1 or json_data.get('success') == True:
                        logger.info("✅ JSON响应显示发送成功")
                        return {'success': True, 'message': '邮件发送成功'}
                    else:
                        error_msg = json_data.get('message', '发送失败')
                        logger.warning(f"❌ JSON响应显示发送失败: {error_msg}")
                        return {'success': False, 'message': error_msg}
            except Exception as json_error:
                logger.info(f"📄 非JSON响应，进行文本分析: {json_error}")

            # 文本内容检查
            found_success = []
            found_error = []

            for indicator in success_indicators:
                if indicator in content_lower:
                    found_success.append(indicator)

            for indicator in error_indicators:
                if indicator in content_lower:
                    found_error.append(indicator)

            logger.info(f"🔍 找到成功标识: {found_success}")
            logger.info(f"🔍 找到错误标识: {found_error}")

            if found_success:
                logger.info("✅ 文本分析显示发送成功")
                return {'success': True, 'message': '邮件发送成功'}

            if found_error:
                logger.warning("❌ 文本分析显示发送失败")
                return {'success': False, 'message': '邮件发送失败'}

            # 特殊处理：空响应需要进一步判断
            if len(content.strip()) == 0:
                logger.warning(f"⚠️ {interface_name}返回空响应，可能表示接口不正确")
                return {'success': False, 'message': f'{interface_name}返回空响应'}

            # 默认判断
            logger.warning(f"⚠️ 无法确定{interface_name}发送状态，响应内容不包含明确的成功或失败标识")
            logger.debug(f"{interface_name}完整响应内容: {content}")
            return {'success': False, 'message': f'{interface_name}发送状态不明确'}

        except Exception as e:
            logger.error(f"❌ 解析发送响应失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {'success': False, 'message': f'响应解析失败: {e}'}
    
    async def batch_send_async(self, email_tasks: List[Tuple[Account, EmailMessage]]) -> List[Dict]:
        """异步批量发送邮件"""
        try:
            logger.info(f"🚀 开始异步批量发送: {len(email_tasks)} 封邮件")
            
            # 创建异步任务
            tasks = []
            for account, message in email_tasks:
                task = asyncio.create_task(self._send_email_async(account, message))
                tasks.append(task)
            
            # 并发执行
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append({
                        'success': False,
                        'message': f'异步发送异常: {result}',
                        'account': email_tasks[i][0].email
                    })
                else:
                    result['account'] = email_tasks[i][0].email
                    processed_results.append(result)
            
            logger.info(f"✅ 异步批量发送完成: {len(processed_results)} 个结果")
            return processed_results
            
        except Exception as e:
            logger.error(f"❌ 异步批量发送失败: {e}")
            return []
    
    async def _send_email_async(self, account: Account, message: EmailMessage) -> Dict[str, Any]:
        """异步发送单封邮件"""
        try:
            # 在线程池中执行同步发送
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor(max_workers=10) as executor:
                result = await loop.run_in_executor(
                    executor, 
                    self.send_email_lightweight, 
                    account, 
                    message
                )
            return result
            
        except Exception as e:
            logger.error(f"异步发送邮件失败: {account.email}, 错误: {e}")
            return {'success': False, 'message': f'异步发送异常: {e}'}
    
    def get_sending_stats(self) -> Dict[str, Any]:
        """获取发送统计信息"""
        runtime = datetime.now() - self.stats['start_time']
        
        stats = {
            'total_sent': self.stats['total_sent'],
            'success_count': self.stats['success_count'],
            'failed_count': self.stats['failed_count'],
            'success_rate': (self.stats['success_count'] / max(self.stats['total_sent'], 1)) * 100,
            'runtime_seconds': runtime.total_seconds(),
            'emails_per_minute': (self.stats['total_sent'] / max(runtime.total_seconds() / 60, 1)),
            'active_sessions': len(self.session_pool)
        }
        
        return stats
    
    def cleanup_sessions(self):
        """清理会话池"""
        try:
            for email, session in self.session_pool.items():
                session.close()
            
            self.session_pool.clear()
            logger.info("✅ 会话池清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理会话池失败: {e}")
    
    def _update_proxy_success(self, account_email: str):
        """更新代理成功统计"""
        try:
            proxy_info = self.proxy_manager.get_proxy_for_account(account_email)
            if proxy_info:
                proxy_id = f"{proxy_info.ip}:{proxy_info.port}"
                if proxy_id in self.proxy_manager.proxy_pool:
                    self.proxy_manager.proxy_pool[proxy_id].success_count += 1
                    self.proxy_manager._update_proxy_stats(proxy_id, proxy_info)
        except Exception as e:
            logger.error(f"更新代理成功统计失败: {e}")

    def _update_proxy_failure(self, account_email: str):
        """更新代理失败统计"""
        try:
            proxy_info = self.proxy_manager.get_proxy_for_account(account_email)
            if proxy_info:
                proxy_id = f"{proxy_info.ip}:{proxy_info.port}"
                if proxy_id in self.proxy_manager.proxy_pool:
                    self.proxy_manager.proxy_pool[proxy_id].failure_count += 1
                    self.proxy_manager._update_proxy_stats(proxy_id, proxy_info)
        except Exception as e:
            logger.error(f"更新代理失败统计失败: {e}")

    def get_proxy_statistics(self) -> Dict[str, Any]:
        """获取代理统计信息"""
        try:
            return self.proxy_manager.get_proxy_statistics()
        except Exception as e:
            logger.error(f"获取代理统计失败: {e}")
            return {}

    def rotate_account_proxy(self, account_email: str, reason: str = "manual") -> bool:
        """手动轮换账号代理"""
        try:
            return self.proxy_manager.rotate_proxy_for_account(account_email, reason)
        except Exception as e:
            logger.error(f"手动轮换代理失败: {e}")
            return False

    def check_proxy_health(self) -> Dict[str, bool]:
        """检查所有代理健康状态"""
        try:
            return self.proxy_manager.check_all_proxies_health()
        except Exception as e:
            logger.error(f"检查代理健康失败: {e}")
            return {}

    def __del__(self):
        """析构函数"""
        self.cleanup_sessions()
