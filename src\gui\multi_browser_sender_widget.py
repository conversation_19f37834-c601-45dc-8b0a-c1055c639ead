#!/usr/bin/env python3
"""
多浏览器发送器GUI界面
支持Cookie复用+代理IP+浏览器轮换发送邮件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import threading
from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from src.core.email_sending_scheduler import EmailSendingScheduler, SendingConfig
from src.core.multi_sender_manager import MultiSenderManager, MultiSenderConfig, RotationStrategy, IntervalStrategy
from src.core.send_mode_manager import SendModeManager, SendModeConfig, SendMode, Priority
from src.models.account import Account
from src.models.database import DatabaseManager
from src.gui.email_template_widget import EmailTemplateWidget
from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class MultiBrowserSenderWidget(QWidget):
    """多浏览器发送器界面"""
    
    # 信号定义
    stats_updated = pyqtSignal(dict)
    log_message = pyqtSignal(str, str)  # message, level
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        super().__init__()
        self.scheduler: Optional[EmailSendingScheduler] = None
        self.accounts: List[Account] = []
        self.is_sending = False

        # 数据库管理器
        if db_manager is None:
            from src.utils.config_manager import ConfigManager
            config_manager = ConfigManager()
            db_path = config_manager.get_database_path()
            self.db_manager = DatabaseManager(str(db_path))
        else:
            self.db_manager = db_manager

        # 定时器
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_stats_display)

        self.init_ui()
        self.connect_signals()

        # 加载模板
        self.load_templates()

        # 变量存储
        self.current_variables = {}

        # 多邮箱管理器
        self.multi_sender_manager: Optional[MultiSenderManager] = None

        # 发送模式管理器
        self.send_mode_manager: Optional[SendModeManager] = None
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("🚀 多浏览器邮件发送系统")
        # 增加界面高度，提供更多垂直空间
        self.setGeometry(100, 100, 1600, 1000)
        self.setMinimumSize(1400, 900)

        # 设置样式
        self.setStyleSheet("""
            QWidget {
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 9pt;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                font-size: 10pt;
                font-weight: bold;
            }
            QPushButton {
                background-color: #3498db;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
            QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 6px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                border: 2px solid #3498db;
            }
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
            QCheckBox {
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 1px solid #bdc3c7;
            }
            QCheckBox::indicator:checked {
                background-color: #3498db;
                border: 1px solid #3498db;
            }
            QProgressBar {
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                text-align: center;
                background-color: #ecf0f1;
            }
            QProgressBar::chunk {
                background-color: #27ae60;
                border-radius: 5px;
            }
        """)

        # 主布局 - 使用水平分割器
        main_layout = QHBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # 左侧面板
        left_panel = self.create_left_panel()

        # 右侧面板
        right_panel = self.create_right_panel()

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setStretchFactor(0, 1)  # 左侧占1/4
        splitter.setStretchFactor(1, 3)  # 右侧占3/4，给邮件编辑更多空间
        splitter.setSizes([400, 1200])  # 调整比例，右侧更宽

        main_layout.addWidget(splitter)
        self.setLayout(main_layout)

    def create_left_panel(self) -> QWidget:
        """创建左侧控制面板"""
        panel = QWidget()
        layout = QVBoxLayout()
        layout.setSpacing(10)  # 减少间距
        layout.setContentsMargins(5, 5, 5, 5)  # 减少边距

        # 使用滚动区域来容纳所有内容
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.NoFrame)  # 去除边框

        # 设置滚动区域样式，确保滚动条不遮挡内容
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f8f9fa;
                width: 8px;
                border-radius: 4px;
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background-color: #dee2e6;
                border-radius: 4px;
                min-height: 20px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #adb5bd;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)

        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()
        scroll_layout.setSpacing(8)  # 减少间距
        scroll_layout.setContentsMargins(5, 5, 20, 5)  # 右侧留出更多滚动条空间

        # 快速配置区域
        quick_config = self.create_quick_config_group()
        scroll_layout.addWidget(quick_config)

        # 账号管理区域
        account_group = self.create_account_group()
        scroll_layout.addWidget(account_group)

        # 控制按钮区域
        control_group = self.create_control_group()
        scroll_layout.addWidget(control_group)

        # 实时状态区域
        status_group = self.create_compact_status_group()
        scroll_layout.addWidget(status_group)

        scroll_layout.addStretch()
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)

        layout.addWidget(scroll_area)
        panel.setLayout(layout)
        return panel

    def create_right_panel(self) -> QWidget:
        """创建右侧主工作区域 - 带垂直滚动支持"""
        panel = QWidget()

        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)
        main_layout.setSpacing(5)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.NoFrame)  # 去除边框

        # 创建滚动内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout()
        scroll_layout.setContentsMargins(5, 5, 5, 5)
        scroll_layout.setSpacing(10)

        # 邮件任务区域（选项卡）- 不限制高度，让其自然展开
        task_group = self.create_task_group()
        scroll_layout.addWidget(task_group)

        # 详细状态区域 - 紧凑显示
        detailed_status = self.create_detailed_status_group()
        scroll_layout.addWidget(detailed_status)

        # 日志区域 - 紧凑显示
        log_group = self.create_log_group()
        scroll_layout.addWidget(log_group)

        # 添加弹性空间，确保内容顶部对齐
        scroll_layout.addStretch()

        scroll_content.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_content)

        # 将滚动区域添加到主布局
        main_layout.addWidget(scroll_area)
        panel.setLayout(main_layout)

        return panel

    def create_quick_config_group(self) -> QGroupBox:
        """创建快速配置区域"""
        group = QGroupBox("⚙️ 快速配置")
        layout = QVBoxLayout()
        layout.setSpacing(12)

        # 基础设置
        basic_frame = QFrame()
        basic_frame.setFrameStyle(QFrame.Box)
        basic_frame.setStyleSheet("QFrame { background-color: white; border-radius: 6px; padding: 8px; }")
        basic_layout = QFormLayout()
        basic_layout.setSpacing(8)

        # 浏览器数量
        self.max_browsers_spin = QSpinBox()
        self.max_browsers_spin.setRange(1, 10)
        self.max_browsers_spin.setValue(3)
        self.max_browsers_spin.setToolTip("同时运行的浏览器数量")
        basic_layout.addRow("🌐 浏览器数量:", self.max_browsers_spin)

        # 发送间隔
        self.send_interval_spin = QDoubleSpinBox()
        self.send_interval_spin.setRange(0.1, 60.0)
        self.send_interval_spin.setValue(2.0)
        self.send_interval_spin.setSingleStep(0.1)
        self.send_interval_spin.setSuffix(" 秒")
        self.send_interval_spin.setToolTip("邮件发送间隔时间")
        basic_layout.addRow("⏱️ 发送间隔:", self.send_interval_spin)

        # 每账号发送数量
        self.emails_per_account_spin = QSpinBox()
        self.emails_per_account_spin.setRange(1, 100)
        self.emails_per_account_spin.setValue(5)
        self.emails_per_account_spin.setToolTip("每个账号发送的邮件数量")
        basic_layout.addRow("📧 每账号发送:", self.emails_per_account_spin)

        basic_frame.setLayout(basic_layout)
        layout.addWidget(basic_frame)

        # 高级设置（默认展开）
        self.advanced_group = QGroupBox("🔧 高级设置")
        self.advanced_group.setCheckable(True)
        self.advanced_group.setChecked(True)  # 默认展开
        advanced_layout = self.create_advanced_config_layout()
        self.advanced_group.setLayout(advanced_layout)
        layout.addWidget(self.advanced_group)

        group.setLayout(layout)
        return group

    def create_advanced_config_layout(self) -> QVBoxLayout:
        """创建高级配置布局"""
        layout = QVBoxLayout()
        layout.setSpacing(8)  # 减少间距

        # 多邮箱设置
        multi_frame = QFrame()
        multi_frame.setFrameStyle(QFrame.Box)
        multi_frame.setStyleSheet("QFrame { background-color: white; border-radius: 6px; padding: 6px; }")
        multi_layout = QFormLayout()
        multi_layout.setSpacing(4)  # 减少间距

        # 每个邮箱发送数量
        self.emails_per_sender_spin = QSpinBox()
        self.emails_per_sender_spin.setRange(1, 100)
        self.emails_per_sender_spin.setValue(10)
        multi_layout.addRow("📮 每邮箱发送:", self.emails_per_sender_spin)

        # 轮换策略
        self.rotation_strategy_combo = QComboBox()
        self.rotation_strategy_combo.addItems([
            "顺序轮换", "随机轮换", "负载均衡", "按成功率轮换"
        ])
        multi_layout.addRow("🔄 轮换策略:", self.rotation_strategy_combo)

        # 发送模式
        self.send_mode_combo = QComboBox()
        self.send_mode_combo.addItems([
            "单个逐渐发送", "批量逐渐发送", "并发发送", "智能发送"
        ])
        self.send_mode_combo.currentTextChanged.connect(self.on_send_mode_changed)
        multi_layout.addRow("🚀 发送模式:", self.send_mode_combo)

        multi_frame.setLayout(multi_layout)
        layout.addWidget(multi_frame)

        # 浏览器设置
        browser_frame = QFrame()
        browser_frame.setFrameStyle(QFrame.Box)
        browser_frame.setStyleSheet("QFrame { background-color: white; border-radius: 6px; padding: 8px; }")
        browser_layout = QFormLayout()
        browser_layout.setSpacing(6)

        # 窗口大小
        size_layout = QHBoxLayout()
        self.window_width_spin = QSpinBox()
        self.window_width_spin.setRange(400, 1920)
        self.window_width_spin.setValue(800)
        self.window_height_spin = QSpinBox()
        self.window_height_spin.setRange(300, 1080)
        self.window_height_spin.setValue(600)
        size_layout.addWidget(self.window_width_spin)
        size_layout.addWidget(QLabel("×"))
        size_layout.addWidget(self.window_height_spin)
        size_layout.addStretch()
        browser_layout.addRow("📐 窗口大小:", size_layout)

        # 浏览器选项
        options_layout = QVBoxLayout()
        self.minimize_browsers_check = QCheckBox("最小化浏览器窗口")
        self.minimize_browsers_check.setChecked(True)
        self.rotate_accounts_check = QCheckBox("循环轮换账号")
        self.rotate_accounts_check.setChecked(True)
        options_layout.addWidget(self.minimize_browsers_check)
        options_layout.addWidget(self.rotate_accounts_check)
        browser_layout.addRow("⚙️ 浏览器选项:", options_layout)

        browser_frame.setLayout(browser_layout)
        layout.addWidget(browser_frame)

        # 其他设置
        other_frame = QFrame()
        other_frame.setFrameStyle(QFrame.Box)
        other_frame.setStyleSheet("QFrame { background-color: white; border-radius: 6px; padding: 8px; }")
        other_layout = QFormLayout()
        other_layout.setSpacing(6)

        # 固定抄送邮箱
        self.cc_emails_edit = QLineEdit()
        self.cc_emails_edit.setPlaceholderText("多个邮箱用分号分隔")
        other_layout.addRow("📋 抄送邮箱:", self.cc_emails_edit)

        # 间隔策略
        self.interval_strategy_combo = QComboBox()
        self.interval_strategy_combo.addItems([
            "固定间隔", "随机间隔", "递增间隔", "智能间隔"
        ])
        other_layout.addRow("⏰ 间隔策略:", self.interval_strategy_combo)

        # 批量大小
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 100)
        self.batch_size_spin.setValue(10)
        other_layout.addRow("📦 批量大小:", self.batch_size_spin)

        # 并发数量
        self.concurrent_count_spin = QSpinBox()
        self.concurrent_count_spin.setRange(1, 20)
        self.concurrent_count_spin.setValue(5)
        other_layout.addRow("⚡ 并发数量:", self.concurrent_count_spin)

        # 发送优先级
        self.priority_combo = QComboBox()
        self.priority_combo.addItems([
            "普通", "高", "最高", "低"
        ])
        other_layout.addRow("🎯 优先级:", self.priority_combo)

        other_frame.setLayout(other_layout)
        layout.addWidget(other_frame)

        return layout

    def create_config_group(self) -> QGroupBox:
        """创建配置区域"""
        group = QGroupBox("发送配置")
        layout = QGridLayout()
        
        # 浏览器数量
        layout.addWidget(QLabel("同时在线浏览器数量:"), 0, 0)
        self.max_browsers_spin = QSpinBox()
        self.max_browsers_spin.setRange(1, 10)
        self.max_browsers_spin.setValue(3)
        layout.addWidget(self.max_browsers_spin, 0, 1)
        
        # 每账号发送数量
        layout.addWidget(QLabel("每账号发送邮件数量:"), 0, 2)
        self.emails_per_account_spin = QSpinBox()
        self.emails_per_account_spin.setRange(1, 100)
        self.emails_per_account_spin.setValue(5)
        layout.addWidget(self.emails_per_account_spin, 0, 3)
        
        # 发送间隔
        layout.addWidget(QLabel("邮件发送间隔(秒):"), 1, 0)
        self.send_interval_spin = QDoubleSpinBox()
        self.send_interval_spin.setRange(0.1, 60.0)
        self.send_interval_spin.setValue(2.0)
        self.send_interval_spin.setSingleStep(0.1)
        layout.addWidget(self.send_interval_spin, 1, 1)
        
        # 浏览器窗口宽度
        layout.addWidget(QLabel("浏览器窗口宽度:"), 1, 2)
        self.window_width_spin = QSpinBox()
        self.window_width_spin.setRange(400, 1920)
        self.window_width_spin.setValue(800)
        layout.addWidget(self.window_width_spin, 1, 3)
        
        # 浏览器窗口高度
        layout.addWidget(QLabel("浏览器窗口高度:"), 2, 0)
        self.window_height_spin = QSpinBox()
        self.window_height_spin.setRange(300, 1080)
        self.window_height_spin.setValue(600)
        layout.addWidget(self.window_height_spin, 2, 1)
        
        # 最小化浏览器
        self.minimize_browsers_check = QCheckBox("最小化浏览器窗口")
        self.minimize_browsers_check.setChecked(True)
        layout.addWidget(self.minimize_browsers_check, 2, 2)
        
        # 轮换账号
        self.rotate_accounts_check = QCheckBox("循环轮换账号")
        self.rotate_accounts_check.setChecked(True)
        layout.addWidget(self.rotate_accounts_check, 2, 3)

        # 多邮箱发送设置
        layout.addWidget(QLabel("每个邮箱发送数量:"), 3, 0)
        self.emails_per_sender_spin = QSpinBox()
        self.emails_per_sender_spin.setRange(1, 100)
        self.emails_per_sender_spin.setValue(10)
        layout.addWidget(self.emails_per_sender_spin, 3, 1)

        # 固定抄送邮箱
        layout.addWidget(QLabel("固定抄送邮箱:"), 3, 2)
        self.cc_emails_edit = QLineEdit()
        self.cc_emails_edit.setPlaceholderText("多个邮箱用分号分隔")
        layout.addWidget(self.cc_emails_edit, 3, 3)

        # 邮箱轮换策略
        layout.addWidget(QLabel("轮换策略:"), 4, 0)
        self.rotation_strategy_combo = QComboBox()
        self.rotation_strategy_combo.addItems([
            "顺序轮换", "随机轮换", "负载均衡", "按成功率轮换"
        ])
        layout.addWidget(self.rotation_strategy_combo, 4, 1)

        # 发送间隔策略
        layout.addWidget(QLabel("间隔策略:"), 4, 2)
        self.interval_strategy_combo = QComboBox()
        self.interval_strategy_combo.addItems([
            "固定间隔", "随机间隔", "递增间隔", "智能间隔"
        ])
        layout.addWidget(self.interval_strategy_combo, 4, 3)

        # 发送模式
        layout.addWidget(QLabel("发送模式:"), 5, 0)
        self.send_mode_combo = QComboBox()
        self.send_mode_combo.addItems([
            "单个逐渐发送", "批量逐渐发送", "并发发送", "智能发送"
        ])
        self.send_mode_combo.currentTextChanged.connect(self.on_send_mode_changed)
        layout.addWidget(self.send_mode_combo, 5, 1)

        # 批量大小（仅批量模式）
        layout.addWidget(QLabel("批量大小:"), 5, 2)
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 100)
        self.batch_size_spin.setValue(10)
        layout.addWidget(self.batch_size_spin, 5, 3)

        # 并发数量（仅并发模式）
        layout.addWidget(QLabel("并发数量:"), 6, 0)
        self.concurrent_count_spin = QSpinBox()
        self.concurrent_count_spin.setRange(1, 20)
        self.concurrent_count_spin.setValue(5)
        layout.addWidget(self.concurrent_count_spin, 6, 1)

        # 发送优先级
        layout.addWidget(QLabel("发送优先级:"), 6, 2)
        self.priority_combo = QComboBox()
        self.priority_combo.addItems([
            "普通", "高", "最高", "低"
        ])
        layout.addWidget(self.priority_combo, 6, 3)
        
        group.setLayout(layout)
        return group
    
    def create_account_group(self) -> QGroupBox:
        """创建账号管理区域"""
        group = QGroupBox("👤 账号管理")
        layout = QVBoxLayout()
        layout.setSpacing(10)

        # 账号统计信息
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Box)
        stats_frame.setStyleSheet("QFrame { background-color: #e8f4fd; border-radius: 6px; padding: 8px; }")
        stats_layout = QHBoxLayout()

        self.total_accounts_label = QLabel("总账号: 0")
        self.active_accounts_label = QLabel("可用: 0")
        self.cookie_valid_label = QLabel("Cookie有效: 0")

        for label in [self.total_accounts_label, self.active_accounts_label, self.cookie_valid_label]:
            label.setStyleSheet("font-weight: bold; color: #2c3e50;")

        stats_layout.addWidget(self.total_accounts_label)
        stats_layout.addWidget(QLabel("|"))
        stats_layout.addWidget(self.active_accounts_label)
        stats_layout.addWidget(QLabel("|"))
        stats_layout.addWidget(self.cookie_valid_label)
        stats_layout.addStretch()

        stats_frame.setLayout(stats_layout)
        layout.addWidget(stats_frame)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.load_accounts_btn = QPushButton("🔄 加载账号")
        self.load_accounts_btn.clicked.connect(self.load_accounts)
        self.load_accounts_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                font-size: 9pt;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        button_layout.addWidget(self.load_accounts_btn)

        self.refresh_cookies_btn = QPushButton("🍪 刷新状态")
        self.refresh_cookies_btn.clicked.connect(self.refresh_cookie_status)
        self.refresh_cookies_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                font-size: 9pt;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        button_layout.addWidget(self.refresh_cookies_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 账号列表（紧凑显示）
        self.account_table = QTableWidget()
        self.account_table.setColumnCount(3)
        self.account_table.setHorizontalHeaderLabels(["邮箱", "状态", "发送数"])
        self.account_table.horizontalHeader().setStretchLastSection(True)
        self.account_table.setMaximumHeight(150)  # 减少高度给其他组件更多空间
        self.account_table.setMinimumHeight(100)  # 减少最小高度
        self.account_table.setAlternatingRowColors(True)
        self.account_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        # 设置列宽
        header = self.account_table.horizontalHeader()
        header.resizeSection(0, 120)  # 邮箱列
        header.resizeSection(1, 60)   # 状态列
        header.setStretchLastSection(True)  # 发送数列自适应

        layout.addWidget(self.account_table)

        group.setLayout(layout)
        return group

    def create_control_group(self) -> QGroupBox:
        """创建控制按钮区域"""
        group = QGroupBox("🎮 发送控制")
        layout = QVBoxLayout()
        layout.setSpacing(10)

        # 主要控制按钮
        main_buttons_layout = QHBoxLayout()

        self.start_btn = QPushButton("🚀 开始发送")
        self.start_btn.clicked.connect(self.start_sending)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                font-size: 11pt;
                font-weight: bold;
                padding: 12px 20px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        main_buttons_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("⏹️ 停止发送")
        self.stop_btn.clicked.connect(self.stop_sending)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                font-size: 11pt;
                font-weight: bold;
                padding: 12px 20px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        main_buttons_layout.addWidget(self.stop_btn)

        layout.addLayout(main_buttons_layout)

        # 辅助控制按钮
        aux_buttons_layout = QHBoxLayout()

        self.pause_btn = QPushButton("⏸️ 暂停")
        self.pause_btn.clicked.connect(self.pause_sending)
        self.pause_btn.setEnabled(False)
        aux_buttons_layout.addWidget(self.pause_btn)

        self.resume_btn = QPushButton("▶️ 继续")
        self.resume_btn.clicked.connect(self.resume_sending)
        self.resume_btn.setEnabled(False)
        aux_buttons_layout.addWidget(self.resume_btn)

        self.clear_queue_btn = QPushButton("🗑️ 清空队列")
        self.clear_queue_btn.clicked.connect(self.clear_queue)
        aux_buttons_layout.addWidget(self.clear_queue_btn)

        layout.addLayout(aux_buttons_layout)

        group.setLayout(layout)
        return group

    def create_compact_status_group(self) -> QGroupBox:
        """创建紧凑状态显示区域"""
        group = QGroupBox("📊 实时状态")
        layout = QVBoxLayout()
        layout.setSpacing(8)

        # 发送进度
        progress_frame = QFrame()
        progress_frame.setFrameStyle(QFrame.Box)
        progress_frame.setStyleSheet("QFrame { background-color: white; border-radius: 6px; padding: 8px; }")
        progress_layout = QVBoxLayout()

        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p% (%v/%m)")
        progress_layout.addWidget(self.progress_bar)

        self.progress_label = QLabel("准备就绪")
        self.progress_label.setAlignment(Qt.AlignCenter)
        self.progress_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        progress_layout.addWidget(self.progress_label)

        progress_frame.setLayout(progress_layout)
        layout.addWidget(progress_frame)

        # 关键统计
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Box)
        stats_frame.setStyleSheet("QFrame { background-color: #f8f9fa; border-radius: 6px; padding: 6px; }")
        stats_layout = QGridLayout()
        stats_layout.setSpacing(4)

        # 统计标签
        self.sent_count_label = QLabel("已发送: 0")
        self.failed_count_label = QLabel("失败: 0")
        self.queue_count_label = QLabel("队列: 0")
        self.speed_label = QLabel("速度: 0/分钟")

        for label in [self.sent_count_label, self.failed_count_label, self.queue_count_label, self.speed_label]:
            label.setStyleSheet("font-size: 8pt; font-weight: bold;")

        stats_layout.addWidget(self.sent_count_label, 0, 0)
        stats_layout.addWidget(self.failed_count_label, 0, 1)
        stats_layout.addWidget(self.queue_count_label, 1, 0)
        stats_layout.addWidget(self.speed_label, 1, 1)

        stats_frame.setLayout(stats_layout)
        layout.addWidget(stats_frame)

        group.setLayout(layout)
        return group

    def create_detailed_status_group(self) -> QGroupBox:
        """创建详细状态显示区域"""
        group = QGroupBox("📈 详细统计")
        layout = QVBoxLayout()

        # 创建状态表格
        self.status_table = QTableWidget()
        self.status_table.setColumnCount(4)
        self.status_table.setHorizontalHeaderLabels(["指标", "当前值", "总计", "百分比"])
        self.status_table.horizontalHeader().setStretchLastSection(True)
        # 设置合理的高度范围
        self.status_table.setMinimumHeight(120)  # 保持合理的最小高度
        self.status_table.setMaximumHeight(200)  # 设置最大高度，避免占用过多空间

        # 初始化状态数据
        status_items = [
            ["发送成功", "0", "0", "0%"],
            ["发送失败", "0", "0", "0%"],
            ["队列等待", "0", "0", "0%"],
            ["发送速度", "0/分钟", "-", "-"],
            ["平均响应", "0ms", "-", "-"]
        ]

        self.status_table.setRowCount(len(status_items))
        for row, item in enumerate(status_items):
            for col, value in enumerate(item):
                self.status_table.setItem(row, col, QTableWidgetItem(value))

        layout.addWidget(self.status_table)

        group.setLayout(layout)
        return group

    def create_task_group(self) -> QGroupBox:
        """创建邮件任务区域"""
        group = QGroupBox("邮件任务管理")
        layout = QVBoxLayout()

        # 创建选项卡
        self.task_tab_widget = QTabWidget()

        # 邮件发送选项卡
        send_tab = self.create_send_tab()
        self.task_tab_widget.addTab(send_tab, "邮件发送")

        # 模板管理选项卡
        template_tab = self.create_template_tab()
        self.task_tab_widget.addTab(template_tab, "模板管理")

        # 发送统计选项卡
        statistics_tab = self.create_statistics_tab()
        self.task_tab_widget.addTab(statistics_tab, "发送统计")

        # 数据源管理选项卡
        data_source_tab = self.create_data_source_tab()
        self.task_tab_widget.addTab(data_source_tab, "数据源管理")

        # 导入模板选项卡
        import_template_tab = self.create_import_template_tab()
        self.task_tab_widget.addTab(import_template_tab, "导入模板")

        layout.addWidget(self.task_tab_widget)
        group.setLayout(layout)
        return group

    def create_send_tab(self) -> QWidget:
        """创建邮件发送选项卡"""
        tab = QWidget()
        layout = QVBoxLayout()
        layout.setSpacing(15)

        # 数据源选择区域
        source_group = QGroupBox("📂 数据源选择")
        source_layout = QHBoxLayout()

        source_layout.addWidget(QLabel("数据源:"))
        self.data_source_combo = QComboBox()
        self.data_source_combo.addItems([
            "手动输入", "导入数据", "文件监控数据", "数据库查询"
        ])
        self.data_source_combo.currentTextChanged.connect(self.on_data_source_changed)
        source_layout.addWidget(self.data_source_combo)

        self.manage_data_source_btn = QPushButton("📊 管理数据源")
        self.manage_data_source_btn.clicked.connect(self.manage_data_sources)
        source_layout.addWidget(self.manage_data_source_btn)

        source_layout.addStretch()
        source_group.setLayout(source_layout)
        layout.addWidget(source_group)

        # 邮件内容编辑区域
        content_group = QGroupBox("✉️ 邮件内容")
        content_layout = QVBoxLayout()
        content_layout.setSpacing(10)

        # 收件人输入
        recipient_frame = QFrame()
        recipient_frame.setFrameStyle(QFrame.Box)
        recipient_frame.setStyleSheet("QFrame { background-color: white; border-radius: 6px; padding: 10px; }")
        recipient_layout = QVBoxLayout()

        recipient_layout.addWidget(QLabel("📧 收件人邮箱:"))
        self.to_email_edit = QLineEdit()
        self.to_email_edit.setPlaceholderText("输入收件人邮箱，多个邮箱用分号(;)分隔")
        self.to_email_edit.setMinimumHeight(35)
        recipient_layout.addWidget(self.to_email_edit)

        recipient_frame.setLayout(recipient_layout)
        content_layout.addWidget(recipient_frame)

        # 模板选择
        template_frame = QFrame()
        template_frame.setFrameStyle(QFrame.Box)
        template_frame.setStyleSheet("QFrame { background-color: white; border-radius: 6px; padding: 10px; }")
        template_layout = QHBoxLayout()

        template_layout.addWidget(QLabel("📋 邮件模板:"))
        self.template_combo = QComboBox()
        self.template_combo.addItem("手动输入", None)
        self.template_combo.currentIndexChanged.connect(self.on_template_selected)
        self.template_combo.setMinimumWidth(200)
        template_layout.addWidget(self.template_combo)

        self.refresh_template_btn = QPushButton("🔄 刷新")
        self.refresh_template_btn.clicked.connect(self.load_templates)
        template_layout.addWidget(self.refresh_template_btn)

        template_layout.addStretch()
        template_frame.setLayout(template_layout)
        content_layout.addWidget(template_frame)

        # 主题和内容
        email_frame = QFrame()
        email_frame.setFrameStyle(QFrame.Box)
        email_frame.setStyleSheet("QFrame { background-color: white; border-radius: 6px; padding: 10px; }")
        email_layout = QVBoxLayout()

        # 主题
        email_layout.addWidget(QLabel("📝 邮件主题:"))
        self.subject_edit = QLineEdit()
        self.subject_edit.setPlaceholderText("输入邮件主题，支持变量如 {name}、{company}")
        self.subject_edit.setMinimumHeight(35)
        email_layout.addWidget(self.subject_edit)

        # 内容
        email_layout.addWidget(QLabel("📄 邮件内容:"))
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("输入邮件内容，支持变量如 {name}、{company}、{date} 等")
        self.content_edit.setMinimumHeight(250)  # 减少最小高度，更合理的空间分配
        self.content_edit.setMaximumHeight(350)  # 设置最大高度，避免占用过多空间
        email_layout.addWidget(self.content_edit)

        # 选项行
        options_layout = QHBoxLayout()

        options_layout.addWidget(QLabel("📋 内容类型:"))
        self.content_type_combo = QComboBox()
        self.content_type_combo.addItems(["text/plain", "text/html"])
        options_layout.addWidget(self.content_type_combo)

        options_layout.addStretch()

        self.variables_btn = QPushButton("🔧 设置变量")
        self.variables_btn.clicked.connect(self.set_variables)
        self.variables_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        options_layout.addWidget(self.variables_btn)

        self.add_task_btn = QPushButton("➕ 添加任务")
        self.add_task_btn.clicked.connect(self.add_email_task)
        self.add_task_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        options_layout.addWidget(self.add_task_btn)

        email_layout.addLayout(options_layout)
        email_frame.setLayout(email_layout)
        content_layout.addWidget(email_frame)

        content_group.setLayout(content_layout)
        layout.addWidget(content_group)

        # 批量操作区域
        batch_group = QGroupBox("📦 批量操作")
        batch_layout = QHBoxLayout()

        self.import_tasks_btn = QPushButton("📥 批量导入")
        self.import_tasks_btn.clicked.connect(self.import_email_tasks)
        self.import_tasks_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        batch_layout.addWidget(self.import_tasks_btn)

        self.download_template_btn = QPushButton("📄 下载模板")
        self.download_template_btn.clicked.connect(self.download_import_template)
        batch_layout.addWidget(self.download_template_btn)

        self.clear_tasks_btn = QPushButton("🗑️ 清空任务")
        self.clear_tasks_btn.clicked.connect(self.clear_tasks)
        self.clear_tasks_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        batch_layout.addWidget(self.clear_tasks_btn)

        batch_layout.addStretch()
        batch_group.setLayout(batch_layout)
        layout.addWidget(batch_group)

        # 任务列表
        task_list_group = QGroupBox("📋 任务队列")
        task_list_layout = QVBoxLayout()

        # 任务统计
        task_stats_layout = QHBoxLayout()
        self.task_count_label = QLabel("总任务: 0")
        self.pending_count_label = QLabel("等待: 0")
        self.running_count_label = QLabel("执行中: 0")
        self.completed_count_label = QLabel("已完成: 0")

        for label in [self.task_count_label, self.pending_count_label, self.running_count_label, self.completed_count_label]:
            label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 4px 8px; background-color: #ecf0f1; border-radius: 4px;")

        task_stats_layout.addWidget(self.task_count_label)
        task_stats_layout.addWidget(self.pending_count_label)
        task_stats_layout.addWidget(self.running_count_label)
        task_stats_layout.addWidget(self.completed_count_label)
        task_stats_layout.addStretch()

        task_list_layout.addLayout(task_stats_layout)

        # 任务表格
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(5)
        self.task_table.setHorizontalHeaderLabels(["收件人", "主题", "类型", "状态", "时间"])
        self.task_table.setAlternatingRowColors(True)
        self.task_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        # 在滚动区域中，让任务表格根据内容自适应高度
        self.task_table.setMinimumHeight(180)  # 保持合理的最小高度

        # 设置列宽
        header = self.task_table.horizontalHeader()
        header.resizeSection(0, 180)  # 收件人
        header.resizeSection(1, 200)  # 主题
        header.resizeSection(2, 80)   # 类型
        header.resizeSection(3, 80)   # 状态
        header.setStretchLastSection(True)  # 时间

        task_list_layout.addWidget(self.task_table)
        task_list_group.setLayout(task_list_layout)
        layout.addWidget(task_list_group)

        tab.setLayout(layout)
        return tab

    def create_template_tab(self) -> QWidget:
        """创建模板管理选项卡"""
        try:
            self.template_widget = EmailTemplateWidget(self.db_manager)
            self.template_widget.template_selected.connect(self.on_template_widget_selected)
            return self.template_widget
        except Exception as e:
            logger.error(f"创建模板管理选项卡失败: {e}")
            # 返回占位符
            tab = QWidget()
            layout = QVBoxLayout()
            label = QLabel(f"模板管理功能暂时不可用\n错误: {e}")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            tab.setLayout(layout)
            return tab

    def create_statistics_tab(self) -> QWidget:
        """创建发送统计选项卡"""
        try:
            from src.gui.send_statistics_widget import SendStatisticsWidget
            self.statistics_widget = SendStatisticsWidget(self.db_manager)
            return self.statistics_widget
        except Exception as e:
            logger.error(f"创建发送统计选项卡失败: {e}")
            # 返回占位符
            tab = QWidget()
            layout = QVBoxLayout()
            label = QLabel(f"发送统计功能暂时不可用\n错误: {e}")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            tab.setLayout(layout)
            return tab

    def create_data_source_tab(self) -> QWidget:
        """创建数据源管理选项卡"""
        try:
            from src.gui.data_source_widget import DataSourceWidget
            self.data_source_widget = DataSourceWidget(self.db_manager)
            self.data_source_widget.recipients_selected.connect(self.on_recipients_selected)
            return self.data_source_widget
        except Exception as e:
            logger.error(f"创建数据源管理选项卡失败: {e}")
            # 返回占位符
            tab = QWidget()
            layout = QVBoxLayout()
            label = QLabel(f"数据源管理功能暂时不可用\n错误: {e}")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            tab.setLayout(layout)
            return tab

    def create_import_template_tab(self) -> QWidget:
        """创建导入模板选项卡"""
        try:
            from src.gui.import_template_widget import ImportTemplateWidget
            self.import_template_widget = ImportTemplateWidget()
            self.import_template_widget.template_selected.connect(self.on_template_selected)
            return self.import_template_widget
        except Exception as e:
            logger.error(f"创建导入模板选项卡失败: {e}")
            # 返回占位符
            tab = QWidget()
            layout = QVBoxLayout()
            label = QLabel(f"导入模板功能暂时不可用\n错误: {e}")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            tab.setLayout(layout)
            return tab

    def create_control_group(self) -> QGroupBox:
        """创建控制按钮区域"""
        group = QGroupBox("发送控制")
        layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始发送")
        self.start_btn.clicked.connect(self.start_sending)
        self.start_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止发送")
        self.stop_btn.clicked.connect(self.stop_sending)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        layout.addWidget(self.stop_btn)
        
        self.pause_btn = QPushButton("暂停发送")
        self.pause_btn.clicked.connect(self.pause_sending)
        self.pause_btn.setEnabled(False)
        layout.addWidget(self.pause_btn)
        
        layout.addStretch()
        
        # 工作线程数
        layout.addWidget(QLabel("工作线程数:"))
        self.worker_threads_spin = QSpinBox()
        self.worker_threads_spin.setRange(1, 5)
        self.worker_threads_spin.setValue(2)
        layout.addWidget(self.worker_threads_spin)
        
        group.setLayout(layout)
        return group
    
    def create_status_group(self) -> QGroupBox:
        """创建状态监控区域"""
        group = QGroupBox("发送状态")
        layout = QGridLayout()
        
        # 统计标签
        self.total_tasks_label = QLabel("总任务: 0")
        layout.addWidget(self.total_tasks_label, 0, 0)
        
        self.sent_success_label = QLabel("发送成功: 0")
        layout.addWidget(self.sent_success_label, 0, 1)
        
        self.sent_failed_label = QLabel("发送失败: 0")
        layout.addWidget(self.sent_failed_label, 0, 2)
        
        self.pending_tasks_label = QLabel("待发送: 0")
        layout.addWidget(self.pending_tasks_label, 0, 3)
        
        self.send_rate_label = QLabel("发送速率: 0 封/分钟")
        layout.addWidget(self.send_rate_label, 1, 0)
        
        self.running_time_label = QLabel("运行时间: 0 秒")
        layout.addWidget(self.running_time_label, 1, 1)
        
        self.active_browsers_label = QLabel("活跃浏览器: 0/0")
        layout.addWidget(self.active_browsers_label, 1, 2)
        
        # 浏览器状态表格
        self.browser_status_table = QTableWidget()
        self.browser_status_table.setColumnCount(4)
        self.browser_status_table.setHorizontalHeaderLabels(["浏览器ID", "状态", "当前账号", "已发送"])
        self.browser_status_table.setMaximumHeight(150)
        layout.addWidget(self.browser_status_table, 2, 0, 1, 4)

        # 发送账号统计表格
        self.sender_stats_table = QTableWidget()
        self.sender_stats_table.setColumnCount(6)
        self.sender_stats_table.setHorizontalHeaderLabels([
            "发送邮箱", "已发送", "成功数", "成功率", "当前负载", "平均响应时间"
        ])
        self.sender_stats_table.setMaximumHeight(150)
        layout.addWidget(self.sender_stats_table, 3, 0, 1, 4)
        
        group.setLayout(layout)
        return group
    
    def create_log_group(self) -> QGroupBox:
        """创建日志区域"""
        group = QGroupBox("📝 运行日志")
        layout = QVBoxLayout()
        layout.setSpacing(8)

        # 日志级别过滤
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("日志级别:"))

        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["全部", "信息", "警告", "错误"])
        self.log_level_combo.currentTextChanged.connect(self.filter_logs)
        filter_layout.addWidget(self.log_level_combo)

        filter_layout.addStretch()

        self.auto_scroll_check = QCheckBox("自动滚动")
        self.auto_scroll_check.setChecked(True)
        filter_layout.addWidget(self.auto_scroll_check)

        layout.addLayout(filter_layout)

        # 日志文本区域
        self.log_text = QTextEdit()
        # 设置合理的高度范围
        self.log_text.setMinimumHeight(120)  # 减少最小高度
        self.log_text.setMaximumHeight(200)  # 设置最大高度，避免占用过多空间
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 9pt;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        layout.addWidget(self.log_text)

        # 日志控制按钮
        log_control_layout = QHBoxLayout()

        self.clear_log_btn = QPushButton("🗑️ 清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        log_control_layout.addWidget(self.clear_log_btn)

        self.save_log_btn = QPushButton("💾 保存日志")
        self.save_log_btn.clicked.connect(self.save_log)
        log_control_layout.addWidget(self.save_log_btn)

        log_control_layout.addStretch()

        # 日志统计
        self.log_count_label = QLabel("日志条数: 0")
        self.log_count_label.setStyleSheet("font-weight: bold; color: #7f8c8d;")
        log_control_layout.addWidget(self.log_count_label)

        layout.addLayout(log_control_layout)

        group.setLayout(layout)
        return group

    def filter_logs(self):
        """过滤日志显示"""
        # 这里可以实现日志级别过滤功能
        pass

    def save_log(self):
        """保存日志到文件"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存日志", f"log_{QDateTime.currentDateTime().toString('yyyyMMdd_hhmmss')}.txt",
                "Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())

                QMessageBox.information(self, "成功", f"日志已保存到: {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存日志失败: {e}")

    def update_account_stats(self):
        """更新账号统计信息"""
        total = len(self.accounts)
        active = sum(1 for acc in self.accounts if acc.status == 'active')
        cookie_valid = sum(1 for acc in self.accounts if hasattr(acc, 'cookie_valid') and acc.cookie_valid)

        self.total_accounts_label.setText(f"总账号: {total}")
        self.active_accounts_label.setText(f"可用: {active}")
        self.cookie_valid_label.setText(f"Cookie有效: {cookie_valid}")

    def update_task_stats(self):
        """更新任务统计信息"""
        total = self.task_table.rowCount()
        pending = 0
        running = 0
        completed = 0

        for row in range(total):
            status_item = self.task_table.item(row, 3)  # 状态列
            if status_item:
                status = status_item.text()
                if status == "等待":
                    pending += 1
                elif status == "执行中":
                    running += 1
                elif status == "已完成":
                    completed += 1

        self.task_count_label.setText(f"总任务: {total}")
        self.pending_count_label.setText(f"等待: {pending}")
        self.running_count_label.setText(f"执行中: {running}")
        self.completed_count_label.setText(f"已完成: {completed}")

    def pause_sending(self):
        """暂停发送"""
        if self.scheduler:
            # 这里实现暂停逻辑
            self.pause_btn.setEnabled(False)
            self.resume_btn.setEnabled(True)
            self.log_message.emit("发送已暂停", "info")

    def resume_sending(self):
        """恢复发送"""
        if self.scheduler:
            # 这里实现恢复逻辑
            self.pause_btn.setEnabled(True)
            self.resume_btn.setEnabled(False)
            self.log_message.emit("发送已恢复", "info")

    def clear_queue(self):
        """清空发送队列"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空发送队列吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.task_table.setRowCount(0)
            self.update_task_stats()
            self.log_message.emit("发送队列已清空", "info")

    def connect_signals(self):
        """连接信号"""
        self.stats_updated.connect(self.on_stats_updated)
        self.log_message.connect(self.on_log_message)
    
    def get_sending_config(self) -> SendingConfig:
        """获取发送配置"""
        return SendingConfig(
            max_browsers=self.max_browsers_spin.value(),
            emails_per_account=self.emails_per_account_spin.value(),
            send_interval=self.send_interval_spin.value(),
            browser_window_width=self.window_width_spin.value(),
            browser_window_height=self.window_height_spin.value(),
            minimize_browsers=self.minimize_browsers_check.isChecked(),
            rotate_accounts=self.rotate_accounts_check.isChecked()
        )

    def get_multi_sender_config(self) -> MultiSenderConfig:
        """获取多邮箱发送配置"""
        # 解析抄送邮箱
        cc_emails = []
        cc_text = self.cc_emails_edit.text().strip()
        if cc_text:
            cc_emails = [email.strip() for email in cc_text.split(';') if email.strip()]

        # 轮换策略映射
        rotation_map = {
            "顺序轮换": RotationStrategy.SEQUENTIAL,
            "随机轮换": RotationStrategy.RANDOM,
            "负载均衡": RotationStrategy.LOAD_BALANCE,
            "按成功率轮换": RotationStrategy.SUCCESS_RATE
        }

        # 间隔策略映射
        interval_map = {
            "固定间隔": IntervalStrategy.FIXED,
            "随机间隔": IntervalStrategy.RANDOM,
            "递增间隔": IntervalStrategy.INCREMENTAL,
            "智能间隔": IntervalStrategy.SMART
        }

        return MultiSenderConfig(
            emails_per_sender=self.emails_per_sender_spin.value(),
            rotation_strategy=rotation_map.get(self.rotation_strategy_combo.currentText(), RotationStrategy.SEQUENTIAL),
            interval_strategy=interval_map.get(self.interval_strategy_combo.currentText(), IntervalStrategy.FIXED),
            base_interval=self.send_interval_spin.value(),
            max_interval=self.send_interval_spin.value() * 3,
            cc_emails=cc_emails,
            enable_load_balance=True,
            max_concurrent_per_sender=3
        )
    
    def load_accounts(self):
        """加载账号"""
        try:
            # 初始化Cookie管理器
            cookie_config = {
                'cookie_dir': 'data/cookies',
                'encryption_enabled': True,
                'max_age_days': 30
            }
            from src.core.cookie_manager import CookieManager
            cookie_manager = CookieManager(cookie_config)

            # 获取所有有Cookie的账号
            cookie_files = cookie_manager.list_cookie_files()

            if not cookie_files:
                self.log_message.emit("未找到任何Cookie文件，请先进行隐藏式登录", "warning")
                return

            # 从Cookie文件创建账号列表
            self.accounts = []
            for email in cookie_files:
                # 检查Cookie是否有效
                cookie_data = cookie_manager.get_cookies(email)
                if cookie_data and cookie_data.get('cookies') and len(cookie_data['cookies']) > 0:
                    account = Account(email=email, password="", status="active")
                    self.accounts.append(account)

            if not self.accounts:
                self.log_message.emit("未找到有效的Cookie账号", "warning")
                return

            self.log_message.emit(f"成功加载 {len(self.accounts)} 个有Cookie的账号", "info")

            # 更新账号表格
            self.update_account_table()

            # 检查Cookie状态
            self.refresh_cookie_status()

        except Exception as e:
            self.log_message.emit(f"加载账号失败: {e}", "error")
            logger.error(f"加载账号失败: {e}")

            # 使用示例数据作为备用
            self.accounts = [
                Account(email="<EMAIL>", password="", status="active"),
            ]
            self.update_account_table()
            self.log_message.emit("使用示例账号，请检查Cookie文件", "warning")
    
    def update_account_table(self):
        """更新账号表格"""
        self.account_table.setRowCount(len(self.accounts))

        for i, account in enumerate(self.accounts):
            # 邮箱地址
            email_item = QTableWidgetItem(account.email)
            email_item.setToolTip(account.email)
            self.account_table.setItem(i, 0, email_item)

            # 状态（带颜色）
            status_item = QTableWidgetItem(account.status)
            if account.status == "active":
                status_item.setBackground(QColor("#d5f4e6"))  # 绿色背景
                status_item.setText("✅ 可用")
            else:
                status_item.setBackground(QColor("#fadbd8"))  # 红色背景
                status_item.setText("❌ 不可用")
            self.account_table.setItem(i, 1, status_item)

            # 发送数量
            send_count = getattr(account, 'send_count', 0)
            count_item = QTableWidgetItem(str(send_count))
            count_item.setTextAlignment(Qt.AlignCenter)
            self.account_table.setItem(i, 2, count_item)

        # 更新账号统计
        self.update_account_stats()
    
    def refresh_cookie_status(self):
        """刷新Cookie状态"""
        self.log_message.emit("刷新Cookie状态...", "info")

        try:
            # 初始化Cookie管理器
            cookie_config = {
                'cookie_dir': 'data/cookies',
                'encryption_enabled': True,
                'max_age_days': 30
            }
            from src.core.cookie_manager import CookieManager
            cookie_manager = CookieManager(cookie_config)

            # 检查每个账号的Cookie状态
            for i, account in enumerate(self.accounts):
                try:
                    cookie_data = cookie_manager.get_cookies(account.email)
                    if cookie_data and cookie_data.get('cookies') and len(cookie_data['cookies']) > 0:
                        cookies = cookie_data['cookies']
                        cookie_status = f"有效 ({len(cookies)}个)"
                        self.account_table.setItem(i, 2, QTableWidgetItem(cookie_status))
                        # 设置绿色背景表示有效
                        self.account_table.item(i, 2).setBackground(QColor(144, 238, 144))
                    else:
                        self.account_table.setItem(i, 2, QTableWidgetItem("无Cookie"))
                        # 设置红色背景表示无效
                        self.account_table.item(i, 2).setBackground(QColor(255, 182, 193))

                except Exception as e:
                    self.account_table.setItem(i, 2, QTableWidgetItem("检查失败"))
                    self.account_table.item(i, 2).setBackground(QColor(255, 255, 0))
                    logger.error(f"检查账号 {account.email} Cookie失败: {e}")

            self.log_message.emit("Cookie状态刷新完成", "info")

        except Exception as e:
            self.log_message.emit(f"刷新Cookie状态失败: {e}", "error")
            logger.error(f"刷新Cookie状态失败: {e}")
    
    def add_email_task(self):
        """添加邮件任务"""
        if not self.scheduler:
            QMessageBox.warning(self, "警告", "请先初始化发送器")
            return

        to_emails = self.to_email_edit.text().strip()
        subject = self.subject_edit.text().strip()
        content = self.content_edit.toPlainText().strip()
        content_type = self.content_type_combo.currentText()

        if not all([to_emails, subject, content]):
            QMessageBox.warning(self, "警告", "请填写完整的邮件信息")
            return

        # 处理多个收件人
        email_list = [email.strip() for email in to_emails.split(';') if email.strip()]

        for to_email in email_list:
            # 应用变量替换
            final_subject = subject
            final_content = content

            # 基本变量替换
            basic_vars = {
                'email': to_email,
                'name': to_email.split('@')[0],  # 默认用邮箱前缀作为姓名
            }

            # 合并用户设置的变量
            all_vars = {**basic_vars, **self.current_variables}

            # 执行变量替换
            for var_name, var_value in all_vars.items():
                placeholder = f"{{{var_name}}}"
                final_subject = final_subject.replace(placeholder, str(var_value))
                final_content = final_content.replace(placeholder, str(var_value))

            task_id = self.scheduler.add_email_task(to_email, final_subject, final_content, content_type)
            self.log_message.emit(f"添加任务: {task_id} -> {to_email}", "info")

        # 清空输入
        self.to_email_edit.clear()
        if self.template_combo.currentIndex() == 0:  # 手动输入模式才清空
            self.subject_edit.clear()
            self.content_edit.clear()

        self.update_task_table()
    
    def import_email_tasks(self):
        """批量导入邮件任务"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择收件人文件", "",
            "CSV Files (*.csv);;Excel Files (*.xlsx);;Text Files (*.txt)"
        )
        if file_path:
            try:
                self.import_recipients_from_file(file_path)
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导入文件失败: {e}")
                logger.error(f"导入文件失败: {e}")

    def import_recipients_from_file(self, file_path: str):
        """从文件导入收件人"""
        import pandas as pd
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QPushButton, QCheckBox, QSpinBox

        try:
            # 读取文件
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8')
            elif file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
            elif file_path.endswith('.txt'):
                # 尝试按行读取邮箱
                with open(file_path, 'r', encoding='utf-8') as f:
                    emails = [line.strip() for line in f if line.strip() and '@' in line]
                df = pd.DataFrame({'email': emails})
            else:
                raise ValueError("不支持的文件格式")

            if df.empty:
                QMessageBox.warning(self, "警告", "文件中没有找到有效数据")
                return

            # 显示导入配置对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("配置导入设置")
            dialog.setModal(True)
            dialog.resize(500, 400)

            layout = QVBoxLayout()

            # 文件信息
            info_label = QLabel(f"文件: {file_path}\n行数: {len(df)}\n列数: {len(df.columns)}")
            layout.addWidget(info_label)

            # 列映射
            mapping_layout = QVBoxLayout()
            mapping_layout.addWidget(QLabel("列映射设置:"))

            # 邮箱列
            email_layout = QHBoxLayout()
            email_layout.addWidget(QLabel("邮箱列:"))
            email_combo = QComboBox()
            email_combo.addItems(df.columns.tolist())
            # 自动选择包含email的列
            for i, col in enumerate(df.columns):
                if 'email' in col.lower() or 'mail' in col.lower():
                    email_combo.setCurrentIndex(i)
                    break
            email_layout.addWidget(email_combo)
            mapping_layout.addLayout(email_layout)

            # 姓名列（可选）
            name_layout = QHBoxLayout()
            name_layout.addWidget(QLabel("姓名列（可选）:"))
            name_combo = QComboBox()
            name_combo.addItem("不使用", None)
            name_combo.addItems(df.columns.tolist())
            # 自动选择包含name的列
            for i, col in enumerate(df.columns):
                if 'name' in col.lower() or '姓名' in col.lower():
                    name_combo.setCurrentIndex(i + 1)
                    break
            name_layout.addWidget(name_combo)
            mapping_layout.addLayout(name_layout)

            # 公司列（可选）
            company_layout = QHBoxLayout()
            company_layout.addWidget(QLabel("公司列（可选）:"))
            company_combo = QComboBox()
            company_combo.addItem("不使用", None)
            company_combo.addItems(df.columns.tolist())
            # 自动选择包含company的列
            for i, col in enumerate(df.columns):
                if 'company' in col.lower() or '公司' in col.lower():
                    company_combo.setCurrentIndex(i + 1)
                    break
            company_layout.addWidget(company_combo)
            mapping_layout.addLayout(company_layout)

            layout.addLayout(mapping_layout)

            # 导入设置
            settings_layout = QVBoxLayout()
            settings_layout.addWidget(QLabel("导入设置:"))

            # 批量大小
            batch_layout = QHBoxLayout()
            batch_layout.addWidget(QLabel("每批处理数量:"))
            batch_spin = QSpinBox()
            batch_spin.setRange(1, 1000)
            batch_spin.setValue(50)
            batch_layout.addWidget(batch_spin)
            settings_layout.addLayout(batch_layout)

            # 去重选项
            dedupe_check = QCheckBox("去除重复邮箱")
            dedupe_check.setChecked(True)
            settings_layout.addWidget(dedupe_check)

            # 验证邮箱格式
            validate_check = QCheckBox("验证邮箱格式")
            validate_check.setChecked(True)
            settings_layout.addWidget(validate_check)

            layout.addLayout(settings_layout)

            # 按钮
            button_layout = QHBoxLayout()
            import_btn = QPushButton("开始导入")
            cancel_btn = QPushButton("取消")

            def do_import():
                try:
                    # 获取设置
                    email_col = email_combo.currentText()
                    name_col = name_combo.currentData()
                    company_col = company_combo.currentData()
                    batch_size = batch_spin.value()
                    dedupe = dedupe_check.isChecked()
                    validate = validate_check.isChecked()

                    # 处理数据
                    emails_data = []
                    for _, row in df.iterrows():
                        email = str(row[email_col]).strip()

                        # 验证邮箱格式
                        if validate and not self.is_valid_email(email):
                            continue

                        # 构建数据
                        data = {'email': email}
                        if name_col:
                            data['name'] = str(row[name_col]).strip()
                        if company_col:
                            data['company'] = str(row[company_col]).strip()

                        emails_data.append(data)

                    # 去重
                    if dedupe:
                        seen = set()
                        unique_data = []
                        for data in emails_data:
                            if data['email'] not in seen:
                                seen.add(data['email'])
                                unique_data.append(data)
                        emails_data = unique_data

                    if not emails_data:
                        QMessageBox.warning(dialog, "警告", "没有找到有效的邮箱数据")
                        return

                    # 批量添加任务
                    self.add_batch_tasks(emails_data, batch_size)

                    dialog.accept()

                    QMessageBox.information(
                        self, "成功",
                        f"导入完成\n有效邮箱: {len(emails_data)}"
                    )

                except Exception as e:
                    QMessageBox.critical(dialog, "错误", f"导入失败: {e}")
                    logger.error(f"导入失败: {e}")

            import_btn.clicked.connect(do_import)
            cancel_btn.clicked.connect(dialog.reject)

            button_layout.addWidget(import_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)

            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            raise Exception(f"读取文件失败: {e}")

    def is_valid_email(self, email: str) -> bool:
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def add_batch_tasks(self, emails_data: List[Dict[str, str]], batch_size: int):
        """批量添加任务"""
        if not self.scheduler:
            QMessageBox.warning(self, "警告", "请先初始化发送器")
            return

        subject = self.subject_edit.text().strip()
        content = self.content_edit.toPlainText().strip()
        content_type = self.content_type_combo.currentText()

        if not all([subject, content]):
            QMessageBox.warning(self, "警告", "请填写邮件主题和内容")
            return

        # 分批处理
        total_added = 0
        for i in range(0, len(emails_data), batch_size):
            batch = emails_data[i:i + batch_size]

            for email_data in batch:
                email = email_data['email']

                # 应用变量替换
                final_subject = subject
                final_content = content

                # 基本变量
                variables = {
                    'email': email,
                    'name': email_data.get('name', email.split('@')[0]),
                    'company': email_data.get('company', ''),
                    **self.current_variables
                }

                # 执行变量替换
                for var_name, var_value in variables.items():
                    if var_value:  # 只替换非空值
                        placeholder = f"{{{var_name}}}"
                        final_subject = final_subject.replace(placeholder, str(var_value))
                        final_content = final_content.replace(placeholder, str(var_value))

                # 添加任务
                task_id = self.scheduler.add_email_task(email, final_subject, final_content, content_type)
                total_added += 1

                if total_added % 10 == 0:  # 每10个任务记录一次日志
                    self.log_message.emit(f"已添加 {total_added} 个任务", "info")

        self.log_message.emit(f"批量导入完成，共添加 {total_added} 个任务", "info")
        self.update_task_table()

    def download_import_template(self):
        """下载导入模板"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存导入模板", "收件人导入模板.csv", "CSV Files (*.csv)"
            )

            if file_path:
                # 创建模板内容
                template_data = [
                    ['email', 'name', 'company', 'phone', 'title'],
                    ['<EMAIL>', '张三', 'ABC公司', '13800138000', '经理'],
                    ['<EMAIL>', '李四', 'XYZ公司', '13900139000', '总监'],
                    ['<EMAIL>', '王五', 'DEF公司', '13700137000', '主管']
                ]

                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerows(template_data)

                QMessageBox.information(
                    self, "成功",
                    f"导入模板已保存到: {file_path}\n\n"
                    f"模板说明:\n"
                    f"• email: 收件人邮箱（必填）\n"
                    f"• name: 收件人姓名（可选，用于变量替换）\n"
                    f"• company: 公司名称（可选，用于变量替换）\n"
                    f"• phone: 电话号码（可选，用于变量替换）\n"
                    f"• title: 职位（可选，用于变量替换）\n\n"
                    f"您可以根据需要添加更多列，导入时可以选择映射关系。"
                )

                self.log_message.emit(f"导入模板已保存: {file_path}", "info")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存模板失败: {e}")
            logger.error(f"保存模板失败: {e}")

    def update_sender_stats_table(self):
        """更新发送账号统计表格"""
        if not self.multi_sender_manager:
            return

        try:
            stats = self.multi_sender_manager.get_sender_statistics()

            self.sender_stats_table.setRowCount(len(stats))

            for row, (email, stat) in enumerate(stats.items()):
                # 发送邮箱
                self.sender_stats_table.setItem(row, 0, QTableWidgetItem(email))

                # 已发送
                self.sender_stats_table.setItem(row, 1, QTableWidgetItem(str(stat['total_sent'])))

                # 成功数
                self.sender_stats_table.setItem(row, 2, QTableWidgetItem(str(stat['success_count'])))

                # 成功率
                success_rate_item = QTableWidgetItem(f"{stat['success_rate']:.1f}%")
                if stat['success_rate'] >= 90:
                    success_rate_item.setBackground(QColor(144, 238, 144))  # 浅绿色
                elif stat['success_rate'] >= 70:
                    success_rate_item.setBackground(QColor(255, 255, 144))  # 浅黄色
                elif stat['total_sent'] > 0:
                    success_rate_item.setBackground(QColor(255, 182, 193))  # 浅红色
                self.sender_stats_table.setItem(row, 3, success_rate_item)

                # 当前负载
                load_item = QTableWidgetItem(str(stat['current_load']))
                if stat['current_load'] > 2:
                    load_item.setBackground(QColor(255, 255, 144))  # 浅黄色
                self.sender_stats_table.setItem(row, 4, load_item)

                # 平均响应时间
                response_time = f"{stat['avg_response_time']:.3f}s" if stat['avg_response_time'] > 0 else "N/A"
                self.sender_stats_table.setItem(row, 5, QTableWidgetItem(response_time))

        except Exception as e:
            logger.error(f"更新发送账号统计失败: {e}")

    def on_send_mode_changed(self, mode_text: str):
        """发送模式改变时的处理"""
        # 根据模式启用/禁用相关控件
        if mode_text == "批量逐渐发送":
            self.batch_size_spin.setEnabled(True)
            self.concurrent_count_spin.setEnabled(False)
        elif mode_text == "并发发送":
            self.batch_size_spin.setEnabled(False)
            self.concurrent_count_spin.setEnabled(True)
        elif mode_text == "智能发送":
            self.batch_size_spin.setEnabled(True)
            self.concurrent_count_spin.setEnabled(True)
        else:  # 单个逐渐发送
            self.batch_size_spin.setEnabled(False)
            self.concurrent_count_spin.setEnabled(False)

        self.log_message.emit(f"发送模式已切换为: {mode_text}", "info")

    def get_send_mode_config(self) -> SendModeConfig:
        """获取发送模式配置"""
        # 发送模式映射
        mode_map = {
            "单个逐渐发送": SendMode.SEQUENTIAL,
            "批量逐渐发送": SendMode.BATCH,
            "并发发送": SendMode.CONCURRENT,
            "智能发送": SendMode.SMART
        }

        return SendModeConfig(
            mode=mode_map.get(self.send_mode_combo.currentText(), SendMode.SEQUENTIAL),
            batch_size=self.batch_size_spin.value(),
            concurrent_count=self.concurrent_count_spin.value(),
            send_interval=self.send_interval_spin.value(),
            priority_enabled=True,
            smart_throttling=True,
            max_retries=3,
            retry_interval=5.0
        )

    def get_email_priority(self) -> Priority:
        """获取邮件优先级"""
        priority_map = {
            "普通": Priority.NORMAL,
            "高": Priority.HIGH,
            "最高": Priority.HIGHEST,
            "低": Priority.LOW
        }
        return priority_map.get(self.priority_combo.currentText(), Priority.NORMAL)

    def on_data_source_changed(self, source_text: str):
        """数据源改变时的处理"""
        if source_text == "手动输入":
            self.to_email_edit.setEnabled(True)
            self.to_email_edit.setPlaceholderText("多个邮箱用分号分隔")
        elif source_text == "导入数据":
            self.to_email_edit.setEnabled(False)
            self.to_email_edit.setPlaceholderText("请在数据源管理中选择收件人")
        elif source_text == "文件监控数据":
            self.to_email_edit.setEnabled(False)
            self.to_email_edit.setPlaceholderText("将从文件监控获取收件人")
        elif source_text == "数据库查询":
            self.to_email_edit.setEnabled(False)
            self.to_email_edit.setPlaceholderText("将从数据库查询获取收件人")

        self.log_message.emit(f"数据源已切换为: {source_text}", "info")

    def manage_data_sources(self):
        """管理数据源"""
        # 切换到数据源管理选项卡
        self.task_tab_widget.setCurrentIndex(3)  # 数据源管理是第4个选项卡

    def on_recipients_selected(self, recipients):
        """从数据源管理界面选择收件人时的处理"""
        # 切换到发送选项卡
        self.task_tab_widget.setCurrentIndex(0)

        # 设置数据源为导入数据
        self.data_source_combo.setCurrentText("导入数据")

        # 将收件人邮箱填入输入框
        emails = [recipient.email for recipient in recipients]
        self.to_email_edit.setText(";".join(emails))

        # 如果有收件人信息，更新当前变量
        if recipients and hasattr(recipients[0], 'variables') and recipients[0].variables:
            # 使用第一个收件人的变量作为示例
            self.current_variables.update(recipients[0].variables)

        self.log_message.emit(f"已选择 {len(recipients)} 个收件人", "info")

    def on_template_selected(self, template):
        """从导入模板界面选择模板时的处理"""
        # 切换到发送选项卡
        self.task_tab_widget.setCurrentIndex(0)

        # 显示模板信息
        self.log_message.emit(f"已选择导入模板: {template.name}", "info")
        self.log_message.emit(f"模板格式: {template.file_format.upper()}", "info")
        self.log_message.emit(f"包含字段: {', '.join([col['display_name'] for col in template.columns])}", "info")
    
    def update_task_table(self):
        """更新任务表格"""
        if not self.scheduler:
            return

        try:
            # 获取任务列表（这里需要根据实际的scheduler实现来调整）
            tasks = getattr(self.scheduler, 'tasks', [])

            self.task_table.setRowCount(len(tasks))

            for i, task in enumerate(tasks):
                # 收件人
                recipient_item = QTableWidgetItem(task.get('to_email', ''))
                recipient_item.setToolTip(task.get('to_email', ''))
                self.task_table.setItem(i, 0, recipient_item)

                # 主题
                subject_item = QTableWidgetItem(task.get('subject', ''))
                subject_item.setToolTip(task.get('subject', ''))
                self.task_table.setItem(i, 1, subject_item)

                # 内容类型
                content_type_item = QTableWidgetItem(task.get('content_type', 'text/plain'))
                content_type_item.setTextAlignment(Qt.AlignCenter)
                self.task_table.setItem(i, 2, content_type_item)

                # 状态（带颜色）
                status = task.get('status', '等待')
                status_item = QTableWidgetItem(status)
                status_item.setTextAlignment(Qt.AlignCenter)

                if status == "等待":
                    status_item.setBackground(QColor("#fff3cd"))  # 黄色背景
                    status_item.setText("⏳ 等待")
                elif status == "执行中":
                    status_item.setBackground(QColor("#cce5ff"))  # 蓝色背景
                    status_item.setText("🚀 执行中")
                elif status == "已完成":
                    status_item.setBackground(QColor("#d5f4e6"))  # 绿色背景
                    status_item.setText("✅ 已完成")
                elif status == "失败":
                    status_item.setBackground(QColor("#fadbd8"))  # 红色背景
                    status_item.setText("❌ 失败")

                self.task_table.setItem(i, 3, status_item)

                # 时间
                create_time = task.get('create_time', '')
                if create_time:
                    time_item = QTableWidgetItem(create_time.strftime("%H:%M:%S") if hasattr(create_time, 'strftime') else str(create_time))
                else:
                    time_item = QTableWidgetItem(time.strftime("%H:%M:%S"))
                time_item.setTextAlignment(Qt.AlignCenter)
                self.task_table.setItem(i, 4, time_item)

            # 更新任务统计
            self.update_task_stats()

        except Exception as e:
            logger.error(f"更新任务表格失败: {e}")
            self.log_message.emit(f"更新任务表格失败: {e}", "error")
    
    def start_sending(self):
        """开始发送"""
        if not self.accounts:
            QMessageBox.warning(self, "警告", "请先加载账号")
            return
        
        try:
            # 创建调度器
            config = self.get_sending_config()
            self.scheduler = EmailSendingScheduler(config)

            # 创建多邮箱管理器
            multi_config = self.get_multi_sender_config()
            self.multi_sender_manager = MultiSenderManager(multi_config)
            self.multi_sender_manager.set_accounts(self.accounts)

            # 初始化
            if not self.scheduler.initialize(self.accounts):
                QMessageBox.critical(self, "错误", "调度器初始化失败")
                return
            
            # 启动发送
            num_workers = self.worker_threads_spin.value()
            self.scheduler.start_sending(num_workers)
            
            # 更新界面状态
            self.is_sending = True
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.pause_btn.setEnabled(True)
            
            # 启动状态更新定时器
            self.stats_timer.start(1000)  # 每秒更新一次
            
            self.log_message.emit("邮件发送已启动", "info")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动发送失败: {e}")
            logger.error(f"启动发送失败: {e}")
    
    def stop_sending(self):
        """停止发送"""
        if self.scheduler:
            self.scheduler.stop_sending()
            self.scheduler.cleanup()
            self.scheduler = None
        
        # 更新界面状态
        self.is_sending = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.pause_btn.setEnabled(False)
        
        # 停止状态更新定时器
        self.stats_timer.stop()
        
        self.log_message.emit("邮件发送已停止", "info")
    
    def pause_sending(self):
        """暂停发送"""
        # TODO: 实现暂停功能
        self.log_message.emit("暂停功能待实现", "warning")
    
    def update_stats_display(self):
        """更新状态显示"""
        if not self.scheduler:
            return
        
        try:
            stats = self.scheduler.get_sending_stats()
            self.stats_updated.emit(stats)
        except Exception as e:
            logger.error(f"更新状态失败: {e}")
    
    def on_stats_updated(self, stats: Dict[str, Any]):
        """处理状态更新"""
        # 更新统计标签
        self.total_tasks_label.setText(f"总任务: {stats['total_tasks']}")
        self.sent_success_label.setText(f"发送成功: {stats['sent_success']}")
        self.sent_failed_label.setText(f"发送失败: {stats['sent_failed']}")
        self.pending_tasks_label.setText(f"待发送: {stats['pending_tasks']}")
        self.send_rate_label.setText(f"发送速率: {stats['emails_per_minute']} 封/分钟")
        self.running_time_label.setText(f"运行时间: {stats['running_time']} 秒")
        
        # 更新浏览器状态
        browser_stats = stats.get('browser_stats', {})
        self.active_browsers_label.setText(
            f"活跃浏览器: {browser_stats.get('active_browsers', 0)}/{browser_stats.get('total_browsers', 0)}"
        )
        
        # 更新浏览器状态表格
        browsers_detail = browser_stats.get('browsers_detail', {})
        self.browser_status_table.setRowCount(len(browsers_detail))
        
        for i, (browser_id, browser_info) in enumerate(browsers_detail.items()):
            self.browser_status_table.setItem(i, 0, QTableWidgetItem(browser_id))
            self.browser_status_table.setItem(i, 1, QTableWidgetItem(browser_info['status']))
            self.browser_status_table.setItem(i, 2, QTableWidgetItem(browser_info['current_account'] or "无"))
            self.browser_status_table.setItem(i, 3, QTableWidgetItem(str(browser_info['sent_count'])))

        # 更新发送账号统计
        self.update_sender_stats_table()
    
    def on_log_message(self, message: str, level: str):
        """处理日志消息"""
        timestamp = time.strftime("%H:%M:%S")

        # 根据级别设置颜色和图标
        level_config = {
            'info': {'color': '#3498db', 'icon': 'ℹ️'},
            'warning': {'color': '#f39c12', 'icon': '⚠️'},
            'error': {'color': '#e74c3c', 'icon': '❌'},
            'success': {'color': '#27ae60', 'icon': '✅'},
            'debug': {'color': '#95a5a6', 'icon': '🔧'}
        }

        config = level_config.get(level, {'color': '#ecf0f1', 'icon': '📝'})

        # 格式化消息
        formatted_message = f'<span style="color: {config["color"]}; font-weight: bold;">[{timestamp}] {config["icon"]} {message}</span>'
        self.log_text.append(formatted_message)

        # 更新日志计数
        current_count = self.log_text.document().blockCount()
        self.log_count_label.setText(f"日志条数: {current_count}")

        # 自动滚动到底部（如果启用）
        if hasattr(self, 'auto_scroll_check') and self.auto_scroll_check.isChecked():
            scrollbar = self.log_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.log_count_label.setText("日志条数: 0")

    def load_templates(self):
        """加载邮件模板到下拉框"""
        try:
            from src.models.email_template import EmailTemplateManager
            template_manager = EmailTemplateManager(self.db_manager)
            templates = template_manager.get_all_templates()

            # 清空现有项目（保留"手动输入"）
            self.template_combo.clear()
            self.template_combo.addItem("手动输入", None)

            # 添加模板
            for template in templates:
                display_name = f"{template.template_name}"
                if template.is_default:
                    display_name += " (默认)"
                self.template_combo.addItem(display_name, template)

            self.log_message.emit(f"加载了 {len(templates)} 个邮件模板", "info")

        except Exception as e:
            self.log_message.emit(f"加载模板失败: {e}", "error")
            logger.error(f"加载模板失败: {e}")

    def on_template_selected(self, index: int):
        """模板选择改变时的处理"""
        template = self.template_combo.itemData(index)
        if template is None:
            # 手动输入模式
            self.subject_edit.setEnabled(True)
            self.content_edit.setEnabled(True)
            self.content_type_combo.setEnabled(True)
            return

        # 使用模板
        self.subject_edit.setText(template.subject)
        self.content_edit.setPlainText(template.content)

        # 设置内容类型
        type_index = self.content_type_combo.findText(template.content_type)
        if type_index >= 0:
            self.content_type_combo.setCurrentIndex(type_index)

        # 加载变量
        if template.variables:
            try:
                import json
                self.current_variables = json.loads(template.variables)
            except:
                self.current_variables = {}
        else:
            self.current_variables = {}

        self.log_message.emit(f"已加载模板: {template.template_name}", "info")

    def on_template_widget_selected(self, template):
        """从模板管理界面选择模板时的处理"""
        # 切换到发送选项卡
        self.task_tab_widget.setCurrentIndex(0)

        # 在下拉框中选择对应模板
        for i in range(self.template_combo.count()):
            item_template = self.template_combo.itemData(i)
            if item_template and item_template.id == template.id:
                self.template_combo.setCurrentIndex(i)
                break

    def set_variables(self):
        """设置变量对话框"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QScrollArea, QWidget

        dialog = QDialog(self)
        dialog.setWindowTitle("设置邮件变量")
        dialog.setModal(True)
        dialog.resize(500, 400)

        layout = QVBoxLayout()

        # 说明
        help_label = QLabel("为每个收件人设置个性化变量值。变量格式: {变量名}")
        help_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        layout.addWidget(help_label)

        # 滚动区域
        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()

        # 获取当前模板的变量
        template = self.template_combo.currentData()
        if template and template.variables:
            try:
                import json
                variables = json.loads(template.variables)

                self.variable_edits = {}
                for var_name, var_desc in variables.items():
                    var_layout = QHBoxLayout()
                    var_layout.addWidget(QLabel(f"{{{var_name}}} ({var_desc}):"))

                    var_edit = QLineEdit()
                    var_edit.setPlaceholderText(f"请输入{var_desc}")
                    var_edit.setText(self.current_variables.get(var_name, ""))
                    self.variable_edits[var_name] = var_edit
                    var_layout.addWidget(var_edit)

                    scroll_layout.addLayout(var_layout)

            except Exception as e:
                scroll_layout.addWidget(QLabel(f"解析变量失败: {e}"))
        else:
            scroll_layout.addWidget(QLabel("当前模板没有定义变量"))

        scroll_widget.setLayout(scroll_layout)
        scroll.setWidget(scroll_widget)
        layout.addWidget(scroll)

        # 按钮
        button_layout = QHBoxLayout()
        ok_btn = QPushButton("确定")
        cancel_btn = QPushButton("取消")

        def save_variables():
            if hasattr(self, 'variable_edits'):
                for var_name, var_edit in self.variable_edits.items():
                    self.current_variables[var_name] = var_edit.text().strip()
            dialog.accept()

        ok_btn.clicked.connect(save_variables)
        cancel_btn.clicked.connect(dialog.reject)

        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        dialog.setLayout(layout)
        dialog.exec_()

    def clear_tasks(self):
        """清空任务列表"""
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空所有任务吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.task_table.setRowCount(0)
            self.log_message.emit("任务列表已清空", "info")
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.is_sending:
            reply = QMessageBox.question(
                self, "确认关闭", 
                "发送正在进行中，确定要关闭吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.stop_sending()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    widget = MultiBrowserSenderWidget()
    widget.show()
    sys.exit(app.exec_())
