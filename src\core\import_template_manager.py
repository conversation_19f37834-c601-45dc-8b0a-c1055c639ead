#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入模板管理器
管理各种导入格式模板，提供标准化的数据导入功能
"""

import json
import csv
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from src.utils.logger import get_logger

logger = get_logger("ImportTemplateManager")


@dataclass
class ImportTemplate:
    """导入模板"""
    name: str
    description: str
    file_format: str  # csv, xlsx, json, txt
    columns: List[Dict[str, str]]  # 列定义
    sample_data: List[Dict[str, str]]  # 示例数据
    encoding: str = "utf-8"
    delimiter: str = ","  # CSV分隔符
    has_header: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'description': self.description,
            'file_format': self.file_format,
            'columns': self.columns,
            'sample_data': self.sample_data,
            'encoding': self.encoding,
            'delimiter': self.delimiter,
            'has_header': self.has_header
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ImportTemplate':
        """从字典创建对象"""
        return cls(
            name=data['name'],
            description=data['description'],
            file_format=data['file_format'],
            columns=data['columns'],
            sample_data=data['sample_data'],
            encoding=data.get('encoding', 'utf-8'),
            delimiter=data.get('delimiter', ','),
            has_header=data.get('has_header', True)
        )


class ImportTemplateManager:
    """导入模板管理器"""
    
    def __init__(self):
        """初始化导入模板管理器"""
        self.templates = self._get_default_templates()
    
    def _get_default_templates(self) -> List[ImportTemplate]:
        """获取默认模板"""
        templates = []
        
        # 基础收件人模板
        basic_template = ImportTemplate(
            name="基础收件人模板",
            description="包含邮箱、姓名、公司等基础信息的收件人模板",
            file_format="csv",
            columns=[
                {"name": "email", "display_name": "邮箱地址", "required": True, "type": "email"},
                {"name": "name", "display_name": "姓名", "required": False, "type": "text"},
                {"name": "company", "display_name": "公司名称", "required": False, "type": "text"},
                {"name": "phone", "display_name": "电话号码", "required": False, "type": "phone"},
                {"name": "title", "display_name": "职位", "required": False, "type": "text"}
            ],
            sample_data=[
                {
                    "email": "<EMAIL>",
                    "name": "张三",
                    "company": "ABC科技有限公司",
                    "phone": "13800138000",
                    "title": "产品经理"
                },
                {
                    "email": "<EMAIL>",
                    "name": "李四",
                    "company": "XYZ网络科技",
                    "phone": "13900139000",
                    "title": "技术总监"
                },
                {
                    "email": "<EMAIL>",
                    "name": "王五",
                    "company": "DEF咨询公司",
                    "phone": "13700137000",
                    "title": "业务主管"
                }
            ]
        )
        templates.append(basic_template)
        
        # 营销收件人模板
        marketing_template = ImportTemplate(
            name="营销收件人模板",
            description="包含营销相关信息的收件人模板，适用于营销邮件发送",
            file_format="csv",
            columns=[
                {"name": "email", "display_name": "邮箱地址", "required": True, "type": "email"},
                {"name": "name", "display_name": "姓名", "required": False, "type": "text"},
                {"name": "company", "display_name": "公司名称", "required": False, "type": "text"},
                {"name": "industry", "display_name": "行业", "required": False, "type": "text"},
                {"name": "position", "display_name": "职位", "required": False, "type": "text"},
                {"name": "location", "display_name": "地区", "required": False, "type": "text"},
                {"name": "interest", "display_name": "兴趣标签", "required": False, "type": "text"},
                {"name": "source", "display_name": "来源渠道", "required": False, "type": "text"}
            ],
            sample_data=[
                {
                    "email": "<EMAIL>",
                    "name": "陈总",
                    "company": "科技创新公司",
                    "industry": "软件开发",
                    "position": "CEO",
                    "location": "北京",
                    "interest": "人工智能,云计算",
                    "source": "展会"
                },
                {
                    "email": "<EMAIL>",
                    "name": "刘经理",
                    "company": "零售连锁企业",
                    "industry": "零售",
                    "position": "运营经理",
                    "location": "上海",
                    "interest": "数字化转型,供应链",
                    "source": "网站注册"
                }
            ]
        )
        templates.append(marketing_template)
        
        # 客户信息模板
        customer_template = ImportTemplate(
            name="客户信息模板",
            description="详细的客户信息模板，包含联系方式和业务信息",
            file_format="csv",
            columns=[
                {"name": "email", "display_name": "邮箱地址", "required": True, "type": "email"},
                {"name": "name", "display_name": "客户姓名", "required": True, "type": "text"},
                {"name": "company", "display_name": "公司名称", "required": False, "type": "text"},
                {"name": "phone", "display_name": "电话号码", "required": False, "type": "phone"},
                {"name": "mobile", "display_name": "手机号码", "required": False, "type": "phone"},
                {"name": "address", "display_name": "地址", "required": False, "type": "text"},
                {"name": "website", "display_name": "网站", "required": False, "type": "url"},
                {"name": "notes", "display_name": "备注", "required": False, "type": "text"}
            ],
            sample_data=[
                {
                    "email": "<EMAIL>",
                    "name": "张总监",
                    "company": "大型企业集团",
                    "phone": "010-12345678",
                    "mobile": "13812345678",
                    "address": "北京市朝阳区商务中心",
                    "website": "www.bigcorp.com",
                    "notes": "重要客户，优先处理"
                }
            ]
        )
        templates.append(customer_template)
        
        # 简单邮箱列表模板
        simple_template = ImportTemplate(
            name="简单邮箱列表",
            description="只包含邮箱地址的简单模板",
            file_format="txt",
            columns=[
                {"name": "email", "display_name": "邮箱地址", "required": True, "type": "email"}
            ],
            sample_data=[
                {"email": "<EMAIL>"},
                {"email": "<EMAIL>"},
                {"email": "<EMAIL>"}
            ],
            has_header=False
        )
        templates.append(simple_template)
        
        return templates
    
    def get_all_templates(self) -> List[ImportTemplate]:
        """获取所有模板"""
        return self.templates.copy()
    
    def get_template_by_name(self, name: str) -> Optional[ImportTemplate]:
        """根据名称获取模板"""
        for template in self.templates:
            if template.name == name:
                return template
        return None
    
    def get_templates_by_format(self, file_format: str) -> List[ImportTemplate]:
        """根据文件格式获取模板"""
        return [t for t in self.templates if t.file_format == file_format]
    
    def create_template_file(self, template: ImportTemplate, file_path: str):
        """创建模板文件"""
        if template.file_format == "csv":
            self._create_csv_template(template, file_path)
        elif template.file_format == "xlsx":
            self._create_xlsx_template(template, file_path)
        elif template.file_format == "json":
            self._create_json_template(template, file_path)
        elif template.file_format == "txt":
            self._create_txt_template(template, file_path)
        else:
            raise ValueError(f"不支持的文件格式: {template.file_format}")
    
    def _create_csv_template(self, template: ImportTemplate, file_path: str):
        """创建CSV模板文件"""
        with open(file_path, 'w', newline='', encoding=template.encoding) as csvfile:
            fieldnames = [col['name'] for col in template.columns]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames, delimiter=template.delimiter)
            
            if template.has_header:
                # 写入中文表头
                header_row = {col['name']: col['display_name'] for col in template.columns}
                writer.writerow(header_row)
            
            # 写入示例数据
            for sample in template.sample_data:
                writer.writerow(sample)
    
    def _create_xlsx_template(self, template: ImportTemplate, file_path: str):
        """创建Excel模板文件"""
        try:
            import pandas as pd
            
            # 准备数据
            data = []
            if template.has_header:
                # 添加表头行
                header_row = {col['name']: col['display_name'] for col in template.columns}
                data.append(header_row)
            
            # 添加示例数据
            data.extend(template.sample_data)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 保存为Excel
            df.to_excel(file_path, index=False, header=False)
            
        except ImportError:
            raise ImportError("需要安装pandas和openpyxl库来支持Excel格式")
    
    def _create_json_template(self, template: ImportTemplate, file_path: str):
        """创建JSON模板文件"""
        template_data = {
            'template_info': {
                'name': template.name,
                'description': template.description,
                'columns': template.columns
            },
            'sample_data': template.sample_data
        }
        
        with open(file_path, 'w', encoding=template.encoding) as jsonfile:
            json.dump(template_data, jsonfile, ensure_ascii=False, indent=2)
    
    def _create_txt_template(self, template: ImportTemplate, file_path: str):
        """创建TXT模板文件"""
        with open(file_path, 'w', encoding=template.encoding) as txtfile:
            if template.has_header:
                # 写入说明
                txtfile.write(f"# {template.name}\n")
                txtfile.write(f"# {template.description}\n")
                txtfile.write("# 每行一个邮箱地址\n\n")
            
            # 写入示例数据
            for sample in template.sample_data:
                if 'email' in sample:
                    txtfile.write(sample['email'] + '\n')
    
    def validate_import_data(self, template: ImportTemplate, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证导入数据"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'valid_rows': [],
            'invalid_rows': []
        }
        
        required_columns = [col['name'] for col in template.columns if col.get('required', False)]
        
        for row_index, row in enumerate(data):
            row_errors = []
            
            # 检查必填字段
            for required_col in required_columns:
                if required_col not in row or not str(row[required_col]).strip():
                    row_errors.append(f"缺少必填字段: {required_col}")
            
            # 验证邮箱格式
            if 'email' in row and row['email']:
                email = str(row['email']).strip()
                if not self._is_valid_email(email):
                    row_errors.append(f"邮箱格式不正确: {email}")
            
            # 验证电话格式
            for col in template.columns:
                if col.get('type') == 'phone' and col['name'] in row and row[col['name']]:
                    phone = str(row[col['name']]).strip()
                    if not self._is_valid_phone(phone):
                        row_errors.append(f"电话格式不正确: {phone}")
            
            if row_errors:
                validation_result['invalid_rows'].append({
                    'row_index': row_index + 1,
                    'data': row,
                    'errors': row_errors
                })
                validation_result['errors'].extend([f"第{row_index + 1}行: {error}" for error in row_errors])
            else:
                validation_result['valid_rows'].append(row)
        
        if validation_result['errors']:
            validation_result['valid'] = False
        
        return validation_result
    
    def _is_valid_email(self, email: str) -> bool:
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def _is_valid_phone(self, phone: str) -> bool:
        """验证电话格式"""
        import re
        # 简单的电话号码验证
        pattern = r'^[\d\-\+\(\)\s]{7,20}$'
        return re.match(pattern, phone) is not None
    
    def get_template_info(self, template: ImportTemplate) -> str:
        """获取模板信息说明"""
        info = f"模板名称: {template.name}\n"
        info += f"描述: {template.description}\n"
        info += f"文件格式: {template.file_format.upper()}\n"
        info += f"编码: {template.encoding}\n"
        
        if template.file_format == "csv":
            info += f"分隔符: {template.delimiter}\n"
        
        info += f"包含表头: {'是' if template.has_header else '否'}\n\n"
        
        info += "字段说明:\n"
        for col in template.columns:
            required = " (必填)" if col.get('required', False) else " (可选)"
            info += f"- {col['display_name']} ({col['name']}){required}\n"
        
        return info
