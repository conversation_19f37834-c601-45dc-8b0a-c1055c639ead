# 🔐 新浪邮箱登录验证功能使用指南

## 📋 功能概述

新浪邮箱登录验证功能是专门为处理新浪邮箱登录时的人机验证而设计的。该功能支持单个账号验证和批量账号验证，能够自动打开浏览器进行登录，并在遇到验证码时等待用户手动完成验证。

## ✨ 核心特性

### 🎯 智能登录流程
- **自动打开浏览器**: 访问 https://mail.sina.com.cn
- **自动填写信息**: 自动输入邮箱地址和密码
- **智能验证检测**: 自动检测是否需要人机验证
- **人工验证支持**: 等待用户手动完成"点击验证"等操作
- **状态自动更新**: 登录成功后自动更新账号状态

### 🔧 多种操作方式
- **单个账号验证**: 右键菜单选择"登录验证"
- **批量选择验证**: 选中多个账号后右键选择"批量登录验证"
- **全部账号验证**: 工具栏点击"批量登录验证"按钮
- **实时进度显示**: 显示验证进度和当前状态

### 🛡️ 安全保障
- **密码加密存储**: 所有密码使用AES加密
- **代理IP支持**: 支持为每个账号配置独立代理
- **会话隔离**: 每个账号使用独立的浏览器会话
- **错误处理**: 完善的异常处理和重试机制

## 🎮 使用方法

### 1. 添加邮箱账号

#### 方法一：手动添加
1. 启动程序: `python main.py`
2. 点击 **"账号管理"** 选项卡
3. 点击 **"添加账号"** 按钮
4. 填写邮箱地址和密码
5. 可选：配置代理IP和端口
6. 点击 **"确定"** 保存

#### 方法二：批量导入
1. 准备CSV文件，格式如下：
   ```csv
   email,password,proxy_ip,proxy_port
   <EMAIL>,password1,*************,8080
   <EMAIL>,password2,,
   ```
2. 点击 **"导入账号"** 按钮
3. 选择CSV文件进行导入

### 2. 登录验证操作

#### 单个账号验证
1. 在账号列表中找到要验证的账号
2. **右键点击** 该账号
3. 选择 **"🔐 登录验证"**
4. 确认登录验证对话框
5. 等待浏览器打开并自动填写信息
6. 如遇验证码，手动完成验证
7. 验证完成后账号状态自动更新

#### 批量选择验证
1. 按住 `Ctrl` 键选择多个账号
2. **右键点击** 选中的账号
3. 选择 **"🔐 批量登录验证 (X个)"**
4. 确认批量验证对话框
5. 系统将依次验证每个账号

#### 全部账号验证
1. 点击工具栏的 **"🔐 批量登录验证"** 按钮
2. 确认对所有账号进行验证
3. 系统将依次验证所有账号

### 3. 验证过程说明

#### 自动化流程
1. **打开浏览器**: 系统自动打开Chrome浏览器
2. **访问登录页**: 导航到新浪邮箱登录页面
3. **填写信息**: 自动输入邮箱地址和密码
4. **点击登录**: 自动点击登录按钮
5. **检测验证**: 检查是否需要人机验证

#### 人工验证处理
当系统检测到需要验证码时：
1. **暂停等待**: 系统暂停自动化流程
2. **显示提示**: 进度对话框显示"等待人工验证..."
3. **手动操作**: 用户在浏览器中完成验证
4. **自动检测**: 系统检测验证是否完成
5. **继续流程**: 验证完成后继续登录流程

#### 常见验证类型
- **点击验证**: 点击"点击验证"按钮
- **滑块验证**: 拖动滑块到指定位置
- **图片验证**: 选择正确的图片
- **短信验证**: 输入手机验证码

## 📊 状态管理

### 账号状态说明
- **active**: 登录验证成功，账号可用
- **inactive**: 登录验证失败，账号不可用
- **error**: 验证过程中出现异常

### 状态更新机制
- 登录成功 → 状态更新为 `active`
- 登录失败 → 状态更新为 `inactive`
- 验证异常 → 状态更新为 `error`

## 🔧 右键菜单功能

在账号列表中右键点击可以看到以下选项：

### 🔐 登录验证
- **功能**: 对选中的单个账号进行登录验证
- **适用**: 需要验证特定账号时使用

### 🔐 批量登录验证 (X个)
- **功能**: 对选中的多个账号进行批量验证
- **显示条件**: 选择了2个或以上账号时显示
- **适用**: 需要验证部分账号时使用

### ✏️ 编辑
- **功能**: 编辑账号信息（邮箱、密码、代理等）
- **适用**: 需要修改账号信息时使用

### 🗑️ 删除
- **功能**: 删除选中的单个账号
- **适用**: 需要移除无用账号时使用

### 🗑️ 批量删除 (X个)
- **功能**: 批量删除选中的多个账号
- **显示条件**: 选择了2个或以上账号时显示
- **适用**: 需要清理多个账号时使用

## 🚀 工具栏功能

### 添加账号
- **功能**: 手动添加单个邮箱账号
- **操作**: 弹出账号编辑对话框

### 导入账号
- **功能**: 从CSV文件批量导入账号
- **支持格式**: CSV文件，包含邮箱、密码、代理信息

### 导出账号
- **功能**: 将账号信息导出到CSV文件
- **用途**: 备份账号信息或迁移到其他系统

### 🔐 批量登录验证
- **功能**: 对所有账号进行登录验证
- **样式**: 蓝色按钮，突出显示
- **适用**: 需要验证所有账号时使用

### 刷新
- **功能**: 刷新账号列表显示
- **用途**: 更新账号状态和统计信息

## ⚠️ 注意事项

### 网络环境
- **稳定网络**: 建议在网络稳定的环境下进行验证
- **代理配置**: 如需使用代理，请确保代理服务器正常
- **防火墙**: 确保防火墙不会阻止浏览器访问

### 验证码处理
- **手动完成**: 遇到验证码时需要手动完成
- **时间限制**: 验证码通常有时间限制，请及时处理
- **多次尝试**: 验证失败可以多次尝试

### 账号安全
- **密码保护**: 密码使用AES加密存储
- **定期更新**: 建议定期更新账号密码
- **权限控制**: 确保只有授权人员可以访问程序

### 性能考虑
- **批量限制**: 大量账号验证时建议分批进行
- **资源占用**: 每个验证会话会占用一定系统资源
- **时间规划**: 批量验证可能需要较长时间

## 🐛 故障排除

### 常见问题

#### 1. 浏览器无法启动
**现象**: 点击登录验证后没有浏览器窗口弹出
**原因**: Chrome浏览器未安装或路径配置错误
**解决方案**:
- 确保已安装Chrome浏览器
- 检查浏览器配置路径
- 尝试重启程序

#### 2. 登录页面无法访问
**现象**: 浏览器打开但无法访问新浪邮箱
**原因**: 网络连接问题或代理配置错误
**解决方案**:
- 检查网络连接
- 验证代理IP配置
- 尝试直连（不使用代理）

#### 3. 验证码识别失败
**现象**: 手动完成验证码后仍然提示失败
**原因**: 验证码超时或网络延迟
**解决方案**:
- 刷新页面重新验证
- 检查网络延迟
- 尝试更换网络环境

#### 4. 账号状态未更新
**现象**: 登录成功但账号状态仍为inactive
**原因**: 状态检测逻辑异常或数据库更新失败
**解决方案**:
- 点击"刷新"按钮
- 重新进行登录验证
- 检查程序日志

### 日志查看
程序运行日志保存在 `logs/sina_email_automation.log`，可以查看详细的错误信息：

```bash
# 查看最新日志
tail -f logs/sina_email_automation.log

# 搜索错误信息
grep "ERROR" logs/sina_email_automation.log
```

## 📞 技术支持

如果遇到问题，请：

1. **查看日志**: 检查 `logs/sina_email_automation.log` 文件
2. **运行测试**: 执行 `python test_login_verification.py` 验证功能
3. **检查配置**: 确认 `config/app_config.yaml` 配置正确
4. **重启程序**: 尝试重启程序解决临时问题

---

## 🎉 总结

新浪邮箱登录验证功能为您提供了完整的账号管理和验证解决方案。通过智能的自动化流程和人工验证支持，您可以高效地管理大量邮箱账号，确保账号的可用性和安全性。

**记住**: 合理使用验证功能，遵守相关服务条款，确保账号安全！
