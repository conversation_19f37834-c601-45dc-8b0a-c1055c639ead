#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件发送统计界面组件
提供发送记录查看、统计分析和数据导出功能
"""

import csv
import json
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QGroupBox, QComboBox, QDateEdit, QTabWidget,
    QTextEdit, QProgressBar, QFileDialog, QMessageBox, QSplitter,
    QHeaderView, QFrame, QGridLayout
)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette
from typing import List, Dict, Any, Optional
from src.models.send_record import SendRecord, SendRecordManager, SendStatus
from src.models.database import DatabaseManager
from src.utils.logger import get_logger

logger = get_logger("SendStatisticsWidget")


class StatisticsCard(QFrame):
    """统计卡片组件"""
    
    def __init__(self, title: str, value: str = "0", color: str = "#3498db"):
        super().__init__()
        self.init_ui(title, value, color)
    
    def init_ui(self, title: str, value: str, color: str):
        """初始化界面"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet(f"""
            QFrame {{
                border: 2px solid {color};
                border-radius: 8px;
                background-color: white;
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 12px;")
        layout.addWidget(title_label)
        
        # 数值
        self.value_label = QLabel(value)
        self.value_label.setAlignment(Qt.AlignCenter)
        self.value_label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 24px;")
        layout.addWidget(self.value_label)
        
        self.setLayout(layout)
    
    def update_value(self, value: str):
        """更新数值"""
        self.value_label.setText(value)


class SendStatisticsWidget(QWidget):
    """邮件发送统计主界面"""
    
    # 信号定义
    record_selected = pyqtSignal(SendRecord)
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.record_manager = SendRecordManager(db_manager)
        
        self.init_ui()
        self.load_statistics()
        
        # 定时刷新
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_statistics)
        self.refresh_timer.start(30000)  # 30秒刷新一次
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 统计概览选项卡
        overview_tab = self.create_overview_tab()
        self.tab_widget.addTab(overview_tab, "统计概览")
        
        # 发送记录选项卡
        records_tab = self.create_records_tab()
        self.tab_widget.addTab(records_tab, "发送记录")
        
        # 数据导出选项卡
        export_tab = self.create_export_tab()
        self.tab_widget.addTab(export_tab, "数据导出")
        
        layout.addWidget(self.tab_widget)
        self.setLayout(layout)
    
    def create_overview_tab(self) -> QWidget:
        """创建统计概览选项卡"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 时间范围选择
        time_group = QGroupBox("时间范围")
        time_layout = QHBoxLayout()
        
        time_layout.addWidget(QLabel("开始日期:"))
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-7))
        self.start_date_edit.setCalendarPopup(True)
        time_layout.addWidget(self.start_date_edit)
        
        time_layout.addWidget(QLabel("结束日期:"))
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        time_layout.addWidget(self.end_date_edit)
        
        self.refresh_stats_btn = QPushButton("刷新统计")
        self.refresh_stats_btn.clicked.connect(self.load_statistics)
        time_layout.addWidget(self.refresh_stats_btn)
        
        time_layout.addStretch()
        time_group.setLayout(time_layout)
        layout.addWidget(time_group)
        
        # 统计卡片区域
        cards_group = QGroupBox("发送统计")
        cards_layout = QGridLayout()
        
        self.total_card = StatisticsCard("总发送数", "0", "#3498db")
        cards_layout.addWidget(self.total_card, 0, 0)
        
        self.success_card = StatisticsCard("成功数", "0", "#27ae60")
        cards_layout.addWidget(self.success_card, 0, 1)
        
        self.failed_card = StatisticsCard("失败数", "0", "#e74c3c")
        cards_layout.addWidget(self.failed_card, 0, 2)
        
        self.success_rate_card = StatisticsCard("成功率", "0%", "#f39c12")
        cards_layout.addWidget(self.success_rate_card, 0, 3)
        
        self.pending_card = StatisticsCard("待发送", "0", "#9b59b6")
        cards_layout.addWidget(self.pending_card, 1, 0)
        
        self.avg_response_card = StatisticsCard("平均响应时间", "0s", "#1abc9c")
        cards_layout.addWidget(self.avg_response_card, 1, 1)
        
        cards_group.setLayout(cards_layout)
        layout.addWidget(cards_group)
        
        # 详细统计表格
        details_group = QGroupBox("详细统计")
        details_layout = QVBoxLayout()
        
        # 发送账号统计
        self.sender_table = QTableWidget()
        self.sender_table.setColumnCount(4)
        self.sender_table.setHorizontalHeaderLabels(["发送邮箱", "总数", "成功数", "成功率"])
        self.sender_table.horizontalHeader().setStretchLastSection(True)
        self.sender_table.setMaximumHeight(200)
        details_layout.addWidget(QLabel("发送账号统计:"))
        details_layout.addWidget(self.sender_table)
        
        # 每日统计
        self.daily_table = QTableWidget()
        self.daily_table.setColumnCount(4)
        self.daily_table.setHorizontalHeaderLabels(["日期", "总数", "成功数", "成功率"])
        self.daily_table.horizontalHeader().setStretchLastSection(True)
        self.daily_table.setMaximumHeight(200)
        details_layout.addWidget(QLabel("每日统计:"))
        details_layout.addWidget(self.daily_table)
        
        details_group.setLayout(details_layout)
        layout.addWidget(details_group)
        
        tab.setLayout(layout)
        return tab
    
    def create_records_tab(self) -> QWidget:
        """创建发送记录选项卡"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 过滤器
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("状态:"))
        self.status_filter = QComboBox()
        self.status_filter.addItem("全部", None)
        for status in SendStatus:
            self.status_filter.addItem(status.value, status)
        self.status_filter.currentIndexChanged.connect(self.filter_records)
        filter_layout.addWidget(self.status_filter)
        
        filter_layout.addWidget(QLabel("发送邮箱:"))
        self.email_filter = QComboBox()
        self.email_filter.addItem("全部", None)
        self.email_filter.currentIndexChanged.connect(self.filter_records)
        filter_layout.addWidget(self.email_filter)
        
        self.refresh_records_btn = QPushButton("刷新记录")
        self.refresh_records_btn.clicked.connect(self.load_records)
        filter_layout.addWidget(self.refresh_records_btn)
        
        filter_layout.addStretch()
        layout.addLayout(filter_layout)
        
        # 记录表格
        self.records_table = QTableWidget()
        self.records_table.setColumnCount(9)
        self.records_table.setHorizontalHeaderLabels([
            "ID", "任务ID", "发送邮箱", "收件邮箱", "主题", "状态", 
            "响应时间", "发送时间", "错误信息"
        ])
        
        # 设置列宽
        header = self.records_table.horizontalHeader()
        header.resizeSection(0, 50)   # ID
        header.resizeSection(1, 80)   # 任务ID
        header.resizeSection(2, 150)  # 发送邮箱
        header.resizeSection(3, 150)  # 收件邮箱
        header.resizeSection(4, 200)  # 主题
        header.resizeSection(5, 80)   # 状态
        header.resizeSection(6, 80)   # 响应时间
        header.resizeSection(7, 150)  # 发送时间
        header.setStretchLastSection(True)  # 错误信息
        
        self.records_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.records_table.setAlternatingRowColors(True)
        self.records_table.itemSelectionChanged.connect(self.on_record_selection_changed)
        
        layout.addWidget(self.records_table)
        
        tab.setLayout(layout)
        return tab
    
    def create_export_tab(self) -> QWidget:
        """创建数据导出选项卡"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 导出设置
        export_group = QGroupBox("导出设置")
        export_layout = QGridLayout()
        
        export_layout.addWidget(QLabel("开始日期:"), 0, 0)
        self.export_start_date = QDateEdit()
        self.export_start_date.setDate(QDate.currentDate().addDays(-30))
        self.export_start_date.setCalendarPopup(True)
        export_layout.addWidget(self.export_start_date, 0, 1)
        
        export_layout.addWidget(QLabel("结束日期:"), 0, 2)
        self.export_end_date = QDateEdit()
        self.export_end_date.setDate(QDate.currentDate())
        self.export_end_date.setCalendarPopup(True)
        export_layout.addWidget(self.export_end_date, 0, 3)
        
        export_layout.addWidget(QLabel("状态过滤:"), 1, 0)
        self.export_status_filter = QComboBox()
        self.export_status_filter.addItem("全部", None)
        for status in SendStatus:
            self.export_status_filter.addItem(status.value, status)
        export_layout.addWidget(self.export_status_filter, 1, 1)
        
        export_layout.addWidget(QLabel("导出格式:"), 1, 2)
        self.export_format = QComboBox()
        self.export_format.addItems(["CSV", "JSON"])
        export_layout.addWidget(self.export_format, 1, 3)
        
        export_group.setLayout(export_layout)
        layout.addWidget(export_group)
        
        # 导出按钮
        button_layout = QHBoxLayout()
        
        self.export_btn = QPushButton("导出数据")
        self.export_btn.clicked.connect(self.export_data)
        button_layout.addWidget(self.export_btn)
        
        self.clear_old_btn = QPushButton("清理旧数据")
        self.clear_old_btn.clicked.connect(self.clear_old_data)
        button_layout.addWidget(self.clear_old_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 导出进度
        self.export_progress = QProgressBar()
        self.export_progress.setVisible(False)
        layout.addWidget(self.export_progress)
        
        # 导出日志
        self.export_log = QTextEdit()
        self.export_log.setMaximumHeight(200)
        self.export_log.setPlaceholderText("导出日志将显示在这里...")
        layout.addWidget(self.export_log)
        
        tab.setLayout(layout)
        return tab

    def load_statistics(self):
        """加载统计数据"""
        try:
            # 获取时间范围
            start_date = self.start_date_edit.date().toPyDate()
            end_date = self.end_date_edit.date().toPyDate()

            # 转换为datetime
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())

            # 获取统计数据
            stats = self.record_manager.get_statistics(start_datetime, end_datetime)

            if not stats:
                return

            # 更新统计卡片
            self.total_card.update_value(str(stats.get('total_count', 0)))
            self.success_card.update_value(str(stats.get('success_count', 0)))
            self.failed_card.update_value(str(stats.get('failed_count', 0)))
            self.success_rate_card.update_value(f"{stats.get('success_rate', 0)}%")
            self.pending_card.update_value(str(stats.get('pending_count', 0)))
            self.avg_response_card.update_value(f"{stats.get('avg_response_time', 0)}s")

            # 更新发送账号统计表格
            self.update_sender_table(stats.get('top_senders', []))

            # 更新每日统计表格
            self.update_daily_table(stats.get('daily_stats', []))

            logger.info("统计数据加载完成")

        except Exception as e:
            logger.error(f"加载统计数据失败: {e}")
            QMessageBox.critical(self, "错误", f"加载统计数据失败: {e}")

    def update_sender_table(self, sender_stats: List[Dict[str, Any]]):
        """更新发送账号统计表格"""
        self.sender_table.setRowCount(len(sender_stats))

        for row, stats in enumerate(sender_stats):
            # 发送邮箱
            self.sender_table.setItem(row, 0, QTableWidgetItem(stats['from_email']))

            # 总数
            total_count = stats['count']
            self.sender_table.setItem(row, 1, QTableWidgetItem(str(total_count)))

            # 成功数
            success_count = stats['success_count']
            self.sender_table.setItem(row, 2, QTableWidgetItem(str(success_count)))

            # 成功率
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0
            rate_item = QTableWidgetItem(f"{success_rate:.1f}%")

            # 根据成功率设置颜色
            if success_rate >= 90:
                rate_item.setBackground(QColor(144, 238, 144))  # 浅绿色
            elif success_rate >= 70:
                rate_item.setBackground(QColor(255, 255, 144))  # 浅黄色
            else:
                rate_item.setBackground(QColor(255, 182, 193))  # 浅红色

            self.sender_table.setItem(row, 3, rate_item)

    def update_daily_table(self, daily_stats: List[Dict[str, Any]]):
        """更新每日统计表格"""
        self.daily_table.setRowCount(len(daily_stats))

        for row, stats in enumerate(daily_stats):
            # 日期
            self.daily_table.setItem(row, 0, QTableWidgetItem(stats['date']))

            # 总数
            total_count = stats['count']
            self.daily_table.setItem(row, 1, QTableWidgetItem(str(total_count)))

            # 成功数
            success_count = stats['success_count']
            self.daily_table.setItem(row, 2, QTableWidgetItem(str(success_count)))

            # 成功率
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0
            rate_item = QTableWidgetItem(f"{success_rate:.1f}%")

            # 根据成功率设置颜色
            if success_rate >= 90:
                rate_item.setBackground(QColor(144, 238, 144))
            elif success_rate >= 70:
                rate_item.setBackground(QColor(255, 255, 144))
            else:
                rate_item.setBackground(QColor(255, 182, 193))

            self.daily_table.setItem(row, 3, rate_item)

    def load_records(self):
        """加载发送记录"""
        try:
            # 获取过滤条件
            status_filter = self.status_filter.currentData()

            # 获取记录
            if status_filter:
                records = self.record_manager.get_records_by_status(status_filter, 1000)
            else:
                # 获取最近的记录
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)
                records = self.record_manager.get_records_by_date_range(start_date, end_date)

            # 更新表格
            self.update_records_table(records)

            # 更新邮箱过滤器
            self.update_email_filter(records)

            logger.info(f"加载了 {len(records)} 条发送记录")

        except Exception as e:
            logger.error(f"加载发送记录失败: {e}")
            QMessageBox.critical(self, "错误", f"加载发送记录失败: {e}")

    def update_records_table(self, records: List[SendRecord]):
        """更新发送记录表格"""
        self.records_table.setRowCount(len(records))

        for row, record in enumerate(records):
            # ID
            self.records_table.setItem(row, 0, QTableWidgetItem(str(record.id)))

            # 任务ID
            task_id = record.task_id or ""
            self.records_table.setItem(row, 1, QTableWidgetItem(task_id))

            # 发送邮箱
            self.records_table.setItem(row, 2, QTableWidgetItem(record.from_email))

            # 收件邮箱
            self.records_table.setItem(row, 3, QTableWidgetItem(record.to_email))

            # 主题
            subject = record.subject[:50] + "..." if len(record.subject) > 50 else record.subject
            self.records_table.setItem(row, 4, QTableWidgetItem(subject))

            # 状态
            status_item = QTableWidgetItem(record.status.value)
            if record.status == SendStatus.SUCCESS:
                status_item.setBackground(QColor(144, 238, 144))
            elif record.status == SendStatus.FAILED:
                status_item.setBackground(QColor(255, 182, 193))
            elif record.status == SendStatus.PENDING:
                status_item.setBackground(QColor(255, 255, 144))
            self.records_table.setItem(row, 5, status_item)

            # 响应时间
            response_time = f"{record.response_time:.3f}s" if record.response_time else ""
            self.records_table.setItem(row, 6, QTableWidgetItem(response_time))

            # 发送时间
            send_time = record.send_time.strftime("%Y-%m-%d %H:%M:%S") if record.send_time else ""
            self.records_table.setItem(row, 7, QTableWidgetItem(send_time))

            # 错误信息
            error_msg = record.error_msg or ""
            self.records_table.setItem(row, 8, QTableWidgetItem(error_msg))

            # 存储记录对象
            self.records_table.item(row, 0).setData(Qt.UserRole, record)

    def update_email_filter(self, records: List[SendRecord]):
        """更新邮箱过滤器"""
        # 获取所有发送邮箱
        emails = list(set(record.from_email for record in records))
        emails.sort()

        # 清空并重新填充
        current_email = self.email_filter.currentData()
        self.email_filter.clear()
        self.email_filter.addItem("全部", None)

        for email in emails:
            self.email_filter.addItem(email, email)

        # 恢复选择
        if current_email:
            index = self.email_filter.findData(current_email)
            if index >= 0:
                self.email_filter.setCurrentIndex(index)

    def filter_records(self):
        """过滤记录"""
        try:
            status_filter = self.status_filter.currentData()
            email_filter = self.email_filter.currentData()

            # 获取所有记录
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            all_records = self.record_manager.get_records_by_date_range(start_date, end_date)

            # 应用过滤器
            filtered_records = []
            for record in all_records:
                if status_filter and record.status != status_filter:
                    continue
                if email_filter and record.from_email != email_filter:
                    continue
                filtered_records.append(record)

            # 更新表格
            self.update_records_table(filtered_records)

        except Exception as e:
            logger.error(f"过滤记录失败: {e}")

    def on_record_selection_changed(self):
        """记录选择改变时的处理"""
        selected_rows = self.records_table.selectionModel().selectedRows()
        if selected_rows:
            row = selected_rows[0].row()
            record = self.records_table.item(row, 0).data(Qt.UserRole)
            if record:
                self.record_selected.emit(record)

    def export_data(self):
        """导出数据"""
        try:
            # 获取导出设置
            start_date = self.export_start_date.date().toPyDate()
            end_date = self.export_end_date.date().toPyDate()
            status_filter = self.export_status_filter.currentData()
            export_format = self.export_format.currentText()

            # 转换为datetime
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())

            # 获取数据
            self.export_progress.setVisible(True)
            self.export_progress.setValue(0)

            export_data = self.record_manager.export_records(
                start_datetime, end_datetime, status_filter
            )

            if not export_data:
                QMessageBox.information(self, "提示", "没有找到符合条件的数据")
                self.export_progress.setVisible(False)
                return

            self.export_progress.setValue(50)

            # 选择保存路径
            if export_format == "CSV":
                file_path, _ = QFileDialog.getSaveFileName(
                    self, "导出CSV文件", f"send_records_{start_date}_{end_date}.csv",
                    "CSV Files (*.csv)"
                )
                if file_path:
                    self.export_to_csv(export_data, file_path)
            else:  # JSON
                file_path, _ = QFileDialog.getSaveFileName(
                    self, "导出JSON文件", f"send_records_{start_date}_{end_date}.json",
                    "JSON Files (*.json)"
                )
                if file_path:
                    self.export_to_json(export_data, file_path)

            self.export_progress.setValue(100)
            self.export_progress.setVisible(False)

            if file_path:
                self.export_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] 导出完成: {file_path}")
                self.export_log.append(f"导出记录数: {len(export_data)}")
                QMessageBox.information(self, "成功", f"数据导出成功\n文件: {file_path}")

        except Exception as e:
            self.export_progress.setVisible(False)
            logger.error(f"导出数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导出数据失败: {e}")

    def export_to_csv(self, data: List[Dict[str, Any]], file_path: str):
        """导出为CSV格式"""
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            if not data:
                return

            fieldnames = data[0].keys()
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for row in data:
                # 处理None值
                clean_row = {k: (v if v is not None else '') for k, v in row.items()}
                writer.writerow(clean_row)

    def export_to_json(self, data: List[Dict[str, Any]], file_path: str):
        """导出为JSON格式"""
        with open(file_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, ensure_ascii=False, indent=2, default=str)

    def clear_old_data(self):
        """清理旧数据"""
        try:
            # 询问保留天数
            from PyQt5.QtWidgets import QInputDialog

            days, ok = QInputDialog.getInt(
                self, "清理旧数据", "请输入要保留的天数:", 30, 1, 365
            )

            if not ok:
                return

            reply = QMessageBox.question(
                self, "确认清理",
                f"确定要删除 {days} 天前的发送记录吗？\n此操作不可撤销。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                deleted_count = self.record_manager.delete_old_records(days)

                self.export_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] 清理完成")
                self.export_log.append(f"删除记录数: {deleted_count}")

                QMessageBox.information(self, "成功", f"清理完成\n删除了 {deleted_count} 条记录")

                # 刷新数据
                self.load_statistics()
                self.load_records()

        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
            QMessageBox.critical(self, "错误", f"清理旧数据失败: {e}")
