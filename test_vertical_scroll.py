#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
垂直滚动功能测试
测试右侧面板的垂直滚动是否能完全展示所有界面内容
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer
from src.gui.multi_browser_sender_widget import MultiBrowserSenderWidget


class VerticalScrollTestWindow(QMainWindow):
    """垂直滚动测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_test_data()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("📜 垂直滚动功能测试 - 完全展示所有界面内容")
        self.setGeometry(50, 50, 1600, 1000)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 添加测试说明
        info_layout = QHBoxLayout()
        
        info_label = QLabel("""
📜 垂直滚动功能测试

🎯 本次优化重点：
✅ 右侧面板添加QScrollArea垂直滚动支持
✅ 移除所有组件的最大高度限制
✅ 邮件内容编辑框最小高度400px，无最大限制
✅ 所有组件可以自然展开，通过滚动查看
✅ 确保"手动输入邮件发送"功能完整可见

🔍 请重点测试：
1. 右侧是否出现垂直滚动条
2. 滚动是否能看到所有内容
3. 邮件编辑区域是否足够大
4. 所有功能是否完整可用
5. 滚动操作是否流畅
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8;
                border: 2px solid #28a745;
                border-radius: 8px;
                padding: 10px;
                font-size: 10pt;
                color: #155724;
            }
        """)
        info_layout.addWidget(info_label)
        
        # 添加测试按钮
        test_buttons_layout = QVBoxLayout()
        
        self.fill_long_content_btn = QPushButton("📝 填充超长邮件内容")
        self.fill_long_content_btn.clicked.connect(self.fill_very_long_content)
        test_buttons_layout.addWidget(self.fill_long_content_btn)
        
        self.test_scroll_btn = QPushButton("📜 测试滚动功能")
        self.test_scroll_btn.clicked.connect(self.test_scroll_function)
        test_buttons_layout.addWidget(self.test_scroll_btn)
        
        self.check_visibility_btn = QPushButton("👁️ 检查内容可见性")
        self.check_visibility_btn.clicked.connect(self.check_content_visibility)
        test_buttons_layout.addWidget(self.check_visibility_btn)
        
        self.switch_email_tab_btn = QPushButton("✉️ 切换到邮件发送")
        self.switch_email_tab_btn.clicked.connect(self.switch_to_email_tab)
        test_buttons_layout.addWidget(self.switch_email_tab_btn)
        
        test_buttons_layout.addStretch()
        info_layout.addLayout(test_buttons_layout)
        
        layout.addLayout(info_layout)
        
        # 创建多浏览器发送组件
        self.sender_widget = MultiBrowserSenderWidget()
        layout.addWidget(self.sender_widget)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
        """)
    
    def setup_test_data(self):
        """设置测试数据"""
        # 延迟执行，确保界面完全加载
        QTimer.singleShot(1000, self.switch_to_email_tab)
        QTimer.singleShot(2000, self.fill_very_long_content)
    
    def switch_to_email_tab(self):
        """切换到邮件发送选项卡"""
        try:
            if hasattr(self.sender_widget, 'task_tab_widget'):
                tab_widget = self.sender_widget.task_tab_widget
                for i in range(tab_widget.count()):
                    tab_text = tab_widget.tabText(i)
                    if "手动输入" in tab_text or "邮件发送" in tab_text:
                        tab_widget.setCurrentIndex(i)
                        print(f"✅ 已切换到选项卡: {tab_text}")
                        return
                print("❌ 未找到邮件发送选项卡")
            else:
                print("❌ 未找到选项卡组件")
        except Exception as e:
            print(f"❌ 切换选项卡失败: {e}")
    
    def fill_very_long_content(self):
        """填充超长邮件内容来测试滚动"""
        try:
            # 填充收件人
            if hasattr(self.sender_widget, 'to_email_edit'):
                self.sender_widget.to_email_edit.setText("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>")
            
            # 填充主题
            if hasattr(self.sender_widget, 'subject_edit'):
                self.sender_widget.subject_edit.setText("【垂直滚动测试】超长邮件内容测试 - {name}您好")
            
            # 填充超长内容
            if hasattr(self.sender_widget, 'content_edit'):
                very_long_content = """亲爱的 {name}，

您好！这是一封专门用于测试垂直滚动功能的超长邮件。

## 🎯 垂直滚动功能测试

### 1. 滚动区域实现
我们为右侧面板添加了QScrollArea，具有以下特性：
- setWidgetResizable(True) - 自动调整内容大小
- setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff) - 禁用水平滚动
- setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded) - 按需显示垂直滚动
- setFrameShape(QFrame.NoFrame) - 去除边框，美观整洁

### 2. 组件高度优化
所有组件都移除了最大高度限制：
- 邮件内容编辑框：最小400px，无最大限制
- 状态表格：最小120px，根据内容自适应
- 任务表格：最小180px，根据内容自适应
- 日志区域：最小150px，根据内容自适应

### 3. 测试内容展示
这段超长的邮件内容用于测试以下功能：

#### 3.1 编辑体验测试
请尝试在这个编辑框中：
- 输入更多文本内容
- 使用回车键换行
- 复制粘贴大段文字
- 滚动查看所有内容

#### 3.2 滚动功能测试
请测试以下滚动操作：
- 使用鼠标滚轮滚动
- 拖拽滚动条滚动
- 使用键盘上下箭头
- 使用Page Up/Page Down

#### 3.3 内容可见性测试
请确认以下内容都能通过滚动看到：
- 收件人输入框
- 邮件主题输入框
- 邮件内容编辑框（当前区域）
- 内容类型选择
- 发送按钮
- 详细统计表格
- 日志显示区域

### 4. 变量支持功能
邮件内容支持以下变量替换：
- {name} - 收件人姓名
- {email} - 收件人邮箱地址
- {company} - 收件人公司名称
- {date} - 当前日期
- {time} - 当前时间
- {subject} - 邮件主题
- {sender} - 发件人信息

### 5. 高级功能测试
请测试以下高级功能：
- 邮件模板选择
- 内容类型切换（text/plain 或 text/html）
- 变量预览功能
- 批量发送设置

### 6. 用户体验验证
通过这次垂直滚动优化，用户应该能够：
1. 看到完整的"手动输入邮件发送"功能
2. 舒适地编辑长邮件内容
3. 通过滚动访问所有功能区域
4. 享受流畅的滚动体验
5. 在任何屏幕尺寸下都能正常使用

### 7. 技术实现细节
```python
# 滚动区域创建
scroll_area = QScrollArea()
scroll_area.setWidgetResizable(True)
scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

# 内容容器
scroll_content = QWidget()
scroll_layout = QVBoxLayout()
scroll_layout.addWidget(task_group)
scroll_layout.addWidget(detailed_status)
scroll_layout.addWidget(log_group)
```

### 8. 测试结论
如果您能够：
- 看到这段完整的长文本
- 流畅地滚动查看所有内容
- 正常使用所有邮件功能
- 舒适地编辑邮件内容

那么垂直滚动功能优化就是成功的！

### 9. 后续优化建议
- 可以考虑添加滚动位置记忆功能
- 优化滚动速度和灵敏度
- 添加快速定位功能
- 支持触摸屏滚动手势

### 10. 感谢测试
感谢您测试垂直滚动功能！您的反馈对我们改进用户体验非常重要。

如果您在使用过程中发现任何问题或有改进建议，请随时联系我们。

此致
敬礼！

垂直滚动功能测试团队
{date} {time}

---

附加测试内容：

这里是更多的测试文本，用于验证滚动功能是否正常工作。
这里是更多的测试文本，用于验证滚动功能是否正常工作。
这里是更多的测试文本，用于验证滚动功能是否正常工作。
这里是更多的测试文本，用于验证滚动功能是否正常工作。
这里是更多的测试文本，用于验证滚动功能是否正常工作。

请继续滚动查看更多内容...

这是邮件的结尾部分，如果您能看到这里，说明滚动功能工作正常！"""
                
                self.sender_widget.content_edit.setPlainText(very_long_content)
                print("✅ 超长邮件内容填充完成")
                print("📜 请测试垂直滚动功能")
            
        except Exception as e:
            print(f"❌ 填充超长内容失败: {e}")
    
    def test_scroll_function(self):
        """测试滚动功能"""
        try:
            print("\n📜 滚动功能测试：")
            print("   1. 请使用鼠标滚轮在右侧面板滚动")
            print("   2. 请拖拽右侧滚动条")
            print("   3. 请检查是否能看到所有内容")
            print("   4. 请验证滚动是否流畅")
            print("✅ 滚动功能测试指引完成")
        except Exception as e:
            print(f"❌ 滚动功能测试失败: {e}")
    
    def check_content_visibility(self):
        """检查内容可见性"""
        try:
            print("\n👁️ 内容可见性检查：")
            
            # 检查关键组件是否存在
            components = [
                ('收件人输入框', 'to_email_edit'),
                ('邮件主题输入框', 'subject_edit'),
                ('邮件内容编辑框', 'content_edit'),
                ('状态表格', 'status_table'),
                ('任务表格', 'task_table'),
                ('日志区域', 'log_text')
            ]
            
            for name, attr in components:
                if hasattr(self.sender_widget, attr):
                    component = getattr(self.sender_widget, attr)
                    if component.isVisible():
                        print(f"   ✅ {name}: 可见")
                    else:
                        print(f"   ❌ {name}: 不可见")
                else:
                    print(f"   ❓ {name}: 未找到")
            
            print("✅ 内容可见性检查完成")
            
        except Exception as e:
            print(f"❌ 检查内容可见性失败: {e}")


def main():
    """主函数"""
    print("📜 启动垂直滚动功能测试...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("垂直滚动功能测试")
    app.setApplicationVersion("2.0")
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    try:
        # 创建测试窗口
        window = VerticalScrollTestWindow()
        window.show()
        
        print("✅ 垂直滚动功能测试启动成功！")
        print("\n🎯 重点测试项目：")
        print("   1. 右侧面板是否出现垂直滚动条")
        print("   2. 滚动是否能查看所有内容")
        print("   3. 邮件编辑区域是否足够大")
        print("   4. 所有功能是否完整可用")
        print("   5. 滚动操作是否流畅自然")
        print("\n📜 滚动功能特性：")
        print("   - 自动显示/隐藏垂直滚动条")
        print("   - 禁用水平滚动，避免混乱")
        print("   - 无边框设计，界面更美观")
        print("   - 组件高度自适应内容")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
