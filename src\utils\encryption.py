#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
加密解密工具模块
提供密码和敏感信息的加密解密功能
"""

import os
import base64
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from typing import Optional, Union, Dict, Any
from src.utils.logger import get_logger

logger = get_logger("Encryption")


class EncryptionManager:
    """加密管理器"""
    
    def __init__(self, key_file: Optional[str] = None):
        """
        初始化加密管理器
        
        Args:
            key_file: 密钥文件路径，如果为None则使用默认路径
        """
        if key_file is None:
            project_root = Path(__file__).parent.parent.parent
            config_dir = project_root / "config"
            config_dir.mkdir(exist_ok=True)
            self.key_file = config_dir / ".encryption_key"
        else:
            self.key_file = Path(key_file)
        
        self._fernet = None
        self._load_or_create_key()
    
    def _load_or_create_key(self):
        """加载或创建加密密钥"""
        try:
            if self.key_file.exists():
                # 加载现有密钥
                with open(self.key_file, 'rb') as f:
                    key = f.read()
                self._fernet = Fernet(key)
                logger.info("加密密钥加载成功")
            else:
                # 创建新密钥
                key = Fernet.generate_key()
                with open(self.key_file, 'wb') as f:
                    f.write(key)
                
                # 设置文件权限（仅所有者可读写）
                if os.name != 'nt':  # 非Windows系统
                    os.chmod(self.key_file, 0o600)
                
                self._fernet = Fernet(key)
                logger.info(f"新加密密钥创建成功: {self.key_file}")
                
        except Exception as e:
            logger.error(f"加密密钥初始化失败: {e}")
            raise
    
    def encrypt(self, data: Union[str, bytes]) -> str:
        """
        加密数据
        
        Args:
            data: 要加密的数据（字符串或字节）
        
        Returns:
            Base64编码的加密数据
        """
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            encrypted_data = self._fernet.encrypt(data)
            return base64.b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            logger.error(f"数据加密失败: {e}")
            raise
    
    def decrypt(self, encrypted_data: str) -> str:
        """
        解密数据

        Args:
            encrypted_data: Base64编码的加密数据

        Returns:
            解密后的字符串
        """
        try:
            # 检查输入是否为空或无效
            if not encrypted_data or not encrypted_data.strip():
                logger.warning("解密数据为空")
                return ""

            # 处理可能的填充问题
            encrypted_data = encrypted_data.strip()

            # 确保base64字符串长度是4的倍数
            missing_padding = len(encrypted_data) % 4
            if missing_padding:
                encrypted_data += '=' * (4 - missing_padding)

            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self._fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')

        except Exception as e:
            logger.error(f"数据解密失败: {e}")
            return ""  # 返回空字符串而不是抛出异常
    
    def encrypt_password(self, password: str) -> str:
        """
        加密密码
        
        Args:
            password: 明文密码
        
        Returns:
            加密后的密码
        """
        return self.encrypt(password)
    
    def decrypt_password(self, encrypted_password: str) -> str:
        """
        解密密码
        
        Args:
            encrypted_password: 加密的密码
        
        Returns:
            明文密码
        """
        return self.decrypt(encrypted_password)
    
    def is_encrypted(self, data: str) -> bool:
        """
        检查数据是否已加密
        
        Args:
            data: 要检查的数据
        
        Returns:
            是否已加密
        """
        try:
            # 尝试解密，如果成功说明是加密数据
            self.decrypt(data)
            return True
        except:
            return False
    
    def change_key(self, new_key_file: Optional[str] = None) -> bool:
        """
        更换加密密钥
        
        Args:
            new_key_file: 新密钥文件路径
        
        Returns:
            是否更换成功
        """
        try:
            # 备份当前密钥
            backup_key_file = str(self.key_file) + ".backup"
            if self.key_file.exists():
                import shutil
                shutil.copy2(self.key_file, backup_key_file)
            
            # 生成新密钥
            if new_key_file:
                self.key_file = Path(new_key_file)
            
            # 删除旧密钥文件
            if self.key_file.exists():
                self.key_file.unlink()
            
            # 创建新密钥
            self._load_or_create_key()
            
            logger.info("加密密钥更换成功")
            return True
            
        except Exception as e:
            logger.error(f"更换加密密钥失败: {e}")
            return False


class PasswordManager:
    """密码管理器"""
    
    def __init__(self, encryption_manager: EncryptionManager):
        """
        初始化密码管理器
        
        Args:
            encryption_manager: 加密管理器实例
        """
        self.encryption = encryption_manager
    
    def store_password(self, password: str) -> str:
        """
        存储密码（加密）
        
        Args:
            password: 明文密码
        
        Returns:
            加密后的密码
        """
        if not password:
            return ""
        
        # 如果已经是加密的，直接返回
        if self.encryption.is_encrypted(password):
            return password
        
        return self.encryption.encrypt_password(password)
    
    def retrieve_password(self, encrypted_password: str) -> str:
        """
        获取密码（解密）
        
        Args:
            encrypted_password: 加密的密码
        
        Returns:
            明文密码
        """
        if not encrypted_password:
            return ""
        
        # 如果不是加密的，直接返回（兼容旧数据）
        if not self.encryption.is_encrypted(encrypted_password):
            return encrypted_password
        
        return self.encryption.decrypt_password(encrypted_password)
    
    def validate_password_strength(self, password: str) -> Dict[str, Union[bool, str]]:
        """
        验证密码强度
        
        Args:
            password: 密码
        
        Returns:
            验证结果字典
        """
        result = {
            'is_valid': True,
            'score': 0,
            'message': '',
            'suggestions': []
        }
        
        if len(password) < 6:
            result['is_valid'] = False
            result['message'] = '密码长度至少6位'
            result['suggestions'].append('增加密码长度')
        
        # 检查字符类型
        has_lower = any(c.islower() for c in password)
        has_upper = any(c.isupper() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password)
        
        score = sum([has_lower, has_upper, has_digit, has_special])
        result['score'] = score
        
        if score < 2:
            result['suggestions'].append('使用大小写字母、数字和特殊字符的组合')
        
        if len(password) >= 8 and score >= 3:
            result['message'] = '密码强度良好'
        elif len(password) >= 6 and score >= 2:
            result['message'] = '密码强度一般'
        else:
            result['message'] = '密码强度较弱'
        
        return result


# 创建全局实例
_encryption_manager = None
_password_manager = None


def get_encryption_manager() -> EncryptionManager:
    """获取全局加密管理器实例"""
    global _encryption_manager
    if _encryption_manager is None:
        _encryption_manager = EncryptionManager()
    return _encryption_manager


def get_password_manager() -> PasswordManager:
    """获取全局密码管理器实例"""
    global _password_manager
    if _password_manager is None:
        _password_manager = PasswordManager(get_encryption_manager())
    return _password_manager
