#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件模板管理界面组件
为多浏览器发送系统提供完整的邮件模板管理功能
"""

import json
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QLabel, QMessageBox, QGroupBox, QTextEdit,
    QComboBox, QDialog, QFormLayout, QDialogButtonBox, QSplitter,
    QHeaderView, QMenu, QAction, QCheckBox, QTabWidget, QPlainTextEdit
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QIcon
from typing import List, Dict, Any, Optional
from src.models.email_template import EmailTemplate, EmailTemplateManager
from src.models.database import DatabaseManager
from src.utils.logger import get_logger

logger = get_logger("EmailTemplateWidget")


class EmailTemplateDialog(QDialog):
    """邮件模板编辑对话框"""
    
    def __init__(self, parent=None, template: Optional[EmailTemplate] = None):
        super().__init__(parent)
        self.template = template
        self.is_edit_mode = template is not None
        
        self.init_ui()
        if self.template:
            self.load_template_data()
    
    def init_ui(self):
        """初始化界面"""
        title = "编辑邮件模板" if self.is_edit_mode else "新建邮件模板"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout()
        
        # 基本信息
        form_layout = QFormLayout()
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入模板名称")
        form_layout.addRow("模板名称:", self.name_edit)
        
        self.subject_edit = QLineEdit()
        self.subject_edit.setPlaceholderText("请输入邮件主题，支持变量如{name}")
        form_layout.addRow("邮件主题:", self.subject_edit)
        
        self.content_type_combo = QComboBox()
        self.content_type_combo.addItems(["text/plain", "text/html"])
        self.content_type_combo.currentTextChanged.connect(self.on_content_type_changed)
        form_layout.addRow("内容类型:", self.content_type_combo)
        
        self.is_default_check = QCheckBox("设为默认模板")
        form_layout.addRow("", self.is_default_check)
        
        layout.addLayout(form_layout)
        
        # 内容编辑区域
        content_group = QGroupBox("邮件内容")
        content_layout = QVBoxLayout()
        
        # 选项卡
        self.tab_widget = QTabWidget()
        
        # 内容编辑选项卡
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("请输入邮件内容，支持变量如{name}、{company}等")
        self.tab_widget.addTab(self.content_edit, "内容编辑")
        
        # 变量管理选项卡
        variables_widget = QWidget()
        variables_layout = QVBoxLayout()
        
        variables_help = QLabel("变量格式: {变量名}\n常用变量: {name}姓名, {company}公司, {email}邮箱, {phone}电话")
        variables_help.setStyleSheet("color: #666; font-size: 12px;")
        variables_layout.addWidget(variables_help)
        
        self.variables_edit = QPlainTextEdit()
        self.variables_edit.setPlaceholderText('请输入变量说明，JSON格式:\n{\n  "name": "收件人姓名",\n  "company": "公司名称",\n  "product": "产品名称"\n}')
        self.variables_edit.setMaximumHeight(150)
        variables_layout.addWidget(self.variables_edit)
        
        variables_widget.setLayout(variables_layout)
        self.tab_widget.addTab(variables_widget, "变量管理")
        
        # 预览选项卡
        self.preview_edit = QTextEdit()
        self.preview_edit.setReadOnly(True)
        self.tab_widget.addTab(self.preview_edit, "预览效果")
        
        content_layout.addWidget(self.tab_widget)
        content_group.setLayout(content_layout)
        layout.addWidget(content_group)
        
        # 预览按钮
        preview_layout = QHBoxLayout()
        self.preview_btn = QPushButton("预览模板")
        self.preview_btn.clicked.connect(self.preview_template)
        preview_layout.addWidget(self.preview_btn)
        preview_layout.addStretch()
        layout.addLayout(preview_layout)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def on_content_type_changed(self, content_type: str):
        """内容类型改变时的处理"""
        if content_type == "text/html":
            self.content_edit.setPlaceholderText("请输入HTML格式的邮件内容，支持变量如{name}")
        else:
            self.content_edit.setPlaceholderText("请输入纯文本邮件内容，支持变量如{name}")
    
    def load_template_data(self):
        """加载模板数据"""
        if not self.template:
            return
        
        self.name_edit.setText(self.template.template_name)
        self.subject_edit.setText(self.template.subject)
        self.content_edit.setPlainText(self.template.content)
        
        # 设置内容类型
        index = self.content_type_combo.findText(self.template.content_type)
        if index >= 0:
            self.content_type_combo.setCurrentIndex(index)
        
        self.is_default_check.setChecked(self.template.is_default)
        
        # 加载变量信息
        if self.template.variables:
            self.variables_edit.setPlainText(self.template.variables)
    
    def preview_template(self):
        """预览模板"""
        try:
            # 获取变量
            variables = {}
            variables_text = self.variables_edit.toPlainText().strip()
            if variables_text:
                try:
                    variables = json.loads(variables_text)
                except json.JSONDecodeError:
                    QMessageBox.warning(self, "警告", "变量格式不正确，请使用JSON格式")
                    return
            
            # 示例变量值
            sample_vars = {
                "name": "张三",
                "company": "ABC公司", 
                "email": "<EMAIL>",
                "phone": "13800138000",
                "product": "新产品"
            }
            
            # 合并变量
            for key in variables.keys():
                if key not in sample_vars:
                    sample_vars[key] = f"[{key}示例值]"
            
            # 渲染预览
            subject = self.subject_edit.text()
            content = self.content_edit.toPlainText()
            
            for key, value in sample_vars.items():
                placeholder = f"{{{key}}}"
                subject = subject.replace(placeholder, str(value))
                content = content.replace(placeholder, str(value))
            
            preview_text = f"主题: {subject}\n\n内容:\n{content}"
            self.preview_edit.setPlainText(preview_text)
            
            # 切换到预览选项卡
            self.tab_widget.setCurrentIndex(2)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"预览失败: {e}")
    
    def get_template_data(self) -> EmailTemplate:
        """获取模板数据"""
        template = EmailTemplate()
        if self.is_edit_mode and self.template:
            template.id = self.template.id
        
        template.template_name = self.name_edit.text().strip()
        template.subject = self.subject_edit.text().strip()
        template.content = self.content_edit.toPlainText().strip()
        template.content_type = self.content_type_combo.currentText()
        template.is_default = self.is_default_check.isChecked()
        template.variables = self.variables_edit.toPlainText().strip()
        
        return template
    
    def accept(self):
        """确认保存"""
        # 验证数据
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "警告", "请输入模板名称")
            self.name_edit.setFocus()
            return
        
        if not self.subject_edit.text().strip():
            QMessageBox.warning(self, "警告", "请输入邮件主题")
            self.subject_edit.setFocus()
            return
        
        if not self.content_edit.toPlainText().strip():
            QMessageBox.warning(self, "警告", "请输入邮件内容")
            self.content_edit.setFocus()
            return
        
        # 验证变量格式
        variables_text = self.variables_edit.toPlainText().strip()
        if variables_text:
            try:
                json.loads(variables_text)
            except json.JSONDecodeError:
                QMessageBox.warning(self, "警告", "变量格式不正确，请使用JSON格式")
                self.tab_widget.setCurrentIndex(1)
                self.variables_edit.setFocus()
                return
        
        super().accept()


class EmailTemplateWidget(QWidget):
    """邮件模板管理主界面"""
    
    # 信号定义
    template_selected = pyqtSignal(EmailTemplate)  # 模板被选中
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.template_manager = EmailTemplateManager(db_manager)
        
        self.init_ui()
        self.load_templates()
        
        # 确保数据库表存在
        self.ensure_database_table()
    
    def ensure_database_table(self):
        """确保数据库表存在并包含新字段"""
        try:
            # 检查表是否存在新字段
            query = "PRAGMA table_info(email_templates)"
            columns = self.db_manager.execute_query(query)
            column_names = [col['name'] for col in columns]
            
            # 如果缺少新字段，添加它们
            if 'content_type' not in column_names:
                self.db_manager.execute_update("ALTER TABLE email_templates ADD COLUMN content_type TEXT DEFAULT 'text/plain'")
                logger.info("添加 content_type 字段到 email_templates 表")
            
            if 'variables' not in column_names:
                self.db_manager.execute_update("ALTER TABLE email_templates ADD COLUMN variables TEXT DEFAULT ''")
                logger.info("添加 variables 字段到 email_templates 表")
                
        except Exception as e:
            logger.error(f"更新数据库表结构失败: {e}")

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 工具栏
        toolbar_layout = QHBoxLayout()

        self.add_btn = QPushButton("新建模板")
        self.add_btn.clicked.connect(self.add_template)
        toolbar_layout.addWidget(self.add_btn)

        self.edit_btn = QPushButton("编辑模板")
        self.edit_btn.clicked.connect(self.edit_template)
        self.edit_btn.setEnabled(False)
        toolbar_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton("删除模板")
        self.delete_btn.clicked.connect(self.delete_template)
        self.delete_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_btn)

        self.copy_btn = QPushButton("复制模板")
        self.copy_btn.clicked.connect(self.copy_template)
        self.copy_btn.setEnabled(False)
        toolbar_layout.addWidget(self.copy_btn)

        toolbar_layout.addStretch()

        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_templates)
        toolbar_layout.addWidget(self.refresh_btn)

        layout.addLayout(toolbar_layout)

        # 模板列表
        self.template_table = QTableWidget()
        self.template_table.setColumnCount(6)
        self.template_table.setHorizontalHeaderLabels([
            "ID", "模板名称", "邮件主题", "内容类型", "默认", "创建时间"
        ])

        # 设置列宽
        header = self.template_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 50)   # ID
        header.resizeSection(1, 150)  # 模板名称
        header.resizeSection(2, 200)  # 邮件主题
        header.resizeSection(3, 100)  # 内容类型
        header.resizeSection(4, 60)   # 默认

        # 设置表格属性
        self.template_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.template_table.setAlternatingRowColors(True)
        self.template_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.template_table.itemDoubleClicked.connect(self.edit_template)

        # 右键菜单
        self.template_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.template_table.customContextMenuRequested.connect(self.show_context_menu)

        layout.addWidget(self.template_table)

        # 预览区域
        preview_group = QGroupBox("模板预览")
        preview_layout = QVBoxLayout()

        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setMaximumHeight(150)
        self.preview_text.setPlaceholderText("选择模板查看预览...")
        preview_layout.addWidget(self.preview_text)

        preview_group.setLayout(preview_layout)
        layout.addWidget(preview_group)

        self.setLayout(layout)

    def load_templates(self):
        """加载模板列表"""
        try:
            templates = self.template_manager.get_all_templates()

            self.template_table.setRowCount(len(templates))

            for row, template in enumerate(templates):
                # ID
                self.template_table.setItem(row, 0, QTableWidgetItem(str(template.id)))

                # 模板名称
                name_item = QTableWidgetItem(template.template_name)
                if template.is_default:
                    name_item.setBackground(QColor(144, 238, 144))  # 浅绿色背景
                self.template_table.setItem(row, 1, name_item)

                # 邮件主题
                subject_item = QTableWidgetItem(template.subject[:50] + "..." if len(template.subject) > 50 else template.subject)
                self.template_table.setItem(row, 2, subject_item)

                # 内容类型
                self.template_table.setItem(row, 3, QTableWidgetItem(template.content_type))

                # 是否默认
                default_item = QTableWidgetItem("是" if template.is_default else "否")
                if template.is_default:
                    default_item.setBackground(QColor(144, 238, 144))
                self.template_table.setItem(row, 4, default_item)

                # 创建时间
                create_time = template.create_time.strftime("%Y-%m-%d %H:%M") if template.create_time else ""
                self.template_table.setItem(row, 5, QTableWidgetItem(create_time))

                # 存储模板对象
                self.template_table.item(row, 0).setData(Qt.UserRole, template)

            logger.info(f"加载了 {len(templates)} 个邮件模板")

        except Exception as e:
            logger.error(f"加载模板列表失败: {e}")
            QMessageBox.critical(self, "错误", f"加载模板列表失败: {e}")

    def on_selection_changed(self):
        """选择改变时的处理"""
        selected_rows = self.template_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.copy_btn.setEnabled(has_selection)

        if has_selection:
            row = selected_rows[0].row()
            template = self.template_table.item(row, 0).data(Qt.UserRole)
            if template:
                self.show_template_preview(template)
                self.template_selected.emit(template)
        else:
            self.preview_text.clear()

    def show_template_preview(self, template: EmailTemplate):
        """显示模板预览"""
        try:
            preview_html = f"""
            <h3>模板名称: {template.template_name}</h3>
            <p><strong>邮件主题:</strong> {template.subject}</p>
            <p><strong>内容类型:</strong> {template.content_type}</p>
            <p><strong>是否默认:</strong> {'是' if template.is_default else '否'}</p>
            <hr>
            <p><strong>邮件内容:</strong></p>
            <div style="border: 1px solid #ccc; padding: 10px; background: #f9f9f9;">
            {template.content.replace('\n', '<br>') if template.content_type == 'text/plain' else template.content}
            </div>
            """

            if template.variables:
                try:
                    variables = json.loads(template.variables)
                    var_list = "<br>".join([f"• {{{k}}}: {v}" for k, v in variables.items()])
                    preview_html += f"<hr><p><strong>可用变量:</strong></p><div>{var_list}</div>"
                except:
                    pass

            self.preview_text.setHtml(preview_html)

        except Exception as e:
            logger.error(f"显示模板预览失败: {e}")

    def add_template(self):
        """新建模板"""
        dialog = EmailTemplateDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                template = dialog.get_template_data()
                template_id = self.template_manager.add_template(template)

                QMessageBox.information(self, "成功", f"模板创建成功，ID: {template_id}")
                self.load_templates()

            except Exception as e:
                logger.error(f"创建模板失败: {e}")
                QMessageBox.critical(self, "错误", f"创建模板失败: {e}")

    def edit_template(self):
        """编辑模板"""
        selected_rows = self.template_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        template = self.template_table.item(row, 0).data(Qt.UserRole)

        if not template:
            return

        dialog = EmailTemplateDialog(self, template)
        if dialog.exec_() == QDialog.Accepted:
            try:
                updated_template = dialog.get_template_data()
                success = self.template_manager.update_template(updated_template)

                if success:
                    QMessageBox.information(self, "成功", "模板更新成功")
                    self.load_templates()
                else:
                    QMessageBox.warning(self, "警告", "模板更新失败")

            except Exception as e:
                logger.error(f"更新模板失败: {e}")
                QMessageBox.critical(self, "错误", f"更新模板失败: {e}")

    def delete_template(self):
        """删除模板"""
        selected_rows = self.template_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        template = self.template_table.item(row, 0).data(Qt.UserRole)

        if not template:
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除模板 '{template.template_name}' 吗？\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success = self.template_manager.delete_template(template.id)

                if success:
                    QMessageBox.information(self, "成功", "模板删除成功")
                    self.load_templates()
                else:
                    QMessageBox.warning(self, "警告", "模板删除失败")

            except Exception as e:
                logger.error(f"删除模板失败: {e}")
                QMessageBox.critical(self, "错误", f"删除模板失败: {e}")

    def copy_template(self):
        """复制模板"""
        selected_rows = self.template_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        template = self.template_table.item(row, 0).data(Qt.UserRole)

        if not template:
            return

        # 创建副本
        new_template = EmailTemplate()
        new_template.template_name = f"{template.template_name} - 副本"
        new_template.subject = template.subject
        new_template.content = template.content
        new_template.content_type = template.content_type
        new_template.variables = template.variables
        new_template.is_default = False  # 副本不设为默认

        dialog = EmailTemplateDialog(self, new_template)
        if dialog.exec_() == QDialog.Accepted:
            try:
                final_template = dialog.get_template_data()
                template_id = self.template_manager.add_template(final_template)

                QMessageBox.information(self, "成功", f"模板复制成功，ID: {template_id}")
                self.load_templates()

            except Exception as e:
                logger.error(f"复制模板失败: {e}")
                QMessageBox.critical(self, "错误", f"复制模板失败: {e}")

    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.template_table.itemAt(position) is None:
            return

        menu = QMenu(self)

        edit_action = QAction("编辑", self)
        edit_action.triggered.connect(self.edit_template)
        menu.addAction(edit_action)

        copy_action = QAction("复制", self)
        copy_action.triggered.connect(self.copy_template)
        menu.addAction(copy_action)

        menu.addSeparator()

        delete_action = QAction("删除", self)
        delete_action.triggered.connect(self.delete_template)
        menu.addAction(delete_action)

        menu.addSeparator()

        # 设为默认
        selected_rows = self.template_table.selectionModel().selectedRows()
        if selected_rows:
            row = selected_rows[0].row()
            template = self.template_table.item(row, 0).data(Qt.UserRole)
            if template and not template.is_default:
                set_default_action = QAction("设为默认模板", self)
                set_default_action.triggered.connect(lambda: self.set_default_template(template.id))
                menu.addAction(set_default_action)

        menu.exec_(self.template_table.mapToGlobal(position))

    def set_default_template(self, template_id: int):
        """设置默认模板"""
        try:
            success = self.template_manager.set_default_template(template_id)

            if success:
                QMessageBox.information(self, "成功", "默认模板设置成功")
                self.load_templates()
            else:
                QMessageBox.warning(self, "警告", "默认模板设置失败")

        except Exception as e:
            logger.error(f"设置默认模板失败: {e}")
            QMessageBox.critical(self, "错误", f"设置默认模板失败: {e}")

    def get_selected_template(self) -> Optional[EmailTemplate]:
        """获取当前选中的模板"""
        selected_rows = self.template_table.selectionModel().selectedRows()
        if not selected_rows:
            return None

        row = selected_rows[0].row()
        return self.template_table.item(row, 0).data(Qt.UserRole)
