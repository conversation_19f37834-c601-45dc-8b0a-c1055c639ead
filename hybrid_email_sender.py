#!/usr/bin/env python3
"""
混合邮件发送器
结合Cookie复用轻量化发送和SMTP发送的最佳解决方案
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.lightweight_email_sender import LightweightEmailSender, EmailMessage
from sina_smtp_sender import SinaSMTPSender
from src.core.proxy_manager import ProxyManager
from src.models.account import Account
from src.utils.logger import setup_logger
from typing import Dict, Any

# 设置日志
logger = setup_logger("INFO")

class HybridEmailSender:
    """混合邮件发送器"""
    
    def __init__(self):
        # 初始化组件
        self.proxy_manager = ProxyManager()
        self.lightweight_sender = LightweightEmailSender(self.proxy_manager)
        self.smtp_sender = SinaSMTPSender()
        
        # 发送策略配置
        self.strategy_config = {
            'prefer_lightweight': True,  # 优先使用轻量化发送
            'fallback_to_smtp': True,    # 失败时回退到SMTP
            'max_retries': 2,            # 最大重试次数
            'retry_delay': 1             # 重试延迟（秒）
        }
    
    def send_email_hybrid(self, account: Account, to_email: str, subject: str, content: str, content_type: str = "text/plain") -> Dict[str, Any]:
        """混合发送邮件"""
        logger.info(f"🚀 混合发送邮件: {account.email} -> {to_email}")
        logger.info(f"📝 主题: {subject}")
        
        # 创建邮件消息对象
        message = EmailMessage(
            to_email=to_email,
            subject=subject,
            content=content,
            content_type=content_type
        )
        
        # 发送策略1: 优先使用轻量化发送
        if self.strategy_config['prefer_lightweight']:
            logger.info("🎯 策略1: 尝试轻量化发送...")
            lightweight_result = self._try_lightweight_send(account, message)
            
            if lightweight_result['success']:
                logger.info("✅ 轻量化发送成功")
                return {
                    'success': True,
                    'method': 'lightweight',
                    'message': '轻量化发送成功',
                    'details': lightweight_result
                }
            else:
                logger.warning(f"⚠️ 轻量化发送失败: {lightweight_result['message']}")
        
        # 发送策略2: 回退到SMTP发送
        if self.strategy_config['fallback_to_smtp']:
            logger.info("🎯 策略2: 回退到SMTP发送...")
            smtp_result = self._try_smtp_send(account, to_email, subject, content, content_type)
            
            if smtp_result['success']:
                logger.info("✅ SMTP发送成功")
                return {
                    'success': True,
                    'method': 'smtp',
                    'message': 'SMTP发送成功',
                    'details': smtp_result
                }
            else:
                logger.error(f"❌ SMTP发送失败: {smtp_result['message']}")
        
        # 所有策略都失败
        logger.error("❌ 所有发送策略都失败")
        return {
            'success': False,
            'method': 'none',
            'message': '所有发送策略都失败',
            'details': {
                'lightweight_attempted': self.strategy_config['prefer_lightweight'],
                'smtp_attempted': self.strategy_config['fallback_to_smtp']
            }
        }
    
    def _try_lightweight_send(self, account: Account, message: EmailMessage) -> Dict[str, Any]:
        """尝试轻量化发送"""
        try:
            for attempt in range(self.strategy_config['max_retries']):
                logger.info(f"🔄 轻量化发送尝试 {attempt + 1}/{self.strategy_config['max_retries']}")
                
                result = self.lightweight_sender.send_email_lightweight(account, message)
                
                if result.get('success'):
                    return result
                
                if attempt < self.strategy_config['max_retries'] - 1:
                    import time
                    time.sleep(self.strategy_config['retry_delay'])
            
            return {'success': False, 'message': '轻量化发送重试次数用尽'}
            
        except Exception as e:
            logger.error(f"❌ 轻量化发送异常: {e}")
            return {'success': False, 'message': f'轻量化发送异常: {e}'}
    
    def _try_smtp_send(self, account: Account, to_email: str, subject: str, content: str, content_type: str) -> Dict[str, Any]:
        """尝试SMTP发送"""
        try:
            for attempt in range(self.strategy_config['max_retries']):
                logger.info(f"🔄 SMTP发送尝试 {attempt + 1}/{self.strategy_config['max_retries']}")
                
                result = self.smtp_sender.send_email_smtp(account, to_email, subject, content, content_type)
                
                if result.get('success'):
                    return result
                
                if attempt < self.strategy_config['max_retries'] - 1:
                    import time
                    time.sleep(self.strategy_config['retry_delay'])
            
            return {'success': False, 'message': 'SMTP发送重试次数用尽'}
            
        except Exception as e:
            logger.error(f"❌ SMTP发送异常: {e}")
            return {'success': False, 'message': f'SMTP发送异常: {e}'}
    
    def test_all_methods(self, account: Account, test_email: str = "<EMAIL>") -> Dict[str, Any]:
        """测试所有发送方法"""
        logger.info("🧪 测试所有发送方法...")
        
        results = {}
        
        # 测试轻量化发送
        logger.info("🧪 测试轻量化发送...")
        message = EmailMessage(
            to_email=test_email,
            subject="轻量化发送测试",
            content="这是轻量化发送的测试邮件",
            content_type="text/plain"
        )
        
        lightweight_result = self._try_lightweight_send(account, message)
        results['lightweight'] = lightweight_result
        
        # 测试SMTP发送
        logger.info("🧪 测试SMTP发送...")
        smtp_result = self._try_smtp_send(
            account, 
            test_email, 
            "SMTP发送测试", 
            "这是SMTP发送的测试邮件", 
            "text/plain"
        )
        results['smtp'] = smtp_result
        
        # 测试混合发送
        logger.info("🧪 测试混合发送...")
        hybrid_result = self.send_email_hybrid(
            account,
            test_email,
            "混合发送测试",
            "这是混合发送策略的测试邮件"
        )
        results['hybrid'] = hybrid_result
        
        return results
    
    def get_best_strategy(self, account: Account) -> str:
        """获取最佳发送策略"""
        logger.info("🔍 分析最佳发送策略...")
        
        # 测试轻量化发送可用性
        try:
            session = self.lightweight_sender.create_lightweight_session(account)
            if session:
                logger.info("✅ 轻量化发送可用")
                return "lightweight_preferred"
        except:
            pass
        
        # 测试SMTP发送可用性
        try:
            smtp = self.smtp_sender.get_smtp_connection(account)
            if smtp:
                logger.info("✅ SMTP发送可用")
                return "smtp_preferred"
        except:
            pass
        
        logger.warning("⚠️ 所有发送方法都不可用")
        return "none_available"
    
    def cleanup(self):
        """清理资源"""
        try:
            self.lightweight_sender.cleanup_sessions()
            self.smtp_sender.close_connections()
            logger.info("✅ 混合发送器资源清理完成")
        except Exception as e:
            logger.error(f"❌ 资源清理失败: {e}")

def test_hybrid_sender():
    """测试混合发送器"""
    print("🧪 混合邮件发送器测试")
    print("="*60)
    
    # 创建测试账号
    account = Account(
        email="<EMAIL>",
        password="your_password_here",  # 需要设置真实密码
        status="active"
    )
    
    # 创建混合发送器
    sender = HybridEmailSender()
    
    try:
        # 分析最佳策略
        print("🔍 分析最佳发送策略...")
        best_strategy = sender.get_best_strategy(account)
        print(f"最佳策略: {best_strategy}")
        print()
        
        # 测试所有方法
        print("🧪 测试所有发送方法...")
        test_results = sender.test_all_methods(account)
        
        print("\n📊 测试结果:")
        print("-" * 40)
        
        for method, result in test_results.items():
            status = "✅ 成功" if result.get('success') else "❌ 失败"
            message = result.get('message', '无消息')
            print(f"{method.upper()}: {status}")
            print(f"  消息: {message}")
        
        # 推荐使用方案
        print("\n💡 推荐使用方案:")
        if test_results['hybrid']['success']:
            print("✅ 混合发送策略 - 自动选择最佳方法")
        elif test_results['lightweight']['success']:
            print("✅ 轻量化发送 - Cookie复用，速度快")
        elif test_results['smtp']['success']:
            print("✅ SMTP发送 - 传统协议，稳定可靠")
        else:
            print("❌ 所有方法都不可用，请检查账号配置")
    
    finally:
        # 清理资源
        sender.cleanup()

if __name__ == "__main__":
    print("⚠️ 注意：运行此测试需要：")
    print("1. 有效的新浪邮箱账号和密码")
    print("2. 已通过隐藏式登录获取的Cookie")
    print("3. 正确的SMTP配置")
    print()
    
    # 取消注释下面的行来运行测试
    # test_hybrid_sender()
