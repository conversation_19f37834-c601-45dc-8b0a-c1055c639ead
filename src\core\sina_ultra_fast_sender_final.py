#!/usr/bin/env python3
"""
新浪邮箱超高速发送器 - 最终完善版
基于真实测试结果的完整实现，支持cookies登录后的超高速发送
"""

import time
from typing import Optional, Dict, Any, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class SinaUltraFastSenderFinal:
    """新浪邮箱超高速发送器 - 最终完善版
    
    完整功能:
    1. 智能登录状态检测
    2. 多策略写信按钮查找
    3. 多种发送方式支持
    4. 完整的错误处理和重试机制
    """
    
    def __init__(self, driver: webdriver.Chrome):
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        self.short_wait = WebDriverWait(driver, 3)
        self.is_ready = False
        self.send_count = 0
        self.success_count = 0
        
        # 完整的选择器配置 - 基于成功测试经验优化
        self.selectors = {
            # 写信按钮选择器 - 优先使用成功验证的选择器
            'write_buttons': [
                # 最高优先级 - 基于调试工具发现的真实选择器
                "//a[@title='写信']",  # 调试发现：真实的写信按钮有title='写信'
                "//a[contains(text(), '写信') and @title='写信']",  # 双重验证
                "//a[normalize-space(text())='写信']",  # 处理换行符情况

                # 高优先级 - 基于成功测试验证
                "//a[contains(text(), '写信')]",  # 测试中成功找到的选择器
                "//a[text()='写信']",  # 精确匹配

                # 中优先级 - 常见变体
                "//a[contains(text(), '写邮件')]",
                "//button[contains(text(), '写信')]",
                "//input[@value='写信']",

                # 低优先级 - 属性匹配
                "//a[@title='写邮件']",
                "//span[contains(text(), '写信')]//parent::a",
                "//div[contains(text(), '写信')]//a",

                # 更低优先级 - 链接和类匹配
                "//a[contains(@href, 'compose')]",
                "//a[contains(@href, 'write')]",
                "//a[contains(@href, 'writer')]",  # 基于URL中的action=writer
                "//a[contains(@class, 'write')]",
                "//a[contains(@class, 'compose')]",
                "//a[contains(@onclick, 'compose')]",
                "//a[contains(@onclick, 'writer')]",

                # 兜底选择器
                "//*[contains(text(), '写信')]",
                "//*[text()='写信']",
            ],
            
            # 收件人输入框 - 基于调试工具发现的真实选择器
            'to_inputs': [
                # 基于调试发现：第24个input，无name/id但可见可用
                "//input[@type='text'][1]",  # 第一个文本输入框
                "//input[@type='text' and not(@name) and not(@id) and not(@placeholder)]",

                # 通用备选
                "//input[@placeholder='收件人：']",
                "//input[contains(@placeholder, '收件人')]",
                "//input[@name='to']",
                "//input[@id='to']",
                "//input[contains(@name, 'to')]",
                "//input[contains(@name, 'mail')]",
                "//textarea[contains(@name, 'to')]",
                "//input[contains(@id, 'to')]",
                "//input[contains(@id, 'mail')]",
                "//input[contains(@class, 'to')]",
                "//input[contains(@class, 'recipient')]",

                # 更宽泛的搜索
                "//div[contains(@class, 'compose')]//input[@type='text']",
                "//form//input[@type='text']",
            ],

            # 主题输入框 - 基于调试工具发现的真实选择器
            'subject_inputs': [
                # 基于调试发现：Input 29, name: subj, class: input inp_base
                "//input[@name='subj']",  # 精确匹配！
                "//input[@class='input inp_base']",
                "//input[contains(@class, 'inp_base')]",

                # 通用备选
                "//input[@placeholder='主　题：']",
                "//input[contains(@placeholder, '主题')]",
                "//input[@name='subject']",
                "//input[@name='title']",
                "//input[contains(@name, 'subject')]",
                "//input[contains(@name, 'title')]",
                "//input[contains(@id, 'subject')]",
                "//input[contains(@id, 'title')]",
                "//input[contains(@class, 'subject')]",
                "//input[contains(@class, 'title')]",

                # 位置匹配
                "//input[@type='text'][2]",  # 第二个文本输入框
                "//form//input[@type='text'][2]",

                # 🎯 增强选择器：基于页面结构
                "//td[contains(text(), '主题') or contains(text(), 'Subject')]//following-sibling::td//input",
                "//label[contains(text(), '主题')]//following-sibling::input",
                "//span[contains(text(), '主题')]//following-sibling::input",

                # 🎯 通过索引查找（收件人后面的输入框）
                "(//input[@type='text'])[2]",  # 第二个文本输入框
                "(//input[@type='text'])[3]",  # 第三个文本输入框（以防万一）

                # 🎯 兜底选择器
                "//input[@type='text'][not(@name='to') and not(@name='mailto')]",  # 排除收件人字段
            ],

            # 邮件内容区域 - 基于调试工具发现的真实选择器
            'content_areas': [
                # 基于调试发现：Iframe 5, class: iframe
                "//iframe[@class='iframe']",  # 精确匹配！
                "//iframe[contains(@class, 'iframe')]",

                # 通用iframe选择器
                "//iframe[@name='content']",
                "//iframe[@id='content']",
                "//iframe[contains(@name, 'editor')]",
                "//iframe[contains(@id, 'editor')]",
                "//iframe[contains(@src, 'editor')]",
                "//iframe[contains(@class, 'editor')]",

                # 富文本编辑器
                "//div[@contenteditable='true']",
                "//div[@contenteditable='']",

                # 通过位置查找
                "//div[contains(@class, 'compose')]//div[@contenteditable='true']",
                "//div[contains(@class, 'mail')]//div[@contenteditable='true']",
                "//form//div[@contenteditable='true']",

                # 🎯 增强iframe选择器
                "//iframe[contains(@src, 'compose')]",
                "//iframe[contains(@src, 'mail')]",
                "//iframe[contains(@name, 'compose')]",
                "//iframe[contains(@id, 'compose')]",

                # 🎯 通过索引查找iframe
                "(//iframe)[1]",  # 第一个iframe
                "(//iframe)[2]",  # 第二个iframe
                "//iframe",  # 任何iframe

                # 🎯 textarea备用选择器
                "//textarea[@name='content']",
                "//textarea[contains(@name, 'content')]",
                "//textarea[contains(@class, 'content')]",
                "//textarea",  # 任何textarea

                # textarea备选
                "//textarea[@name='content']",
                "//textarea[contains(@name, 'content')]",
                "//textarea[contains(@name, 'body')]",
                "//textarea[contains(@name, 'message')]",
                "//textarea[contains(@class, 'content')]",

                # 兜底选择器
                "//iframe",  # 任何iframe
                "//*[@contenteditable='true']",
                "//*[@contenteditable='']",
            ],

            # 发送按钮 - 基于真实HTML结构修正
            'send_buttons': [
                # 🎯 最高优先级：基于真实HTML结构的精确选择器
                "//a[.//i[contains(@class, 'icon_send')] and .//i[contains(@class, 'mailPubText') and text()='发送']]",
                "//a[.//i[contains(@class, 'icon_send')]]",  # 包含发送图标的链接
                "//a[.//i[text()='发送']]",  # 包含发送文字的链接
                "//a[contains(@href, 'javascript:void(0)') and .//i[text()='发送']]",

                # 🎯 高优先级：基于class和结构的选择器
                "//i[contains(@class, 'mailPubText') and text()='发送']/parent::a",
                "//i[contains(@class, 'icon_send')]/parent::a",
                "//a[contains(@class, 'mailPub') and .//text()[contains(., '发送')]]",

                # 🎯 中优先级：通用发送按钮选择器
                "//a[contains(text(), '发送')]",
                "//a[.//text()[contains(., '发送')]]",
                "//button[contains(text(), '发送')]",
                "//input[@value='发送']",
                "//input[@type='submit'][@value='发送']",

                # 🎯 低优先级：位置相关选择器
                "//div[contains(@class, 'toolbar')]//a[contains(text(), '发送')]",
                "//form//a[contains(text(), '发送')]",
                "//form//button[contains(text(), '发送')]",
                "//form//input[@type='submit']",

                # 🎯 兜底选择器
                "//*[contains(text(), '发送') and (self::a or self::button or self::input)]",
                "//input[@type='submit']",  # 任何提交按钮
            ]
        }
    
    def check_login_status(self) -> tuple[bool, str]:
        """智能检查登录状态"""
        try:
            current_url = self.driver.current_url.lower()
            page_title = self.driver.title
            
            logger.info(f"检查登录状态 - URL: {current_url}")
            logger.info(f"检查登录状态 - 标题: {page_title}")
            
            # 优先检查URL和标题
            if ('mail.sina.com.cn' in current_url and 
                ('index.php' in current_url or 'classic' in current_url) and
                ('新浪邮箱' in page_title or 'sina' in page_title.lower())):
                return True, "已进入新浪邮箱主界面"
            
            # 检查是否在登录页面
            if 'login' in current_url or 'passport' in current_url:
                return False, "仍在登录页面"
            
            # 检查页面内容
            page_source = self.driver.page_source
            mailbox_indicators = ['收件箱', 'inbox', '写信', 'compose', '发件箱', 'sent']
            
            if any(indicator in page_source for indicator in mailbox_indicators):
                return True, "检测到邮箱功能界面"
            
            # 如果在新浪邮箱域名下
            if 'mail.sina.com.cn' in current_url:
                return True, "在邮箱域名下，可能已登录"
            
            return False, "登录状态不明确"
            
        except Exception as e:
            logger.error(f"检查登录状态失败: {e}")
            return False, f"检查失败: {e}"
    
    def find_element_by_selectors(self, selectors: List[str], timeout: int = 2) -> Optional[Any]:
        """通过多个选择器查找元素"""
        for selector in selectors:
            try:
                element = WebDriverWait(self.driver, timeout).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                if element and element.is_displayed():
                    logger.debug(f"找到元素: {selector}")
                    return element
            except TimeoutException:
                continue
            except Exception as e:
                logger.debug(f"选择器失败 {selector}: {e}")
                continue
        return None
    
    def find_write_button(self) -> Optional[Any]:
        """智能查找写信按钮"""
        try:
            logger.info("查找写信按钮...")
            
            # 极速等待页面加载
            time.sleep(1)  # 减少等待时间

            # 使用预定义选择器 - 极速查找
            element = self.find_element_by_selectors(self.selectors['write_buttons'], timeout=2)
            if element:
                logger.info(f"找到写信按钮: {element.text}")
                return element
            
            # 如果预定义选择器失败，尝试智能搜索
            logger.info("预定义选择器失败，尝试智能搜索...")
            
            all_elements = (
                self.driver.find_elements(By.TAG_NAME, "a") +
                self.driver.find_elements(By.TAG_NAME, "button") +
                self.driver.find_elements(By.XPATH, "//input[@type='button' or @type='submit']")
            )
            
            for element in all_elements:
                try:
                    text = element.text.strip()
                    title = element.get_attribute('title') or ''
                    onclick = element.get_attribute('onclick') or ''
                    href = element.get_attribute('href') or ''
                    
                    combined_text = (text + title + onclick + href).lower()
                    
                    if any(keyword in combined_text for keyword in ['写信', '写邮件', 'compose', 'write']):
                        if element.is_displayed() and element.is_enabled():
                            logger.info(f"智能搜索找到写信按钮: {text}")
                            return element
                except:
                    continue
            
            logger.warning("未找到写信按钮")
            return None
            
        except Exception as e:
            logger.error(f"查找写信按钮失败: {e}")
            return None
    
    def prepare_compose_page(self) -> bool:
        """准备写邮件页面 - 修正版：必须点击写信按钮显示右侧界面"""
        try:
            logger.info("🔧 准备写邮件页面 (修正版)...")

            # 检查登录状态
            login_success, login_message = self.check_login_status()
            if not login_success:
                logger.error(f"登录状态检查失败: {login_message}")
                return False

            logger.info(f"登录状态确认: {login_message}")

            # 重要修正：不要假设已经在写邮件界面，必须检查真实输入框
            logger.info("🔍 检查是否已有真实的写邮件输入框...")
            if self.is_compose_interface_visible():
                logger.info("✅ 已检测到真实的写邮件界面（右侧已显示）")
                self.is_ready = True
                return True

            # 如果没有真实输入框，必须点击写信按钮
            logger.info("❌ 未检测到真实的写邮件输入框")
            logger.info("🖱️ 点击'写信'按钮显示右侧发件界面...")

            # 超极速查找并点击写信按钮
            if not hasattr(self, 'ultra_compose_manager'):
                from src.core.ultra_speed_cookie_manager import UltraSpeedComposeManager
                self.ultra_compose_manager = UltraSpeedComposeManager(self.driver)

            # 使用超极速写信按钮管理器
            success = self.ultra_compose_manager.ultra_speed_find_and_click_compose()

            if success:
                self.is_ready = True
                logger.info("⚡ 超极速写信按钮点击成功")
                return True
            else:
                logger.error("❌ 超极速写信按钮点击失败")

                # 备用方案：使用原始方法
                logger.info("🔄 尝试备用写信按钮点击方案...")
                write_button = self.find_write_button()
                if write_button:
                    # 记录点击前的URL
                    before_url = self.driver.current_url
                    logger.info(f"✅ 找到写信按钮，立即点击...")
                    logger.info(f"点击前URL: {before_url}")

                    write_button.click()

                    # 关键修正：必须验证URL是否真的变化了
                    logger.info("⚡ 等待URL变化，验证写信按钮是否真的被点击...")

                    # 极速等待URL变化 - 基于成功经验优化
                    for i in range(6):  # 减少到最多等待6秒
                        time.sleep(0.5)  # 减少每次等待时间到0.5秒
                        after_url = self.driver.current_url

                        if 'action=writer' in after_url:
                            logger.info(f"✅ 写信按钮点击成功！URL已变化")
                            logger.info(f"点击后URL: {after_url}")
                            logger.info("✅ 已进入写邮件界面")
                            self.is_ready = True
                            return True
                        elif after_url != before_url:
                            logger.info(f"⚠️ URL发生变化但不是预期的写邮件界面")
                            logger.info(f"点击后URL: {after_url}")

                    # 如果URL没有变化，说明点击失败
                    final_url = self.driver.current_url
                    logger.error("❌ 写信按钮点击失败！URL没有变化")
                    logger.error(f"点击前URL: {before_url}")
                    logger.error(f"点击后URL: {final_url}")
                    logger.error("❌ 未能进入写邮件界面")
                    return False
                else:
                    logger.error("❌ 未找到写信按钮")
                    logger.error("❌ 无法显示右侧发件界面")
                    return False

        except Exception as e:
            logger.error(f"❌ 准备写邮件页面失败: {e}")
            return False
    
    def is_compose_interface_visible(self) -> bool:
        """检查写邮件界面是否可见 - 修正版：必须基于URL变化判断"""
        try:
            # 关键修正：必须基于URL变化判断，不能仅凭输入框存在
            current_url = self.driver.current_url
            logger.info(f"🔍 检查写邮件界面 - 当前URL: {current_url}")

            # 正确的判断逻辑：URL必须包含action=writer
            url_indicates_compose = 'action=writer' in current_url

            if url_indicates_compose:
                logger.info("✅ URL确认：已进入写邮件界面 (action=writer)")
                return True
            else:
                logger.warning("❌ URL确认：未进入写邮件界面")
                logger.info(f"   期望包含: action=writer")
                logger.info(f"   实际URL: {current_url}")
                logger.info("   必须点击'写信'按钮来进入写邮件界面")
                return False

        except Exception as e:
            logger.error(f"❌ 检查写邮件界面失败: {e}")
            return False
    
    def send_email_ultra_fast(self, to_email: str, subject: str, content: str) -> bool:
        """超高速发送邮件 - 简化版：点击写信后直接发送"""
        try:
            if not self.is_ready:
                logger.info("⚡ 页面未准备就绪，快速准备...")
                if not self.prepare_compose_page():
                    logger.error("❌ 页面准备失败")
                    return False

            logger.info(f"🚀 开始超高速发送邮件到: {to_email}")
            logger.info("📝 正确流程：填写收件人 → 填写主题 → 填写内容 → 点击发送")
            start_time = time.time()

            # 策略1: JavaScript超高速填写
            logger.info("🚀 尝试JavaScript超高速发送...")
            if self._send_with_javascript(to_email, subject, content):
                elapsed = time.time() - start_time
                logger.info(f"✅ JavaScript发送成功 ({elapsed:.2f}秒)")
                self.success_count += 1
                return True

            # 策略2: 直接元素操作
            logger.info("🔧 JavaScript失败，尝试元素操作发送...")
            if self._send_with_elements(to_email, subject, content):
                elapsed = time.time() - start_time
                logger.info(f"✅ 元素操作发送成功 ({elapsed:.2f}秒)")
                self.success_count += 1
                return True

            elapsed = time.time() - start_time
            logger.error(f"❌ 所有发送策略都失败了 ({elapsed:.2f}秒)")
            return False

        except Exception as e:
            logger.error(f"❌ 超高速发送异常: {e}")
            return False
        finally:
            self.send_count += 1
    
    def _send_with_javascript(self, to_email: str, subject: str, content: str) -> bool:
        """使用JavaScript发送"""
        try:
            logger.info("尝试JavaScript发送...")
            
            # 转义特殊字符
            safe_email = to_email.replace("'", "\\'").replace('"', '\\"')
            safe_subject = subject.replace("'", "\\'").replace('"', '\\"')
            safe_content = content.replace("'", "\\'").replace('"', '\\"').replace('\n', '\\n')
            
            js_code = f"""
            try {{
                console.log('🚀 开始JavaScript超高速填写和发送...');
                console.log('📝 步骤: 1.填写收件人 → 2.填写主题 → 3.填写内容 → 4.点击发送');
                var startTime = Date.now();

                // 步骤1: 填写收件人 - 基于成功经验的精确选择器
                var toField = document.querySelector('input[type="text"]') ||  // 测试成功的选择器！
                             document.querySelector('input[name="to"]') ||
                             document.querySelector('input[name="mailto"]') ||
                             document.querySelector('input[name*="to"]') ||
                             document.querySelector('input[placeholder*="收件人"]') ||
                             document.querySelector('textarea[name*="to"]') ||
                             document.querySelectorAll('input[type="text"]')[0];  // 第一个文本输入框

                if (toField && toField.offsetParent !== null) {{
                    toField.focus();
                    toField.value = '{safe_email}';
                    toField.dispatchEvent(new Event('input', {{bubbles: true}}));
                    toField.dispatchEvent(new Event('change', {{bubbles: true}}));
                    toField.dispatchEvent(new Event('blur', {{bubbles: true}}));
                    console.log('✅ 收件人已填写:', '{safe_email}');
                }} else {{
                    console.log('❌ 未找到收件人字段');
                    return false;
                }}

                // 步骤2: 填写主题 - 基于成功经验的精确选择器
                var subjectField = document.querySelector('input[name="subj"]') ||  // 测试成功的选择器！
                                  document.querySelector('input[class*="inp_base"]') ||
                                  document.querySelector('input[name="subject"]') ||
                                  document.querySelector('input[name="title"]') ||
                                  document.querySelector('input[name*="subject"]') ||
                                  document.querySelector('input[placeholder*="主题"]') ||
                                  document.querySelectorAll('input[type="text"]')[1];  // 第二个文本输入框

                if (subjectField && subjectField.offsetParent !== null) {{
                    subjectField.focus();
                    subjectField.value = '{safe_subject}';
                    subjectField.dispatchEvent(new Event('input', {{bubbles: true}}));
                    subjectField.dispatchEvent(new Event('change', {{bubbles: true}}));
                    console.log('✅ 主题已填写:', '{safe_subject}');
                }}

                // 步骤3: 填写邮件内容 - 并行尝试多种方式
                var contentFilled = false;

                // 方式1: iframe编辑器 - 基于真实选择器优化
                var iframe = document.querySelector('iframe[class="iframe"]') ||  // 基于调试发现的真实选择器！
                           document.querySelector('iframe.iframe') ||  // CSS类选择器
                           document.querySelector('iframe[name="content"]') ||
                           document.querySelector('iframe[id="content"]') ||
                           document.querySelector('iframe[name*="editor"]') ||
                           document.querySelector('iframe[id*="editor"]') ||
                           document.querySelector('iframe');  // 任何iframe

                if (iframe && iframe.offsetParent !== null && !contentFilled) {{
                    try {{
                        var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        var body = iframeDoc.body || iframeDoc.querySelector('body');
                        if (body) {{
                            body.innerHTML = '{safe_content}';
                            body.dispatchEvent(new Event('input', {{bubbles: true}}));
                            contentFilled = true;
                            console.log('✅ iframe内容已填写');
                        }}
                    }} catch(e) {{
                        console.log('iframe访问失败:', e);
                    }}
                }}

                // 方式2: 富文本编辑器
                if (!contentFilled) {{
                    var contentDiv = document.querySelector('div[contenteditable="true"]') ||
                                   document.querySelector('div[contenteditable=""]');
                    if (contentDiv && contentDiv.offsetParent !== null) {{
                        contentDiv.focus();
                        contentDiv.innerHTML = '{safe_content}';
                        contentDiv.dispatchEvent(new Event('input', {{bubbles: true}}));
                        contentFilled = true;
                        console.log('✅ 富文本编辑器内容已填写');
                    }}
                }}

                // 方式3: textarea
                if (!contentFilled) {{
                    var textarea = document.querySelector('textarea[name="content"]') ||
                                 document.querySelector('textarea[name*="content"]') ||
                                 document.querySelector('textarea[name*="body"]');
                    if (textarea && textarea.offsetParent !== null) {{
                        textarea.focus();
                        textarea.value = '{safe_content}';
                        textarea.dispatchEvent(new Event('input', {{bubbles: true}}));
                        contentFilled = true;
                        console.log('✅ textarea内容已填写');
                    }}
                }}

                if (!contentFilled) {{
                    console.log('⚠️ 内容填写可能失败，但继续尝试发送');
                }}

                // 步骤4: 点击发送按钮 - 基于成功经验的精确选择器
                var sendButton = document.querySelector('input[type="submit"][value="发送"]') ||
                               document.querySelector('input[value="发送"]') ||
                               document.querySelector('input[type="submit"][value*="发送"]') ||
                               document.querySelector('button[text()="发送"]') ||
                               document.querySelector('button:contains("发送")') ||
                               document.querySelector('button[contains(text(), "发送")]') ||
                               document.querySelector('input[type="submit"]') ||  // 任何提交按钮
                               document.querySelector('button[type="submit"]');

                if (sendButton && sendButton.offsetParent !== null) {{
                    console.log('✅ 找到发送按钮，立即发送...');
                    sendButton.focus();
                    sendButton.click();

                    var elapsed = Date.now() - startTime;
                    console.log('🚀 JavaScript发送完成，耗时:', elapsed + 'ms');
                    return true;
                }} else {{
                    console.log('❌ 未找到发送按钮');
                    return false;
                }}

            }} catch(e) {{
                console.error('❌ JavaScript发送失败:', e);
                return false;
            }}
            """
            
            result = self.driver.execute_script(js_code)
            
            if result:
                time.sleep(3)  # 等待发送完成
                return self._check_send_success()
            
            return False
            
        except Exception as e:
            logger.error(f"JavaScript发送失败: {e}")
            return False
    
    def _send_with_elements(self, to_email: str, subject: str, content: str) -> bool:
        """使用元素操作发送 - 优化版"""
        try:
            logger.info("🔧 尝试元素操作发送...")
            start_time = time.time()

            # 极速收件人填写 - 修复点击被拦截问题
            to_field = self.find_element_by_selectors(self.selectors['to_inputs'], timeout=1)
            if to_field:
                try:
                    # 方法1: 滚动到元素并确保可见
                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'instant', block: 'center'});", to_field)
                    time.sleep(0.2)

                    # 方法2: 尝试直接点击
                    to_field.click()
                    to_field.clear()
                    to_field.send_keys(to_email)

                except Exception as click_error:
                    logger.warning(f"⚠️ 直接点击失败，尝试JavaScript点击: {click_error}")
                    # 方法3: 使用JavaScript点击和填写
                    self.driver.execute_script("""
                        arguments[0].scrollIntoView({behavior: 'instant', block: 'center'});
                        arguments[0].focus();
                        arguments[0].value = arguments[1];
                        arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                        arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                    """, to_field, to_email)

                # 验证填写结果
                current_value = to_field.get_attribute('value')
                if to_email in current_value:
                    logger.info(f"✅ 收件人已填写: {to_email}")
                else:
                    logger.warning(f"⚠️ 收件人填写可能不完整: {current_value}")
            else:
                logger.error("❌ 未找到收件人字段")
                return False

            # 极速主题填写 - 增强查找和填写机制
            subject_field = self.find_element_by_selectors(self.selectors['subject_inputs'], timeout=3)  # 增加超时时间
            if subject_field:
                try:
                    # 滚动到元素并确保可见
                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'instant', block: 'center'});", subject_field)
                    time.sleep(0.2)  # 增加等待时间

                    subject_field.click()
                    subject_field.clear()
                    subject_field.send_keys(subject)

                    # 验证填写结果
                    current_value = subject_field.get_attribute('value')
                    if subject in current_value:
                        logger.info(f"✅ 主题已填写: {subject}")
                    else:
                        logger.warning(f"⚠️ 主题填写可能不完整: {current_value}")

                except Exception as click_error:
                    logger.warning(f"⚠️ 主题直接点击失败，尝试JavaScript填写: {click_error}")
                    # 使用JavaScript填写
                    self.driver.execute_script("""
                        arguments[0].scrollIntoView({behavior: 'instant', block: 'center'});
                        arguments[0].focus();
                        arguments[0].value = arguments[1];
                        arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                        arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                    """, subject_field, subject)

                    # 再次验证
                    current_value = subject_field.get_attribute('value')
                    if subject in current_value:
                        logger.info(f"✅ 主题JavaScript填写成功: {subject}")
                    else:
                        logger.warning(f"⚠️ 主题JavaScript填写失败: {current_value}")
            else:
                logger.error("❌ 未找到主题字段！尝试所有选择器都失败了")
                # 调试信息：显示页面上所有的input元素
                try:
                    all_inputs = self.driver.find_elements(By.XPATH, "//input[@type='text']")
                    logger.info(f"🔍 页面上共有 {len(all_inputs)} 个文本输入框")
                    for i, inp in enumerate(all_inputs[:5]):  # 只显示前5个
                        name = inp.get_attribute('name') or 'None'
                        class_attr = inp.get_attribute('class') or 'None'
                        placeholder = inp.get_attribute('placeholder') or 'None'
                        logger.info(f"  输入框{i+1}: name='{name}', class='{class_attr}', placeholder='{placeholder}'")
                except:
                    pass

            # 极速内容填写
            content_filled = self._fill_content_optimized(content)
            if content_filled:
                logger.info("✅ 内容填写成功")
            else:
                logger.warning("⚠️ 内容填写可能失败，但继续尝试发送")

            # 极速发送操作 - 修复点击被拦截问题
            send_button = self.find_element_by_selectors(self.selectors['send_buttons'], timeout=1)
            if send_button:
                try:
                    # 确保按钮可见和可点击
                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'instant', block: 'center'});", send_button)
                    time.sleep(0.3)  # 稍微增加等待时间确保滚动完成

                    # 尝试直接点击
                    send_button.click()
                    elapsed = time.time() - start_time
                    logger.info(f"✅ 发送按钮已点击 (耗时: {elapsed:.2f}秒)")

                except Exception as click_error:
                    logger.warning(f"⚠️ 发送按钮直接点击失败，尝试JavaScript点击: {click_error}")
                    # 使用JavaScript点击
                    self.driver.execute_script("""
                        arguments[0].scrollIntoView({behavior: 'instant', block: 'center'});
                        setTimeout(function() {
                            arguments[0].focus();
                            arguments[0].click();
                        }, 100);
                    """, send_button)

                    elapsed = time.time() - start_time
                    logger.info(f"✅ 发送按钮JavaScript点击完成 (耗时: {elapsed:.2f}秒)")

                # 减少等待时间，使用智能检查
                return self._check_send_success_fast()
            else:
                logger.error("❌ 未找到发送按钮")
                return False

        except Exception as e:
            logger.error(f"❌ 元素操作发送失败: {e}")
            return False
    
    def _fill_content_optimized(self, content: str) -> bool:
        """优化的邮件内容填写"""
        try:
            # 策略1: iframe编辑器 - 基于调试发现的真实选择器优化
            iframe_selectors = [
                # 最高优先级：基于调试发现的真实选择器
                "//iframe[@class='iframe']",  # 调试发现的精确选择器！
                "//iframe[contains(@class, 'iframe')]",

                # 高优先级：通用选择器
                "//iframe[@name='content']",
                "//iframe[@id='content']",
                "//iframe[contains(@name, 'editor')]",
                "//iframe[contains(@id, 'editor')]",
                "//iframe[contains(@src, 'editor')]",

                # 兜底选择器：任何可见的iframe
                "//iframe"
            ]

            for selector in iframe_selectors:
                try:
                    iframe = self.driver.find_element(By.XPATH, selector)
                    if iframe and iframe.is_displayed():
                        logger.info(f"🎯 尝试iframe选择器: {selector}")
                        self.driver.switch_to.frame(iframe)

                        # 多种方式尝试找到编辑区域
                        edit_targets = [
                            ("body", By.TAG_NAME, "body"),
                            ("div[contenteditable]", By.XPATH, "//div[@contenteditable='true']"),
                            ("div[contenteditable]", By.XPATH, "//div[@contenteditable='']"),
                            ("任何div", By.TAG_NAME, "div"),
                            ("html", By.TAG_NAME, "html")
                        ]

                        for target_name, by_type, target_selector in edit_targets:
                            try:
                                target = self.driver.find_element(by_type, target_selector)
                                if target:
                                    logger.info(f"🎯 尝试填写到: {target_name}")

                                    # 多种填写方式
                                    target.click()  # 确保焦点

                                    # 方式1: 直接send_keys
                                    try:
                                        target.clear()
                                        target.send_keys(content)
                                        logger.info(f"✅ 方式1成功: send_keys")
                                    except:
                                        # 方式2: JavaScript填写
                                        self.driver.execute_script("arguments[0].innerHTML = arguments[1];", target, content)
                                        logger.info(f"✅ 方式2成功: innerHTML")

                                    # 触发事件
                                    self.driver.execute_script("""
                                        arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                                        arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                                    """, target)

                                    self.driver.switch_to.default_content()
                                    logger.info(f"✅ iframe内容已填写 (选择器: {selector}, 目标: {target_name})")
                                    return True
                            except Exception as inner_e:
                                logger.debug(f"目标 {target_name} 失败: {inner_e}")
                                continue

                        self.driver.switch_to.default_content()

                except Exception as e:
                    self.driver.switch_to.default_content()  # 确保退出iframe
                    logger.debug(f"iframe选择器失败 {selector}: {e}")
                    continue

            # 策略2: 富文本编辑器
            contenteditable_selectors = [
                "//div[@contenteditable='true']",
                "//div[@contenteditable='']"
            ]

            for selector in contenteditable_selectors:
                try:
                    content_div = self.driver.find_element(By.XPATH, selector)
                    if content_div and content_div.is_displayed():
                        content_div.click()
                        content_div.clear()
                        content_div.send_keys(content)

                        # 触发事件
                        self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", content_div)

                        logger.info(f"✅ 富文本编辑器内容已填写 (选择器: {selector})")
                        return True
                except Exception as e:
                    logger.debug(f"富文本编辑器选择器失败 {selector}: {e}")
                    continue

            # 策略3: textarea
            textarea_selectors = [
                "//textarea[@name='content']",
                "//textarea[contains(@name, 'content')]",
                "//textarea[contains(@name, 'body')]",
                "//textarea[contains(@name, 'message')]"
            ]

            for selector in textarea_selectors:
                try:
                    textarea = self.driver.find_element(By.XPATH, selector)
                    if textarea and textarea.is_displayed():
                        textarea.click()
                        textarea.clear()
                        textarea.send_keys(content)

                        # 触发事件
                        self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', {bubbles: true}));", textarea)

                        logger.info(f"✅ textarea内容已填写 (选择器: {selector})")
                        return True
                except Exception as e:
                    logger.debug(f"textarea选择器失败 {selector}: {e}")
                    continue

            logger.warning("⚠️ 所有内容填写策略都失败了")

            # 🔍 调试信息：显示页面上的iframe和可编辑元素
            try:
                all_iframes = self.driver.find_elements(By.XPATH, "//iframe")
                logger.info(f"🔍 页面上共有 {len(all_iframes)} 个iframe")
                for i, iframe in enumerate(all_iframes[:3]):  # 只显示前3个
                    src = iframe.get_attribute('src') or 'None'
                    class_attr = iframe.get_attribute('class') or 'None'
                    name = iframe.get_attribute('name') or 'None'
                    logger.info(f"  iframe{i+1}: src='{src}', class='{class_attr}', name='{name}'")

                all_textareas = self.driver.find_elements(By.XPATH, "//textarea")
                logger.info(f"🔍 页面上共有 {len(all_textareas)} 个textarea")

                all_contenteditable = self.driver.find_elements(By.XPATH, "//div[@contenteditable='true']")
                logger.info(f"🔍 页面上共有 {len(all_contenteditable)} 个可编辑div")

            except Exception as debug_error:
                logger.debug(f"调试信息获取失败: {debug_error}")

            return False

        except Exception as e:
            logger.error(f"❌ 填写内容失败: {e}")
            return False

    def _check_send_success_fast(self) -> bool:
        """快速检查发送是否成功"""
        try:
            # 快速检查 - 减少等待时间
            max_wait = 5  # 最多等待5秒
            check_interval = 0.5
            waited = 0

            while waited < max_wait:
                time.sleep(check_interval)
                waited += check_interval

                page_source = self.driver.page_source.lower()
                current_url = self.driver.current_url.lower()

                # 🎯 基于真实发送成功界面的精确指示器
                success_indicators = [
                    '您的邮件已发送',  # 真实界面显示的文字！
                    '邮件已发送', '发送成功', '已发送', '发送完成',
                    'sent successfully', 'message sent',
                    '已成功发送', '发送邮件成功',
                    '邮件发送成功', '成功发送'
                ]
                for indicator in success_indicators:
                    if indicator in page_source:
                        logger.info(f"✅ 发送成功确认: {indicator} (等待{waited:.1f}秒)")
                        return True

                # URL变化检查 - 发送成功后的URL变化
                if 'sent' in current_url or 'success' in current_url or 'mailinfo' in current_url:
                    logger.info(f"✅ 发送成功确认: URL变化 (等待{waited:.1f}秒)")
                    return True

                # 🎯 界面元素检查 - 基于真实发送成功界面
                try:
                    # 检查绿色对勾和"您的邮件已发送"文字
                    success_elements = self.driver.find_elements(By.XPATH,
                        "//*[contains(text(), '您的邮件已发送') or contains(text(), '邮件已发送') or contains(text(), '发送成功')]")
                    if success_elements:
                        logger.info(f"✅ 发送成功确认: 找到成功提示元素 (等待{waited:.1f}秒)")
                        return True

                    # 检查绿色对勾图标
                    check_icons = self.driver.find_elements(By.XPATH,
                        "//i[contains(@class, 'check') or contains(@class, 'success')] | //div[contains(@class, 'success')]")
                    if check_icons:
                        logger.info(f"✅ 发送成功确认: 找到成功图标 (等待{waited:.1f}秒)")
                        return True
                except:
                    pass

                # 错误指示器
                error_indicators = ['发送失败', 'send failed', 'error', '错误', '失败']
                for indicator in error_indicators:
                    if indicator in page_source:
                        logger.warning(f"❌ 发送失败确认: {indicator}")
                        return False

            # 超时后的最终检查
            logger.info(f"⏰ 快速检查超时({max_wait}秒)，进行最终判断...")

            # 如果没有明确的失败指示器，假设成功
            page_source = self.driver.page_source.lower()
            error_indicators = ['发送失败', 'send failed', 'error', '错误']

            for indicator in error_indicators:
                if indicator in page_source:
                    logger.warning(f"❌ 最终检查发现失败指示器: {indicator}")
                    return False

            logger.info("🤔 无明确指示器，假设发送成功")
            return True

        except Exception as e:
            logger.error(f"❌ 检查发送结果失败: {e}")
            return False
    
    def _check_send_success(self) -> bool:
        """检查发送是否成功"""
        try:
            time.sleep(2)
            page_source = self.driver.page_source.lower()
            
            success_indicators = ['发送成功', '已发送', 'sent successfully', '发送完成']
            error_indicators = ['发送失败', 'send failed', 'error', '错误']
            
            for indicator in success_indicators:
                if indicator in page_source:
                    logger.info(f"发送成功确认: {indicator}")
                    return True
            
            for indicator in error_indicators:
                if indicator in page_source:
                    logger.warning(f"发送失败确认: {indicator}")
                    return False
            
            logger.info("无明确指示器，假设发送成功")
            return True
            
        except Exception as e:
            logger.error(f"检查发送结果失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取发送统计"""
        success_rate = (self.success_count / max(self.send_count, 1)) * 100
        return {
            'send_count': self.send_count,
            'success_count': self.success_count,
            'fail_count': self.send_count - self.success_count,
            'success_rate': round(success_rate, 1)
        }
    
    def reset_for_next_email(self) -> bool:
        """为下一封邮件重置状态"""
        try:
            logger.info("为下一封邮件重置状态...")
            return self.prepare_compose_page()
        except Exception as e:
            logger.error(f"重置状态失败: {e}")
            return False

    def quick_reset_for_continuous_sending(self) -> bool:
        """快速重置用于连续发送 - 速度优化版本"""
        try:
            # 检查是否仍在写邮件界面
            current_url = self.driver.current_url
            if 'action=writer' in current_url:
                # 仍在写邮件界面，只需清空输入框
                self._quick_clear_compose_fields()
                logger.info("⚡ 快速重置完成（仍在写邮件界面）")
                return True
            else:
                # 需要重新进入写邮件界面
                return self.prepare_compose_page()

        except Exception as e:
            logger.error(f"❌ 快速重置失败: {e}")
            return False

    def _quick_clear_compose_fields(self):
        """快速清空写邮件字段"""
        try:
            # 快速清空收件人
            to_field = self.find_element_by_selectors(self.selectors['to_inputs'], timeout=0.5)
            if to_field:
                to_field.clear()

            # 快速清空主题
            subject_field = self.find_element_by_selectors(self.selectors['subject_inputs'], timeout=0.5)
            if subject_field:
                subject_field.clear()

            # 快速清空内容（如果是textarea）
            try:
                content_area = self.find_element_by_selectors(self.selectors['content_areas'], timeout=0.5)
                if content_area and content_area.tag_name == 'textarea':
                    content_area.clear()
            except:
                pass

            logger.info("⚡ 输入字段快速清空完成")

        except Exception as e:
            logger.debug(f"快速清空字段失败: {e}")
