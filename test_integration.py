#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多浏览器发送系统集成测试
测试所有主要功能模块的集成和运行状态
"""

import sys
import os
import tempfile
import json
from datetime import datetime

# 添加项目路径
sys.path.append('.')

def test_core_modules():
    """测试核心模块"""
    print("=" * 50)
    print("测试核心模块")
    print("=" * 50)
    
    try:
        # 测试多邮箱管理器
        from src.core.multi_sender_manager import MultiSenderManager, MultiSenderConfig, RotationStrategy
        config = MultiSenderConfig(
            emails_per_sender=5,
            rotation_strategy=RotationStrategy.SEQUENTIAL
        )
        manager = MultiSenderManager(config)
        print("✓ 多邮箱管理器创建成功")
        
        # 测试发送模式管理器
        from src.core.send_mode_manager import SendModeManager, SendModeConfig, SendMode
        mode_config = SendModeConfig(mode=SendMode.SEQUENTIAL)
        mode_manager = SendModeManager(mode_config)
        print("✓ 发送模式管理器创建成功")
        
        # 测试变量管理器
        from src.utils.variable_manager import VariableManager
        var_manager = VariableManager()
        test_text = "Hello {name}, welcome to {company}!"
        variables = var_manager.extract_variables(test_text)
        assert set(variables) == {'name', 'company'}, f"变量提取失败: {variables}"
        print("✓ 变量管理器功能正常")
        
        # 测试导入模板管理器
        from src.core.import_template_manager import ImportTemplateManager
        template_manager = ImportTemplateManager()
        templates = template_manager.get_all_templates()
        assert len(templates) > 0, "没有找到默认模板"
        print(f"✓ 导入模板管理器正常，找到 {len(templates)} 个模板")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_database_modules():
    """测试数据库模块"""
    print("\n" + "=" * 50)
    print("测试数据库模块")
    print("=" * 50)
    
    try:
        # 创建临时数据库
        from src.models.database import DatabaseManager
        temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db.close()
        
        db_manager = DatabaseManager(temp_db.name)
        print("✓ 数据库管理器创建成功")
        
        # 测试邮件模板管理
        from src.models.email_template import EmailTemplateManager, EmailTemplate
        template_manager = EmailTemplateManager(db_manager)
        
        # 创建测试模板
        test_template = EmailTemplate(
            template_name="测试模板",
            subject="测试主题 {name}",
            content="测试内容 {company}",
            content_type="text/plain",
            variables='{"name": "姓名", "company": "公司"}'
        )
        
        template_id = template_manager.add_template(test_template)
        assert template_id > 0, "模板添加失败"
        print("✓ 邮件模板管理功能正常")
        
        # 测试发送记录管理
        from src.models.send_record import SendRecordManager, SendRecord, SendStatus
        record_manager = SendRecordManager(db_manager)
        
        test_record = SendRecord(
            from_email="<EMAIL>",
            to_email="<EMAIL>",
            subject="测试邮件",
            content="测试内容",
            status=SendStatus.SUCCESS
        )
        
        record_id = record_manager.add_record(test_record)
        assert record_id > 0, "发送记录添加失败"
        print("✓ 发送记录管理功能正常")
        
        # 测试数据源管理
        from src.core.data_source_manager import DataSourceManager, RecipientData
        data_manager = DataSourceManager(db_manager)
        
        test_recipient = RecipientData(
            email="<EMAIL>",
            name="测试用户",
            company="测试公司",
            source="manual"
        )
        
        success = data_manager.add_recipient(test_recipient)
        assert success, "收件人添加失败"
        print("✓ 数据源管理功能正常")
        
        # 清理临时文件
        try:
            db_manager.close()  # 先关闭数据库连接
        except:
            pass

        try:
            os.unlink(temp_db.name)
        except PermissionError:
            # Windows下可能有权限问题，忽略
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_template_functionality():
    """测试模板功能"""
    print("\n" + "=" * 50)
    print("测试模板功能")
    print("=" * 50)
    
    try:
        from src.core.import_template_manager import ImportTemplateManager
        
        template_manager = ImportTemplateManager()
        
        # 测试获取模板
        templates = template_manager.get_all_templates()
        print(f"✓ 获取到 {len(templates)} 个默认模板")
        
        # 测试按格式过滤
        csv_templates = template_manager.get_templates_by_format("csv")
        print(f"✓ 找到 {len(csv_templates)} 个CSV模板")
        
        # 测试创建模板文件
        if csv_templates:
            template = csv_templates[0]
            temp_file = tempfile.NamedTemporaryFile(suffix='.csv', delete=False)
            temp_file.close()
            
            template_manager.create_template_file(template, temp_file.name)
            
            # 检查文件是否创建成功
            assert os.path.exists(temp_file.name), "模板文件创建失败"
            assert os.path.getsize(temp_file.name) > 0, "模板文件为空"
            
            print("✓ 模板文件创建成功")
            
            # 清理临时文件
            os.unlink(temp_file.name)
        
        return True
        
    except Exception as e:
        print(f"❌ 模板功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_variable_replacement():
    """测试变量替换功能"""
    print("\n" + "=" * 50)
    print("测试变量替换功能")
    print("=" * 50)
    
    try:
        from src.utils.variable_manager import VariableManager
        
        var_manager = VariableManager()
        
        # 测试变量提取
        template = "Hello {name}, welcome to {company}! Today is {date}."
        variables = var_manager.extract_variables(template)
        expected = ['name', 'company', 'date']
        assert set(variables) == set(expected), f"变量提取错误: {variables}"
        print("✓ 变量提取功能正常")
        
        # 测试变量替换
        var_values = {
            'name': '张三',
            'company': 'ABC公司'
        }
        
        result = var_manager.replace_variables(template, var_values)
        assert '张三' in result, "姓名变量替换失败"
        assert 'ABC公司' in result, "公司变量替换失败"
        assert '{date}' not in result, "日期变量应该被内置变量替换"
        print("✓ 变量替换功能正常")
        
        # 测试个性化内容生成
        recipient_info = {
            'name': '李四',
            'company': 'XYZ公司'
        }
        
        personalized = var_manager.generate_personalized_content(template, recipient_info, 1)
        assert '李四' in personalized, "个性化内容生成失败"
        print("✓ 个性化内容生成功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 变量替换功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_components():
    """测试GUI组件（不显示界面）"""
    print("\n" + "=" * 50)
    print("测试GUI组件")
    print("=" * 50)
    
    try:
        # 由于GUI组件需要QApplication，这里只测试导入
        from src.gui.email_template_widget import EmailTemplateWidget
        from src.gui.send_statistics_widget import SendStatisticsWidget
        from src.gui.data_source_widget import DataSourceWidget
        from src.gui.import_template_widget import ImportTemplateWidget
        from src.gui.multi_browser_sender_widget import MultiBrowserSenderWidget
        
        print("✓ 所有GUI组件导入成功")
        
        # 测试对话框组件
        from src.gui.email_template_widget import EmailTemplateDialog
        from src.gui.data_source_widget import RecipientEditDialog
        from src.gui.import_template_widget import TemplatePreviewDialog
        
        print("✓ 所有对话框组件导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("多浏览器发送系统集成测试")
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("核心模块", test_core_modules()))
    test_results.append(("数据库模块", test_database_modules()))
    test_results.append(("模板功能", test_template_functionality()))
    test_results.append(("变量替换", test_variable_replacement()))
    test_results.append(("GUI组件", test_gui_components()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "❌ 失败"
        print(f"{test_name:15} {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed + failed} 项测试")
    print(f"通过: {passed} 项")
    print(f"失败: {failed} 项")
    
    if failed == 0:
        print("\n🎉 所有测试通过！多浏览器发送系统集成测试成功！")
        return True
    else:
        print(f"\n⚠️  有 {failed} 项测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
