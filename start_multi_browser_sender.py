#!/usr/bin/env python3
"""
多浏览器Cookie复用发送器 - 快速启动脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from src.gui.multi_browser_sender_widget import MultiBrowserSenderWidget
from src.utils.logger import setup_logger

def main():
    """启动多浏览器发送器"""
    print("🚀 启动多浏览器Cookie复用发送器...")
    
    # 设置日志
    logger = setup_logger("INFO")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("多浏览器Cookie复用发送器")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    widget = MultiBrowserSenderWidget()
    widget.setWindowTitle("🌐 多浏览器Cookie复用发送器 v1.0.0")
    widget.show()
    
    print("✅ 多浏览器发送器启动成功")
    print("📋 功能说明:")
    print("   1. 配置浏览器数量和发送参数")
    print("   2. 加载已有Cookie的邮箱账号")
    print("   3. 添加邮件任务")
    print("   4. 开始多浏览器轮换发送")
    print()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
