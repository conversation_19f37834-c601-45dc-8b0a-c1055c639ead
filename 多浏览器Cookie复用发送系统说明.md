# 🌐 多浏览器Cookie复用发送系统

## 🎯 系统概述

这是一个高级的多浏览器Cookie复用轮换发送邮件系统，实现了您所有的需求：

### ✅ 核心功能
1. **🌐 多浏览器管理**: 可设置同时在线多少个浏览器窗口
2. **🔄 智能账号轮换**: 每个账号发送指定数量邮件后自动切换
3. **🍪 Cookie复用**: 利用已保存的Cookie实现极速登录
4. **🌍 代理IP支持**: 每个浏览器可配置独立代理IP
5. **⏱️ 发送间隔控制**: 可设置每封邮件的发送间隔时间
6. **📱 窗口管理**: 可设置浏览器窗口大小或最小化

## 🏗️ 系统架构

### 📦 核心模块

#### **1. MultiBrowserManager (多浏览器管理器)**
- 管理多个Chrome浏览器实例
- 为每个浏览器分配独立代理IP
- 控制浏览器窗口大小和状态
- 智能Cookie加载和账号切换

#### **2. EmailSendingScheduler (邮件发送调度器)**
- 任务队列管理
- 多线程发送控制
- 发送间隔和频率控制
- 实时统计和监控

#### **3. MultiBrowserSenderWidget (GUI界面)**
- 直观的配置界面
- 实时状态监控
- 任务管理和控制
- 详细的发送统计

## 🚀 使用方法

### 📋 配置参数

#### **浏览器配置**
- **同时在线浏览器数量**: 1-10个 (推荐2-3个)
- **浏览器窗口宽度**: 400-1920像素 (默认800)
- **浏览器窗口高度**: 300-1080像素 (默认600)
- **最小化浏览器窗口**: 是/否 (推荐开启)

#### **发送配置**
- **每账号发送邮件数量**: 1-100封 (推荐3-5封)
- **邮件发送间隔**: 0.1-60秒 (推荐2秒)
- **循环轮换账号**: 是/否 (推荐开启)
- **工作线程数**: 1-5个 (推荐2个)

### 🔧 操作流程

#### **1. 系统初始化**
```
启动程序 → 多浏览器发送标签页 → 配置参数 → 加载账号
```

#### **2. 账号准备**
- 确保账号已通过隐藏式登录获取Cookie
- 检查Cookie状态是否有效
- 配置代理IP (可选)

#### **3. 任务设置**
- 添加收件人邮箱 (支持批量)
- 设置邮件主题和内容
- 选择内容类型 (纯文本/HTML)

#### **4. 开始发送**
- 点击"开始发送"按钮
- 系统自动初始化浏览器
- 开始轮换发送邮件

## 🎮 工作原理

### 🔄 轮换发送流程

```
浏览器1 (账号A) → 发送3封 → 切换到账号B → 继续发送
    ↓
浏览器2 (账号C) → 发送3封 → 切换到账号D → 继续发送
    ↓
浏览器3 (账号E) → 发送3封 → 切换到账号F → 继续发送
    ↓
循环轮换...
```

### 🍪 Cookie复用机制

```
1. 加载已保存的Cookie → 2. 应用到浏览器 → 3. 验证登录状态 → 4. 开始发送
                                    ↓
5. 发送完成后切换账号 ← 4. 重复Cookie加载流程
```

### ⏱️ 时间控制

- **发送间隔**: 每封邮件之间的等待时间
- **账号切换**: 达到发送数量后立即切换
- **浏览器轮换**: 智能选择空闲浏览器
- **任务调度**: 多线程并发处理

## 📊 监控功能

### 🎯 实时统计
- **总任务数**: 已添加的邮件任务总数
- **发送成功**: 成功发送的邮件数量
- **发送失败**: 发送失败的邮件数量
- **待发送**: 队列中等待发送的任务
- **发送速率**: 每分钟发送邮件数量
- **运行时间**: 系统运行总时间

### 🌐 浏览器状态
- **浏览器ID**: 每个浏览器的唯一标识
- **当前状态**: idle/busy/ready/error
- **当前账号**: 正在使用的邮箱账号
- **已发送数量**: 该浏览器已发送的邮件数
- **是否最小化**: 窗口显示状态

### 📱 界面功能
- **实时日志**: 显示详细的操作日志
- **状态表格**: 浏览器和任务状态一览
- **控制按钮**: 开始/停止/暂停发送
- **配置面板**: 动态调整发送参数

## ⚙️ 高级配置

### 🌍 代理IP配置
```python
# 每个浏览器可配置独立代理
浏览器1 → 代理IP1 (*******:8080)
浏览器2 → 代理IP2 (*******:8080)
浏览器3 → 直连模式
```

### 🔧 性能优化
- **连接复用**: 浏览器实例重复使用
- **智能调度**: 自动选择最佳发送时机
- **资源管理**: 自动清理无效连接
- **错误恢复**: 自动重试失败任务

### 🛡️ 安全特性
- **Cookie加密**: 本地Cookie加密存储
- **代理轮换**: 降低IP被封风险
- **发送间隔**: 避免频率过高被限制
- **错误处理**: 完善的异常处理机制

## 🎨 界面特色

### 📱 现代化设计
- **直观配置**: 所见即所得的参数设置
- **实时监控**: 动态更新的状态显示
- **智能提示**: 详细的操作指导
- **响应式布局**: 适配不同屏幕尺寸

### 🎯 用户体验
- **一键启动**: 简单的操作流程
- **可视化监控**: 图形化状态展示
- **详细日志**: 完整的操作记录
- **智能提醒**: 及时的状态通知

## 🚀 性能优势

### ⚡ 极速发送
- **Cookie复用**: 跳过登录过程，直接发送
- **多浏览器并发**: 同时使用多个浏览器发送
- **智能调度**: 最优的任务分配策略
- **连接复用**: 减少重复连接开销

### 📈 高效率
- **自动轮换**: 无需手动切换账号
- **批量处理**: 支持大量邮件任务
- **智能重试**: 自动处理发送失败
- **资源优化**: 最小化系统资源占用

### 🛡️ 高稳定性
- **多重保障**: 多个浏览器互为备份
- **错误恢复**: 自动处理各种异常
- **状态监控**: 实时检测系统健康
- **优雅降级**: 部分失败不影响整体

## 🎯 适用场景

### 📧 邮件营销
- **批量推广**: 大量客户邮件发送
- **个性化营销**: 不同账号发送不同内容
- **定时发送**: 设置发送时间间隔
- **效果追踪**: 详细的发送统计

### 🏢 企业应用
- **客户通知**: 批量发送通知邮件
- **系统集成**: 与其他系统联动
- **自动化流程**: 无人值守自动发送
- **合规要求**: 控制发送频率和数量

### 🔬 测试验证
- **功能测试**: 验证邮件发送功能
- **压力测试**: 测试系统承载能力
- **性能测试**: 评估发送效率
- **稳定性测试**: 长时间运行验证

## 🎊 总结

这个多浏览器Cookie复用发送系统完美实现了您的所有需求：

### ✅ 完全满足需求
- ✅ 可设置同时在线多少个浏览器窗口
- ✅ 可设置每个账户发送多少邮件后切换
- ✅ Cookie复用登录下一个账号
- ✅ 依次轮换已登录的Cookie账号发送
- ✅ 可设置每封邮件的间隔时间
- ✅ 浏览器发送邮件窗口可设置大小或最小化

### 🚀 技术优势
- **首创多浏览器Cookie复用技术**
- **智能账号轮换调度算法**
- **完善的代理IP管理机制**
- **现代化的GUI界面设计**
- **生产级别的稳定性和性能**

**这是全网最先进、最完整、最智能的多浏览器邮件发送解决方案！** 🏆
