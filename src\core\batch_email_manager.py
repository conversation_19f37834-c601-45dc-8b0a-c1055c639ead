#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量邮件发送管理器
专为100+账号设计的高性能批量发送系统
- 智能负载均衡
- 频率控制
- 失败重试
- 实时监控
"""

import asyncio
import time
import random
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
from queue import Queue, PriorityQueue
import threading

from ..models.account import Account
from ..utils.logger import get_logger
from .cookie_manager import CookieManager
from .lightweight_email_sender import LightweightEmailSender, EmailMessage

logger = get_logger("BatchEmailManager")


@dataclass
class SendTask:
    """发送任务"""
    priority: int  # 优先级 (1=高, 2=中, 3=低)
    account: Account
    message: EmailMessage
    retry_count: int = 0
    max_retries: int = 3
    created_time: datetime = field(default_factory=datetime.now)
    scheduled_time: Optional[datetime] = None
    
    def __lt__(self, other):
        return self.priority < other.priority


@dataclass
class AccountStatus:
    """账号状态"""
    email: str
    is_active: bool = True
    last_send_time: Optional[datetime] = None
    send_count_today: int = 0
    total_sent: int = 0
    success_count: int = 0
    failed_count: int = 0
    consecutive_failures: int = 0
    rate_limit_until: Optional[datetime] = None
    
    @property
    def success_rate(self) -> float:
        if self.total_sent == 0:
            return 100.0
        return (self.success_count / self.total_sent) * 100
    
    @property
    def is_rate_limited(self) -> bool:
        if not self.rate_limit_until:
            return False
        return datetime.now() < self.rate_limit_until


class BatchEmailManager:
    """批量邮件发送管理器"""
    
    def __init__(self, cookie_manager: CookieManager, config: Dict[str, Any] = None):
        self.cookie_manager = cookie_manager
        self.email_sender = LightweightEmailSender(cookie_manager)
        self.config = config or {}
        
        # 发送配置
        self.max_concurrent = self.config.get('max_concurrent_sends', 20)
        self.send_interval = self.config.get('send_interval_seconds', 2)
        self.daily_limit_per_account = self.config.get('daily_limit_per_account', 100)
        self.rate_limit_threshold = self.config.get('rate_limit_threshold', 5)  # 连续失败次数
        
        # 任务队列和状态管理
        self.task_queue = PriorityQueue()
        self.account_status: Dict[str, AccountStatus] = {}
        self.active_tasks: Dict[str, asyncio.Task] = {}
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'active_accounts': 0,
            'start_time': datetime.now()
        }
        
        # 控制标志
        self.is_running = False
        self.stop_event = threading.Event()
        
        logger.info("批量邮件发送管理器初始化完成")
    
    def add_accounts(self, accounts: List[Account]):
        """添加账号到管理器"""
        try:
            for account in accounts:
                if account.email not in self.account_status:
                    self.account_status[account.email] = AccountStatus(email=account.email)
                    logger.info(f"添加账号: {account.email}")
            
            self.stats['active_accounts'] = len(self.account_status)
            logger.info(f"✅ 已添加 {len(accounts)} 个账号，总计 {self.stats['active_accounts']} 个活跃账号")
            
        except Exception as e:
            logger.error(f"❌ 添加账号失败: {e}")
    
    def add_send_task(self, account: Account, message: EmailMessage, priority: int = 2, 
                     scheduled_time: Optional[datetime] = None) -> bool:
        """添加发送任务"""
        try:
            # 检查账号状态
            if account.email not in self.account_status:
                self.add_accounts([account])
            
            account_status = self.account_status[account.email]
            
            # 检查账号是否可用
            if not account_status.is_active:
                logger.warning(f"账号不可用: {account.email}")
                return False
            
            # 检查频率限制
            if account_status.is_rate_limited:
                logger.warning(f"账号被频率限制: {account.email}")
                return False
            
            # 检查每日发送限制
            if account_status.send_count_today >= self.daily_limit_per_account:
                logger.warning(f"账号达到每日发送限制: {account.email}")
                return False
            
            # 创建发送任务
            task = SendTask(
                priority=priority,
                account=account,
                message=message,
                scheduled_time=scheduled_time
            )
            
            self.task_queue.put(task)
            self.stats['total_tasks'] += 1
            
            logger.info(f"✅ 添加发送任务: {account.email} -> {message.to_email} (优先级: {priority})")
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加发送任务失败: {e}")
            return False
    
    def add_batch_tasks(self, tasks: List[Tuple[Account, EmailMessage, int]]) -> int:
        """批量添加发送任务"""
        try:
            success_count = 0
            
            for account, message, priority in tasks:
                if self.add_send_task(account, message, priority):
                    success_count += 1
            
            logger.info(f"✅ 批量添加任务完成: {success_count}/{len(tasks)} 个任务成功添加")
            return success_count
            
        except Exception as e:
            logger.error(f"❌ 批量添加任务失败: {e}")
            return 0
    
    async def start_batch_sending(self):
        """开始批量发送"""
        try:
            logger.info("🚀 开始批量邮件发送...")
            self.is_running = True
            self.stop_event.clear()
            
            # 创建发送工作协程
            workers = []
            for i in range(self.max_concurrent):
                worker = asyncio.create_task(self._send_worker(f"Worker-{i+1}"))
                workers.append(worker)
            
            # 创建监控协程
            monitor_task = asyncio.create_task(self._monitor_progress())
            
            # 等待所有任务完成或停止信号
            try:
                await asyncio.gather(*workers, monitor_task)
            except asyncio.CancelledError:
                logger.info("批量发送被取消")
            
            logger.info("✅ 批量邮件发送完成")
            
        except Exception as e:
            logger.error(f"❌ 批量发送失败: {e}")
        finally:
            self.is_running = False
    
    async def _send_worker(self, worker_name: str):
        """发送工作协程"""
        try:
            logger.info(f"🔧 {worker_name} 启动")
            
            while self.is_running and not self.stop_event.is_set():
                try:
                    # 获取任务（非阻塞）
                    if self.task_queue.empty():
                        await asyncio.sleep(1)
                        continue
                    
                    task = self.task_queue.get_nowait()
                    
                    # 检查是否到达预定发送时间
                    if task.scheduled_time and datetime.now() < task.scheduled_time:
                        # 重新放回队列
                        self.task_queue.put(task)
                        await asyncio.sleep(1)
                        continue
                    
                    # 执行发送
                    await self._execute_send_task(task, worker_name)
                    
                    # 发送间隔
                    await asyncio.sleep(self.send_interval)
                    
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error(f"❌ {worker_name} 处理任务异常: {e}")
                    await asyncio.sleep(1)
            
            logger.info(f"🔧 {worker_name} 停止")
            
        except Exception as e:
            logger.error(f"❌ {worker_name} 异常退出: {e}")
    
    async def _execute_send_task(self, task: SendTask, worker_name: str):
        """执行发送任务"""
        try:
            account_status = self.account_status[task.account.email]
            
            logger.info(f"📧 {worker_name} 发送邮件: {task.account.email} -> {task.message.to_email}")
            
            # 执行发送
            result = self.email_sender.send_email_lightweight(task.account, task.message)
            
            # 更新账号状态
            account_status.last_send_time = datetime.now()
            account_status.total_sent += 1
            
            if result['success']:
                # 发送成功
                account_status.success_count += 1
                account_status.consecutive_failures = 0
                account_status.send_count_today += 1
                self.stats['completed_tasks'] += 1
                
                logger.info(f"✅ {worker_name} 发送成功: {task.account.email}")
                
            else:
                # 发送失败
                account_status.failed_count += 1
                account_status.consecutive_failures += 1
                
                # 检查是否需要重试
                if task.retry_count < task.max_retries:
                    task.retry_count += 1
                    # 延迟重试
                    task.scheduled_time = datetime.now() + timedelta(minutes=task.retry_count * 5)
                    self.task_queue.put(task)
                    logger.warning(f"⚠️ {worker_name} 发送失败，安排重试: {task.account.email} (第{task.retry_count}次)")
                else:
                    self.stats['failed_tasks'] += 1
                    logger.error(f"❌ {worker_name} 发送最终失败: {task.account.email}")
                
                # 检查是否需要频率限制
                if account_status.consecutive_failures >= self.rate_limit_threshold:
                    account_status.rate_limit_until = datetime.now() + timedelta(hours=1)
                    logger.warning(f"⚠️ 账号被频率限制1小时: {task.account.email}")
            
        except Exception as e:
            logger.error(f"❌ {worker_name} 执行发送任务异常: {e}")
            self.stats['failed_tasks'] += 1
    
    async def _monitor_progress(self):
        """监控发送进度"""
        try:
            while self.is_running and not self.stop_event.is_set():
                await asyncio.sleep(30)  # 每30秒报告一次
                
                stats = self.get_sending_statistics()
                logger.info(f"📊 发送进度: 总任务={stats['total_tasks']}, "
                          f"已完成={stats['completed_tasks']}, "
                          f"失败={stats['failed_tasks']}, "
                          f"队列中={stats['pending_tasks']}, "
                          f"成功率={stats['success_rate']:.1f}%")
                
        except Exception as e:
            logger.error(f"❌ 监控进度异常: {e}")
    
    def stop_batch_sending(self):
        """停止批量发送"""
        logger.info("🛑 停止批量邮件发送...")
        self.is_running = False
        self.stop_event.set()
    
    def get_sending_statistics(self) -> Dict[str, Any]:
        """获取发送统计信息"""
        try:
            pending_tasks = self.task_queue.qsize()
            runtime = datetime.now() - self.stats['start_time']
            
            # 计算成功率
            total_processed = self.stats['completed_tasks'] + self.stats['failed_tasks']
            success_rate = (self.stats['completed_tasks'] / max(total_processed, 1)) * 100
            
            # 计算发送速度
            emails_per_minute = (total_processed / max(runtime.total_seconds() / 60, 1))
            
            # 账号状态统计
            active_accounts = sum(1 for status in self.account_status.values() if status.is_active)
            rate_limited_accounts = sum(1 for status in self.account_status.values() if status.is_rate_limited)
            
            stats = {
                'total_tasks': self.stats['total_tasks'],
                'completed_tasks': self.stats['completed_tasks'],
                'failed_tasks': self.stats['failed_tasks'],
                'pending_tasks': pending_tasks,
                'success_rate': success_rate,
                'runtime_minutes': runtime.total_seconds() / 60,
                'emails_per_minute': emails_per_minute,
                'active_accounts': active_accounts,
                'rate_limited_accounts': rate_limited_accounts,
                'total_accounts': len(self.account_status)
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ 获取统计信息失败: {e}")
            return {}
    
    def get_account_statistics(self) -> List[Dict[str, Any]]:
        """获取账号统计信息"""
        try:
            account_stats = []
            
            for email, status in self.account_status.items():
                stats = {
                    'email': email,
                    'is_active': status.is_active,
                    'total_sent': status.total_sent,
                    'success_count': status.success_count,
                    'failed_count': status.failed_count,
                    'success_rate': status.success_rate,
                    'send_count_today': status.send_count_today,
                    'consecutive_failures': status.consecutive_failures,
                    'is_rate_limited': status.is_rate_limited,
                    'last_send_time': status.last_send_time.isoformat() if status.last_send_time else None
                }
                account_stats.append(stats)
            
            # 按成功率排序
            account_stats.sort(key=lambda x: x['success_rate'], reverse=True)
            
            return account_stats
            
        except Exception as e:
            logger.error(f"❌ 获取账号统计失败: {e}")
            return []
    
    def reset_daily_limits(self):
        """重置每日发送限制"""
        try:
            for status in self.account_status.values():
                status.send_count_today = 0
            
            logger.info("✅ 每日发送限制已重置")
            
        except Exception as e:
            logger.error(f"❌ 重置每日限制失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            self.stop_batch_sending()
            self.email_sender.cleanup_sessions()
            logger.info("✅ 批量发送管理器清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理资源失败: {e}")
