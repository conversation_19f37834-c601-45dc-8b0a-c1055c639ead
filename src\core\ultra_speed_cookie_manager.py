#!/usr/bin/env python3
"""
超极速Cookie登录管理器 - 专为速度优化
基于成功经验，优化主程序快速cookie登录的速度
目标：将cookie登录时间从3-5秒优化到1-2秒
"""

import time
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from src.utils.logger import setup_logger
from src.core.cookie_manager import CookieManager

logger = setup_logger("INFO")

class UltraSpeedCookieManager:
    """超极速Cookie登录管理器"""
    
    def __init__(self, base_cookie_manager: CookieManager):
        self.base_manager = base_cookie_manager
        self.cookie_cache: Dict[str, Any] = {}  # Cookie缓存
        self.login_cache: Dict[str, float] = {}  # 登录时间缓存
        
        logger.info("⚡ 超极速Cookie管理器初始化完成")
    
    def preload_cookies(self, account_emails: List[str]):
        """预加载Cookie到缓存 - 关键优化"""
        try:
            logger.info(f"🔥 预加载 {len(account_emails)} 个账号的Cookie...")
            
            for email in account_emails:
                cookie_data = self.base_manager.get_cookies(email)
                if cookie_data:
                    # 预处理Cookie数据
                    processed_cookies = self._preprocess_cookies(cookie_data['cookies'])
                    self.cookie_cache[email] = {
                        'cookies': processed_cookies,
                        'load_time': time.time(),
                        'processed': True
                    }
                    logger.info(f"✅ 预加载Cookie: {email} ({len(processed_cookies)} 个)")
                else:
                    logger.warning(f"⚠️ 未找到Cookie: {email}")
            
            logger.info(f"🔥 Cookie预加载完成: {len(self.cookie_cache)} 个账号")
            
        except Exception as e:
            logger.error(f"❌ Cookie预加载失败: {e}")
    
    def _preprocess_cookies(self, cookies: List[Dict]) -> List[Dict]:
        """预处理Cookie数据 - 过滤和优化"""
        processed = []
        
        for cookie in cookies:
            # 只保留新浪相关的Cookie
            domain = cookie.get('domain', '')
            if 'sina.com' in domain or domain.startswith('.'):
                # 简化Cookie结构，只保留必要字段
                processed_cookie = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie.get('domain', '.sina.com.cn'),
                    'path': cookie.get('path', '/'),
                }
                processed.append(processed_cookie)
        
        return processed
    
    def ultra_speed_cookie_login(self, driver, account_email: str) -> bool:
        """超极速Cookie登录"""
        start_time = time.time()
        
        try:
            logger.info(f"⚡ 超极速Cookie登录: {account_email}")
            
            # 1. 从缓存获取Cookie（如果有）
            if account_email in self.cookie_cache:
                cookies = self.cookie_cache[account_email]['cookies']
                logger.info(f"🔥 使用缓存Cookie: {len(cookies)} 个")
            else:
                # 实时加载并预处理
                cookie_data = self.base_manager.get_cookies(account_email)
                if not cookie_data:
                    logger.error(f"❌ 未找到Cookie: {account_email}")
                    return False
                
                cookies = self._preprocess_cookies(cookie_data['cookies'])
                # 缓存起来
                self.cookie_cache[account_email] = {
                    'cookies': cookies,
                    'load_time': time.time(),
                    'processed': True
                }
            
            # 2. 超极速访问主页（如果需要）
            current_url = driver.current_url
            if 'mail.sina.com.cn' not in current_url:
                logger.info("🌐 快速访问新浪邮箱...")
                driver.get("https://mail.sina.com.cn/")
                time.sleep(0.5)  # 极短等待
            
            # 3. 超极速清除和应用Cookie
            logger.info("🍪 超极速应用Cookie...")
            
            # 快速清除现有Cookie
            driver.delete_all_cookies()
            
            # 批量应用Cookie - 无等待
            applied_count = 0
            for cookie in cookies:
                try:
                    driver.add_cookie(cookie)
                    applied_count += 1
                except Exception as e:
                    logger.debug(f"跳过Cookie: {cookie['name']} - {e}")
            
            logger.info(f"⚡ 应用Cookie: {applied_count}/{len(cookies)} 个")
            
            # 4. 超极速刷新验证
            logger.info("🔄 超极速刷新验证...")
            driver.refresh()
            
            # 5. 极速登录验证
            login_success = self._ultra_speed_login_verification(driver)
            
            elapsed = time.time() - start_time
            
            if login_success:
                self.login_cache[account_email] = time.time()
                logger.info(f"⚡ 超极速Cookie登录成功: {account_email} ({elapsed:.2f}秒)")
                return True
            else:
                logger.error(f"❌ 超极速Cookie登录失败: {account_email} ({elapsed:.2f}秒)")
                return False
                
        except Exception as e:
            elapsed = time.time() - start_time
            logger.error(f"❌ 超极速Cookie登录异常: {account_email} ({elapsed:.2f}秒) - {e}")
            return False
    
    def _ultra_speed_login_verification(self, driver) -> bool:
        """超极速登录验证 - 最小化验证时间"""
        try:
            # 极速验证策略：检查关键元素
            verification_start = time.time()
            
            # 方法1: 检查URL变化（最快）
            current_url = driver.current_url
            if 'mail.sina.com.cn' in current_url and 'login' not in current_url:
                logger.info("⚡ URL验证通过（极速）")
                return True
            
            # 方法2: 检查页面标题（快速）
            try:
                title = driver.title
                if '新浪邮箱' in title or 'Sina Mail' in title:
                    logger.info("⚡ 标题验证通过（快速）")
                    return True
            except:
                pass
            
            # 方法3: 检查关键元素（备用）
            try:
                # 极短超时检查写信按钮
                wait = WebDriverWait(driver, 1)  # 只等待1秒
                write_button = wait.until(EC.presence_of_element_located((
                    By.XPATH, "//a[contains(text(), '写信') or contains(text(), '写邮件')]"
                )))
                if write_button:
                    logger.info("⚡ 元素验证通过（备用）")
                    return True
            except:
                pass
            
            elapsed = time.time() - verification_start
            logger.warning(f"⚠️ 登录验证失败 ({elapsed:.2f}秒)")
            return False
            
        except Exception as e:
            logger.error(f"❌ 登录验证异常: {e}")
            return False
    
    def get_cached_cookie_count(self) -> int:
        """获取缓存的Cookie数量"""
        return len(self.cookie_cache)
    
    def clear_cache(self):
        """清理缓存"""
        self.cookie_cache.clear()
        self.login_cache.clear()
        logger.info("🧹 Cookie缓存已清理")

class UltraSpeedComposeManager:
    """超极速写信按钮管理器"""
    
    def __init__(self, driver):
        self.driver = driver
        
        # 超极速写信按钮选择器 - 基于成功经验优化
        self.ultra_speed_selectors = [
            # 最高优先级：基于成功经验的精确选择器
            "//a[contains(text(), '写信')]",
            "//a[contains(@title, '写信')]",
            
            # 高优先级：常见变体
            "//a[contains(text(), '写邮件')]",
            "//button[contains(text(), '写信')]",
            "//div[contains(text(), '写信')]",
            
            # 中优先级：属性匹配
            "//a[contains(@href, 'writer')]",
            "//a[contains(@href, 'compose')]",
            
            # 低优先级：通用选择器
            "//a[@title='写邮件']",
            "//span[contains(text(), '写信')]",
        ]
        
        logger.info("⚡ 超极速写信按钮管理器初始化完成")
    
    def ultra_speed_find_and_click_compose(self) -> bool:
        """超极速查找并点击写信按钮"""
        start_time = time.time()
        
        try:
            logger.info("⚡ 超极速查找写信按钮...")
            
            # 记录点击前URL
            before_url = self.driver.current_url
            
            # 超极速查找策略
            write_button = None
            
            # 策略1: 极速查找（无等待）
            for selector in self.ultra_speed_selectors[:3]:  # 只尝试前3个最可能的
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        write_button = elements[0]
                        logger.info(f"⚡ 极速找到写信按钮: {selector}")
                        break
                except:
                    continue
            
            # 策略2: 快速查找（短等待）
            if not write_button:
                try:
                    wait = WebDriverWait(self.driver, 1)  # 只等待1秒
                    for selector in self.ultra_speed_selectors[3:6]:
                        try:
                            write_button = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                            if write_button:
                                logger.info(f"⚡ 快速找到写信按钮: {selector}")
                                break
                        except:
                            continue
                except:
                    pass
            
            if not write_button:
                logger.error("❌ 未找到写信按钮")
                return False
            
            # 超极速点击
            logger.info("⚡ 超极速点击写信按钮...")
            write_button.click()
            
            # 超极速验证URL变化
            success = self._ultra_speed_verify_compose_page(before_url)
            
            elapsed = time.time() - start_time
            
            if success:
                logger.info(f"⚡ 超极速写信按钮点击成功 ({elapsed:.2f}秒)")
                return True
            else:
                logger.error(f"❌ 超极速写信按钮点击失败 ({elapsed:.2f}秒)")
                return False
                
        except Exception as e:
            elapsed = time.time() - start_time
            logger.error(f"❌ 超极速写信按钮点击异常 ({elapsed:.2f}秒) - {e}")
            return False
    
    def _ultra_speed_verify_compose_page(self, before_url: str) -> bool:
        """超极速验证写邮件页面"""
        try:
            # 极速等待URL变化 - 优化等待策略
            for i in range(8):  # 最多等待4秒（8 * 0.5秒）
                time.sleep(0.5)  # 每次等待0.5秒
                
                current_url = self.driver.current_url
                
                # 检查是否进入写邮件界面
                if 'action=writer' in current_url:
                    logger.info(f"⚡ URL验证成功: 已进入写邮件界面 (等待{(i+1)*0.5:.1f}秒)")
                    return True
                elif current_url != before_url:
                    logger.info(f"⚠️ URL已变化但不是写邮件界面: {current_url}")
                    # 继续等待，可能还在跳转中
            
            # 最终检查
            final_url = self.driver.current_url
            if 'action=writer' in final_url:
                logger.info("⚡ 最终验证成功: 已进入写邮件界面")
                return True
            else:
                logger.warning(f"⚠️ 最终验证失败: {final_url}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 写邮件页面验证异常: {e}")
            return False
