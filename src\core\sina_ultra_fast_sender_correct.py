#!/usr/bin/env python3
"""
新浪邮箱超高速发送器 - 正确版本
正确流程: Cookies登录 → 点击写信 → 右侧界面发送
"""

import time
from typing import Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class SinaUltraFastSenderCorrect:
    """新浪邮箱超高速发送器 - 正确版本
    
    正确流程:
    1. 假设已通过cookies登录到新浪邮箱主界面
    2. 点击"写信"按钮
    3. 在右侧显示的写邮件界面中填写和发送
    """
    
    def __init__(self, driver: webdriver.Chrome):
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        self.is_ready = False
        self.send_count = 0
        self.success_count = 0
        
        # 正确的新浪邮箱选择器 - 基于登录后的真实界面
        self.selectors = {
            # 写信按钮 - 在登录后的主界面左侧
            'write_buttons': [
                "//a[contains(text(), '写信')]",
                "//a[contains(text(), '写邮件')]", 
                "//button[contains(text(), '写信')]",
                "//div[contains(text(), '写信')]//a",
                "//span[contains(text(), '写信')]//parent::a",
                "//a[@title='写信']",
                "//a[contains(@href, 'compose')]",
                "//a[contains(@class, 'write')]"
            ],
            
            # 右侧写邮件界面的元素 - 点击写信后右侧显示
            'to_inputs': [
                "//input[contains(@name, 'to')]",
                "//input[contains(@placeholder, '收件人')]",
                "//input[contains(@id, 'to')]",
                "//textarea[contains(@name, 'to')]"
            ],
            
            'subject_inputs': [
                "//input[contains(@name, 'subject')]",
                "//input[contains(@placeholder, '主题')]",
                "//input[contains(@id, 'subject')]"
            ],
            
            # 邮件内容区域 - 可能是iframe或富文本编辑器
            'content_areas': [
                "//iframe[contains(@id, 'editor')]",
                "//iframe[contains(@name, 'content')]",
                "//div[@contenteditable='true']",
                "//textarea[contains(@name, 'content')]",
                "//div[contains(@class, 'editor')]"
            ],
            
            # 发送按钮 - 在右侧写邮件界面
            'send_buttons': [
                "//input[@type='submit'][contains(@value, '发送')]",
                "//button[contains(text(), '发送')]",
                "//a[contains(text(), '发送')]",
                "//input[contains(@value, '发送')]"
            ]
        }
    
    def find_element_by_selectors(self, selectors, timeout=5):
        """通过多个选择器查找元素"""
        for selector in selectors:
            try:
                element = WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                if element and element.is_displayed():
                    logger.debug(f"✅ 找到元素: {selector}")
                    return element
            except:
                continue
        return None
    
    def prepare_compose_page(self) -> bool:
        """准备写邮件页面 - 正确流程：点击写信按钮"""
        try:
            logger.info("🚀 准备新浪邮箱写邮件页面（正确流程）...")
            
            # 检查是否在新浪邮箱主页面（登录后）
            current_url = self.driver.current_url.lower()
            if 'mail.sina.com.cn' not in current_url:
                logger.error("❌ 未检测到新浪邮箱登录状态，请先使用cookies登录")
                return False
            
            logger.info("✅ 检测到已登录新浪邮箱主界面")
            
            # 检查右侧是否已经显示写邮件界面
            if self._is_compose_interface_visible():
                logger.info("✅ 右侧写邮件界面已显示")
                self.is_ready = True
                return True
            
            # 点击写信按钮，让右侧显示写邮件界面
            logger.info("🔍 查找并点击写信按钮...")
            if self._click_write_button():
                # 等待右侧界面加载
                time.sleep(2)
                
                if self._is_compose_interface_visible():
                    logger.info("✅ 成功点击写信按钮，右侧写邮件界面已显示")
                    self.is_ready = True
                    return True
                else:
                    logger.warning("⚠️ 点击写信按钮后，右侧界面未正确显示")
                    return False
            else:
                logger.error("❌ 未找到或无法点击写信按钮")
                return False
            
        except Exception as e:
            logger.error(f"❌ 准备写邮件页面失败: {e}")
            return False
    
    def _click_write_button(self) -> bool:
        """点击写信按钮"""
        try:
            for selector in self.selectors['write_buttons']:
                try:
                    element = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    if element:
                        logger.info(f"✅ 找到写信按钮: {selector}")
                        element.click()
                        return True
                except:
                    continue
            return False
        except Exception as e:
            logger.error(f"❌ 点击写信按钮失败: {e}")
            return False
    
    def _is_compose_interface_visible(self) -> bool:
        """检查右侧写邮件界面是否可见"""
        try:
            # 检查收件人输入框是否存在且可见
            for selector in self.selectors['to_inputs']:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element and element.is_displayed():
                        logger.debug(f"✅ 检测到写邮件界面元素: {selector}")
                        return True
                except:
                    continue
            return False
        except Exception as e:
            logger.debug(f"检查写邮件界面失败: {e}")
            return False
    
    def send_email_ultra_fast(self, to_email: str, subject: str, content: str) -> bool:
        """超高速发送邮件 - 正确流程版本"""
        try:
            if not self.is_ready:
                logger.warning("⚠️ 页面未准备就绪，尝试重新准备...")
                if not self.prepare_compose_page():
                    return False
            
            logger.info(f"⚡ 超高速发送邮件: {to_email}")
            start_time = time.time()
            
            # 策略1: JavaScript超高速填写右侧界面 (最快)
            if self._send_with_javascript_right_panel(to_email, subject, content):
                elapsed = time.time() - start_time
                logger.info(f"✅ JavaScript右侧界面发送成功 ({elapsed:.2f}秒)")
                self.success_count += 1
                return True
            
            # 策略2: 直接操作右侧界面元素 (快速且可靠)
            if self._send_with_right_panel_elements(to_email, subject, content):
                elapsed = time.time() - start_time
                logger.info(f"✅ 右侧界面元素发送成功 ({elapsed:.2f}秒)")
                self.success_count += 1
                return True
            
            elapsed = time.time() - start_time
            logger.error(f"❌ 所有右侧界面发送策略都失败了 ({elapsed:.2f}秒)")
            return False
            
        except Exception as e:
            elapsed = time.time() - start_time if 'start_time' in locals() else 0
            logger.error(f"❌ 超高速发送异常: {e} ({elapsed:.2f}秒)")
            return False
        finally:
            self.send_count += 1
    
    def _send_with_javascript_right_panel(self, to_email: str, subject: str, content: str) -> bool:
        """使用JavaScript超高速填写右侧界面"""
        try:
            logger.info("⚡ JavaScript右侧界面超高速发送...")
            
            # 转义特殊字符
            safe_email = to_email.replace("'", "\\'").replace('"', '\\"')
            safe_subject = subject.replace("'", "\\'").replace('"', '\\"')
            safe_content = content.replace("'", "\\'").replace('"', '\\"').replace('\n', '\\n')
            
            # 针对右侧界面的JavaScript代码
            js_code = f"""
            try {{
                console.log('⚡ 开始JavaScript右侧界面超高速发送...');
                
                // 1. 填写收件人 - 右侧界面
                var toField = document.querySelector('input[name*="to"]') ||
                             document.querySelector('input[placeholder*="收件人"]') ||
                             document.querySelector('textarea[name*="to"]');
                
                if (toField && toField.offsetParent !== null) {{
                    toField.value = '{safe_email}';
                    toField.dispatchEvent(new Event('input', {{bubbles: true}}));
                    toField.dispatchEvent(new Event('change', {{bubbles: true}}));
                    console.log('✅ 右侧收件人已填写');
                }} else {{
                    console.log('❌ 未找到右侧收件人字段');
                    return false;
                }}
                
                // 2. 填写主题 - 右侧界面
                var subjectField = document.querySelector('input[name*="subject"]') ||
                                  document.querySelector('input[placeholder*="主题"]');
                
                if (subjectField && subjectField.offsetParent !== null) {{
                    subjectField.value = '{safe_subject}';
                    subjectField.dispatchEvent(new Event('input', {{bubbles: true}}));
                    console.log('✅ 右侧主题已填写');
                }}
                
                // 3. 填写内容 - 右侧界面
                var contentFilled = false;
                
                // 尝试iframe编辑器
                var iframe = document.querySelector('iframe[id*="editor"], iframe[name*="content"]');
                if (iframe && iframe.offsetParent !== null) {{
                    try {{
                        var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        var body = iframeDoc.body;
                        if (body) {{
                            body.innerHTML = '{safe_content}';
                            contentFilled = true;
                            console.log('✅ 右侧iframe内容已填写');
                        }}
                    }} catch(e) {{
                        console.log('iframe访问失败:', e);
                    }}
                }}
                
                // 尝试富文本编辑器
                if (!contentFilled) {{
                    var contentDiv = document.querySelector('div[contenteditable="true"]');
                    if (contentDiv && contentDiv.offsetParent !== null) {{
                        contentDiv.innerHTML = '{safe_content}';
                        contentFilled = true;
                        console.log('✅ 右侧富文本编辑器内容已填写');
                    }}
                }}
                
                // 尝试textarea
                if (!contentFilled) {{
                    var textarea = document.querySelector('textarea[name*="content"]');
                    if (textarea && textarea.offsetParent !== null) {{
                        textarea.value = '{safe_content}';
                        contentFilled = true;
                        console.log('✅ 右侧textarea内容已填写');
                    }}
                }}
                
                // 4. 等待一下然后发送
                setTimeout(function() {{
                    var sendButton = document.querySelector('input[type="submit"][value*="发送"]') ||
                                   document.querySelector('button:contains("发送")') ||
                                   document.querySelector('input[value*="发送"]');
                    
                    if (sendButton && sendButton.offsetParent !== null) {{
                        console.log('✅ 找到右侧发送按钮，准备发送...');
                        sendButton.click();
                        console.log('✅ 右侧发送按钮已点击');
                        return true;
                    }} else {{
                        console.log('❌ 未找到右侧发送按钮');
                        return false;
                    }}
                }}, 300);
                
                return true;
                
            }} catch(e) {{
                console.error('JavaScript右侧界面发送失败:', e);
                return false;
            }}
            """
            
            # 执行JavaScript
            result = self.driver.execute_script(js_code)
            
            if result:
                time.sleep(3)  # 等待发送完成
                return self._check_send_success()
            
            return False
            
        except Exception as e:
            logger.error(f"❌ JavaScript右侧界面发送失败: {e}")
            return False
    
    def _send_with_right_panel_elements(self, to_email: str, subject: str, content: str) -> bool:
        """直接操作右侧界面元素"""
        try:
            logger.info("⚡ 直接操作右侧界面元素...")
            
            # 填写收件人
            to_field = self.find_element_by_selectors(self.selectors['to_inputs'])
            if to_field:
                to_field.clear()
                to_field.send_keys(to_email)
                logger.info("✅ 右侧收件人已填写")
            else:
                logger.error("❌ 未找到右侧收件人字段")
                return False
            
            # 填写主题
            subject_field = self.find_element_by_selectors(self.selectors['subject_inputs'])
            if subject_field:
                subject_field.clear()
                subject_field.send_keys(subject)
                logger.info("✅ 右侧主题已填写")
            
            # 填写内容
            content_filled = False
            
            # 尝试iframe
            try:
                iframe = self.driver.find_element(By.XPATH, "//iframe[contains(@id, 'editor') or contains(@name, 'content')]")
                if iframe and iframe.is_displayed():
                    self.driver.switch_to.frame(iframe)
                    body = self.driver.find_element(By.TAG_NAME, "body")
                    body.clear()
                    body.send_keys(content)
                    self.driver.switch_to.default_content()
                    content_filled = True
                    logger.info("✅ 右侧iframe内容已填写")
            except:
                pass
            
            # 尝试富文本编辑器
            if not content_filled:
                try:
                    content_div = self.driver.find_element(By.XPATH, "//div[@contenteditable='true']")
                    if content_div and content_div.is_displayed():
                        content_div.clear()
                        content_div.send_keys(content)
                        content_filled = True
                        logger.info("✅ 右侧富文本编辑器内容已填写")
                except:
                    pass
            
            # 尝试textarea
            if not content_filled:
                try:
                    textarea = self.driver.find_element(By.XPATH, "//textarea[contains(@name, 'content')]")
                    if textarea and textarea.is_displayed():
                        textarea.clear()
                        textarea.send_keys(content)
                        content_filled = True
                        logger.info("✅ 右侧textarea内容已填写")
                except:
                    pass
            
            # 发送邮件
            send_button = self.find_element_by_selectors(self.selectors['send_buttons'])
            if send_button:
                send_button.click()
                logger.info("✅ 右侧发送按钮已点击")
                time.sleep(3)
                return self._check_send_success()
            else:
                logger.error("❌ 未找到右侧发送按钮")
                return False
            
        except Exception as e:
            logger.error(f"❌ 直接操作右侧界面元素失败: {e}")
            return False
    
    def _check_send_success(self) -> bool:
        """检查发送是否成功"""
        try:
            time.sleep(2)
            page_source = self.driver.page_source.lower()
            
            success_indicators = ['发送成功', '已发送', 'sent successfully', '发送完成']
            error_indicators = ['发送失败', 'send failed', 'error', '错误']
            
            for indicator in success_indicators:
                if indicator in page_source:
                    logger.info(f"✅ 发送成功确认: {indicator}")
                    return True
            
            for indicator in error_indicators:
                if indicator in page_source:
                    logger.warning(f"❌ 发送失败确认: {indicator}")
                    return False
            
            logger.info("🤔 无明确指示器，假设发送成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 检查发送结果失败: {e}")
            return False
    
    def reset_for_next_email(self) -> bool:
        """为下一封邮件重置状态"""
        try:
            logger.info("🔄 为下一封邮件重置状态...")
            # 右侧界面通常保持显示，只需要清空表单
            return self.prepare_compose_page()
        except Exception as e:
            logger.error(f"❌ 重置状态失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取发送统计"""
        success_rate = (self.success_count / max(self.send_count, 1)) * 100
        return {
            'send_count': self.send_count,
            'success_count': self.success_count,
            'fail_count': self.send_count - self.success_count,
            'success_rate': round(success_rate, 1)
        }
