做一个windos程序
1，可以批量在浏览器自动登录新浪邮箱，导入账号+密码即可，每个账号可以单独代理ip（代理ip格式多样，可批量导入）
2，登录游览器可以设置轮换自动按设置的模版进行发送邮件
3，可以监控某个文件下面的txt文件新增的qq号码，然后转换成qq邮箱
4，实时监控新增的qq邮箱进行自动发送邮件
5，发件的登录账号邮箱按设置轮换进行发送
6，功能希望强大且智能

剔除如图我标红的“邮件发送和轻量化发送”界面模块及功能！

优化完善“多浏览器发送”模块邮件发送，开发以下：
1.开发邮件模版，制作模版的新增删除或编辑！
2.开发“主题+邮件内容”添加变量内容，提高进箱率防止落入垃圾箱！
3.开发添加邮件发送记录，添加邮件发送统计监控，可导出数据！
4.开发单个或批量发送邮件，可批量导入收件箱数据，可设置每次收件箱数量一次一个或多个！
5.开启同时发送多个邮箱时，可设置每次发送添加一个或多个固定邮箱！
6.开发用户可选择的发送模式，单个逐渐，或批量逐渐，或者其他
7.开发用户可选择的发送收件数据，导入的，还是“文件监控”的数据

导入的格式模版可以供下载，根据邮件发送，开发全面超级超级强大的“多浏览器发送系统”！