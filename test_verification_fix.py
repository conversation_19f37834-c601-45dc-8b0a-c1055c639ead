#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码检测修复测试脚本
用于验证修复后的验证码检测逻辑是否正确
"""

import time
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.models.database import DatabaseManager
from src.models.account import AccountManager
from src.core.browser_manager import BrowserManager
from src.core.stealth_login_manager import StealthLoginManager
from src.utils.logger import get_logger

logger = get_logger("VerificationFixTest")

class VerificationFixTest:
    """验证码检测修复测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        self.config = {
            'browser': {
                'implicit_wait': 3,
                'page_load_timeout': 20,
                'window_size': [1920, 1080]
            },
            'performance': {
                'max_concurrent_browsers': 1
            }
        }
        
        # 初始化数据库和账号管理器
        self.db_manager = DatabaseManager()
        self.account_manager = AccountManager(self.db_manager)
        
        # 初始化浏览器管理器和登录管理器
        self.browser_manager = BrowserManager(self.config)
        self.login_manager = StealthLoginManager(self.browser_manager, None)
        
        logger.info("🧪 验证码检测修复测试环境初始化完成")
    
    def test_verification_detection_accuracy(self):
        """测试验证码检测的准确性"""
        try:
            logger.info("🔍 开始测试验证码检测准确性...")
            
            # 模拟包含验证码的页面内容
            verification_page_content = """
            <html>
                <body>
                    <div class="login-form">
                        <input name="username" placeholder="用户名">
                        <input name="password" placeholder="密码">
                        <div class="captcha-box">
                            <span>验证码</span>
                            <div class="geetest-slider">
                                <div class="geetest-challenge">请完成验证</div>
                            </div>
                        </div>
                        <button class="loginBtn">登录</button>
                    </div>
                </body>
            </html>
            """.lower()
            
            # 测试严格验证码检测
            has_verification = self.login_manager._strict_verification_detection(verification_page_content)
            
            if has_verification:
                logger.info("✅ 验证码检测测试通过：正确识别包含验证码的页面")
            else:
                logger.error("❌ 验证码检测测试失败：未能识别包含验证码的页面")
            
            # 模拟登录成功后的页面内容
            success_page_content = """
            <html>
                <body>
                    <div class="mailbox">
                        <div class="inbox">收件箱</div>
                        <div class="compose">写邮件</div>
                        <div class="mail-list">邮件列表</div>
                        <div class="sent">已发送</div>
                    </div>
                </body>
            </html>
            """.lower()
            
            # 测试登录成功检测
            success_result = self.login_manager._enhanced_login_success_detection(
                success_page_content, 
                "https://m0.mail.sina.com.cn/classic/index.php"
            )
            
            if success_result[0]:
                logger.info("✅ 登录成功检测测试通过：正确识别登录成功页面")
            else:
                logger.error("❌ 登录成功检测测试失败：未能识别登录成功页面")
            
            return has_verification and success_result[0]
            
        except Exception as e:
            logger.error(f"❌ 验证码检测测试异常: {e}")
            return False
    
    def test_false_positive_prevention(self):
        """测试防止误判的能力"""
        try:
            logger.info("🛡️ 开始测试防止误判能力...")
            
            # 模拟仍在登录页面但URL已变化的情况
            login_page_with_verification = """
            <html>
                <body>
                    <div class="login-form">
                        <input name="freename" placeholder="邮箱地址">
                        <input name="freepassword" placeholder="密码">
                        <div class="verification-area">
                            <span>验证码</span>
                            <div class="captcha-challenge">
                                <div class="geetest-box">请拖动滑块完成验证</div>
                            </div>
                        </div>
                        <input type="submit" class="loginBtn" value="登录">
                    </div>
                </body>
            </html>
            """.lower()
            
            # 测试在有验证码的情况下是否会误判为登录成功
            success_result = self.login_manager._enhanced_login_success_detection(
                login_page_with_verification, 
                "https://mail.sina.com.cn/#"  # 这个URL容易误判
            )
            
            if not success_result[0]:
                logger.info("✅ 防误判测试通过：正确识别仍在登录页面")
                return True
            else:
                logger.error("❌ 防误判测试失败：误判登录页面为成功页面")
                return False
            
        except Exception as e:
            logger.error(f"❌ 防误判测试异常: {e}")
            return False
    
    def test_real_login_scenario(self, email: str):
        """测试真实登录场景"""
        try:
            logger.info(f"🎯 开始真实登录场景测试: {email}")
            
            # 获取账号信息
            account = self.account_manager.get_account_by_email(email)
            if not account:
                logger.error(f"❌ 未找到测试账号: {email}")
                return False
            
            # 记录开始时间
            start_time = time.time()
            
            # 执行登录测试
            success, message = self.login_manager.stealth_login(account, stealth_mode=True)
            
            # 记录结束时间
            end_time = time.time()
            total_time = end_time - start_time
            
            logger.info(f"📊 真实登录测试结果:")
            logger.info(f"   账号: {email}")
            logger.info(f"   结果: {'成功' if success else '失败'}")
            logger.info(f"   消息: {message}")
            logger.info(f"   耗时: {total_time:.2f}秒")
            
            if success:
                logger.info("✅ 真实登录测试通过：成功完成登录验证")
            else:
                logger.warning(f"⚠️ 真实登录测试结果: {message}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 真实登录测试异常: {e}")
            return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        try:
            logger.info("🚀 开始验证码检测修复综合测试")
            logger.info("=" * 60)
            
            test_results = []
            
            # 1. 验证码检测准确性测试
            logger.info("📋 测试1: 验证码检测准确性")
            result1 = self.test_verification_detection_accuracy()
            test_results.append(("验证码检测准确性", result1))
            
            # 2. 防止误判测试
            logger.info("📋 测试2: 防止误判能力")
            result2 = self.test_false_positive_prevention()
            test_results.append(("防止误判能力", result2))
            
            # 3. 真实登录场景测试（如果有测试账号）
            test_accounts = self.account_manager.get_all_accounts()
            if test_accounts:
                logger.info("📋 测试3: 真实登录场景")
                test_account = test_accounts[0]  # 使用第一个账号测试
                result3 = self.test_real_login_scenario(test_account.email)
                test_results.append(("真实登录场景", result3))
            
            # 输出测试结果
            logger.info("=" * 60)
            logger.info("📊 综合测试结果:")
            
            passed_tests = 0
            total_tests = len(test_results)
            
            for test_name, result in test_results:
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"   {test_name}: {status}")
                if result:
                    passed_tests += 1
            
            success_rate = (passed_tests / total_tests) * 100
            logger.info(f"📈 测试通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
            
            if success_rate >= 80:
                logger.info("🎉 验证码检测修复测试整体通过！")
            else:
                logger.warning("⚠️ 验证码检测修复需要进一步优化")
            
            logger.info("=" * 60)
            
        except Exception as e:
            logger.error(f"❌ 综合测试异常: {e}")
    
    def cleanup(self):
        """清理测试环境"""
        try:
            if hasattr(self, 'browser_manager'):
                self.browser_manager.close_all_drivers()
            logger.info("🧹 测试环境清理完成")
        except Exception as e:
            logger.error(f"❌ 清理异常: {e}")

def main():
    """主函数"""
    test = VerificationFixTest()
    
    try:
        # 执行综合测试
        test.run_comprehensive_test()
        
    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断测试")
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
    finally:
        test.cleanup()

if __name__ == "__main__":
    main()
