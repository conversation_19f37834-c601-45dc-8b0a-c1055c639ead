#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多邮箱发送管理器
管理多个邮箱的轮换发送、负载均衡和发送策略
"""

import random
import time
from typing import List, Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass
from src.models.account import Account
from src.utils.logger import get_logger

logger = get_logger("MultiSenderManager")


class RotationStrategy(Enum):
    """轮换策略枚举"""
    SEQUENTIAL = "sequential"      # 顺序轮换
    RANDOM = "random"             # 随机轮换
    LOAD_BALANCE = "load_balance" # 负载均衡
    SUCCESS_RATE = "success_rate" # 按成功率轮换


class IntervalStrategy(Enum):
    """间隔策略枚举"""
    FIXED = "fixed"           # 固定间隔
    RANDOM = "random"         # 随机间隔
    INCREMENTAL = "incremental" # 递增间隔
    SMART = "smart"           # 智能间隔


@dataclass
class SenderStats:
    """发送者统计信息"""
    email: str
    total_sent: int = 0
    success_count: int = 0
    failed_count: int = 0
    last_send_time: Optional[float] = None
    avg_response_time: float = 0.0
    current_load: int = 0  # 当前负载（正在发送的邮件数）
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_sent == 0:
            return 0.0
        return (self.success_count / self.total_sent) * 100
    
    @property
    def is_available(self) -> bool:
        """是否可用"""
        # 简单的可用性检查
        return self.success_rate >= 50 or self.total_sent < 10


@dataclass
class MultiSenderConfig:
    """多邮箱发送配置"""
    emails_per_sender: int = 10           # 每个邮箱发送数量
    rotation_strategy: RotationStrategy = RotationStrategy.SEQUENTIAL
    interval_strategy: IntervalStrategy = IntervalStrategy.FIXED
    base_interval: float = 2.0            # 基础间隔时间
    max_interval: float = 10.0            # 最大间隔时间
    cc_emails: List[str] = None           # 固定抄送邮箱
    enable_load_balance: bool = True      # 启用负载均衡
    max_concurrent_per_sender: int = 3    # 每个发送者最大并发数
    
    def __post_init__(self):
        if self.cc_emails is None:
            self.cc_emails = []


class MultiSenderManager:
    """多邮箱发送管理器"""
    
    def __init__(self, config: MultiSenderConfig):
        """
        初始化多邮箱发送管理器
        
        Args:
            config: 多邮箱发送配置
        """
        self.config = config
        self.accounts: List[Account] = []
        self.sender_stats: Dict[str, SenderStats] = {}
        self.current_sender_index = 0
        self.last_send_time = 0.0
        
    def set_accounts(self, accounts: List[Account]):
        """
        设置发送账号列表
        
        Args:
            accounts: 账号列表
        """
        self.accounts = accounts
        
        # 初始化统计信息
        for account in accounts:
            if account.email not in self.sender_stats:
                self.sender_stats[account.email] = SenderStats(email=account.email)
        
        logger.info(f"设置了 {len(accounts)} 个发送账号")
    
    def get_next_sender(self) -> Optional[Account]:
        """
        获取下一个发送者
        
        Returns:
            下一个发送账号或None
        """
        if not self.accounts:
            return None
        
        available_accounts = [acc for acc in self.accounts 
                            if self.sender_stats[acc.email].is_available]
        
        if not available_accounts:
            # 如果没有可用账号，重置所有账号的统计
            logger.warning("没有可用的发送账号，重置统计信息")
            for stats in self.sender_stats.values():
                stats.current_load = 0
            available_accounts = self.accounts
        
        if self.config.rotation_strategy == RotationStrategy.SEQUENTIAL:
            return self._get_sequential_sender(available_accounts)
        elif self.config.rotation_strategy == RotationStrategy.RANDOM:
            return self._get_random_sender(available_accounts)
        elif self.config.rotation_strategy == RotationStrategy.LOAD_BALANCE:
            return self._get_load_balanced_sender(available_accounts)
        elif self.config.rotation_strategy == RotationStrategy.SUCCESS_RATE:
            return self._get_success_rate_sender(available_accounts)
        else:
            return available_accounts[0]
    
    def _get_sequential_sender(self, accounts: List[Account]) -> Account:
        """顺序轮换获取发送者"""
        if self.current_sender_index >= len(accounts):
            self.current_sender_index = 0
        
        account = accounts[self.current_sender_index]
        self.current_sender_index += 1
        return account
    
    def _get_random_sender(self, accounts: List[Account]) -> Account:
        """随机获取发送者"""
        return random.choice(accounts)
    
    def _get_load_balanced_sender(self, accounts: List[Account]) -> Account:
        """负载均衡获取发送者"""
        # 选择当前负载最小的发送者
        min_load = min(self.sender_stats[acc.email].current_load for acc in accounts)
        candidates = [acc for acc in accounts 
                     if self.sender_stats[acc.email].current_load == min_load]
        return random.choice(candidates)
    
    def _get_success_rate_sender(self, accounts: List[Account]) -> Account:
        """按成功率获取发送者"""
        # 按成功率排序，优先选择成功率高的
        sorted_accounts = sorted(accounts, 
                               key=lambda acc: self.sender_stats[acc.email].success_rate,
                               reverse=True)
        
        # 使用加权随机选择，成功率高的权重大
        weights = []
        for acc in sorted_accounts:
            rate = self.sender_stats[acc.email].success_rate
            weight = max(rate, 10)  # 最小权重为10
            weights.append(weight)
        
        return random.choices(sorted_accounts, weights=weights)[0]
    
    def calculate_send_interval(self, sender_email: str) -> float:
        """
        计算发送间隔
        
        Args:
            sender_email: 发送者邮箱
        
        Returns:
            发送间隔时间（秒）
        """
        stats = self.sender_stats.get(sender_email)
        if not stats:
            return self.config.base_interval
        
        if self.config.interval_strategy == IntervalStrategy.FIXED:
            return self.config.base_interval
        
        elif self.config.interval_strategy == IntervalStrategy.RANDOM:
            return random.uniform(self.config.base_interval, self.config.max_interval)
        
        elif self.config.interval_strategy == IntervalStrategy.INCREMENTAL:
            # 根据发送数量递增间隔
            increment = min(stats.total_sent * 0.1, self.config.max_interval - self.config.base_interval)
            return self.config.base_interval + increment
        
        elif self.config.interval_strategy == IntervalStrategy.SMART:
            # 智能间隔：根据成功率和响应时间调整
            base = self.config.base_interval
            
            # 成功率低则增加间隔
            if stats.success_rate < 70:
                base *= 1.5
            elif stats.success_rate < 50:
                base *= 2.0
            
            # 响应时间长则增加间隔
            if stats.avg_response_time > 5.0:
                base *= 1.2
            
            return min(base, self.config.max_interval)
        
        return self.config.base_interval
    
    def should_wait(self, sender_email: str) -> bool:
        """
        检查是否需要等待
        
        Args:
            sender_email: 发送者邮箱
        
        Returns:
            是否需要等待
        """
        stats = self.sender_stats.get(sender_email)
        if not stats or not stats.last_send_time:
            return False
        
        interval = self.calculate_send_interval(sender_email)
        elapsed = time.time() - stats.last_send_time
        
        return elapsed < interval
    
    def get_wait_time(self, sender_email: str) -> float:
        """
        获取需要等待的时间
        
        Args:
            sender_email: 发送者邮箱
        
        Returns:
            等待时间（秒）
        """
        stats = self.sender_stats.get(sender_email)
        if not stats or not stats.last_send_time:
            return 0.0
        
        interval = self.calculate_send_interval(sender_email)
        elapsed = time.time() - stats.last_send_time
        
        return max(0, interval - elapsed)
    
    def record_send_start(self, sender_email: str):
        """
        记录发送开始
        
        Args:
            sender_email: 发送者邮箱
        """
        if sender_email not in self.sender_stats:
            self.sender_stats[sender_email] = SenderStats(email=sender_email)
        
        stats = self.sender_stats[sender_email]
        stats.current_load += 1
        stats.last_send_time = time.time()
        
        logger.debug(f"发送开始: {sender_email}, 当前负载: {stats.current_load}")
    
    def record_send_result(self, sender_email: str, success: bool, 
                          response_time: Optional[float] = None):
        """
        记录发送结果
        
        Args:
            sender_email: 发送者邮箱
            success: 是否成功
            response_time: 响应时间
        """
        if sender_email not in self.sender_stats:
            self.sender_stats[sender_email] = SenderStats(email=sender_email)
        
        stats = self.sender_stats[sender_email]
        stats.total_sent += 1
        stats.current_load = max(0, stats.current_load - 1)
        
        if success:
            stats.success_count += 1
        else:
            stats.failed_count += 1
        
        if response_time is not None:
            # 更新平均响应时间
            if stats.avg_response_time == 0:
                stats.avg_response_time = response_time
            else:
                stats.avg_response_time = (stats.avg_response_time + response_time) / 2
        
        logger.debug(f"发送结果: {sender_email}, 成功: {success}, "
                    f"成功率: {stats.success_rate:.1f}%, 当前负载: {stats.current_load}")
    
    def get_sender_statistics(self) -> Dict[str, Dict[str, Any]]:
        """
        获取发送者统计信息
        
        Returns:
            统计信息字典
        """
        result = {}
        for email, stats in self.sender_stats.items():
            result[email] = {
                'total_sent': stats.total_sent,
                'success_count': stats.success_count,
                'failed_count': stats.failed_count,
                'success_rate': round(stats.success_rate, 2),
                'avg_response_time': round(stats.avg_response_time, 3),
                'current_load': stats.current_load,
                'is_available': stats.is_available
            }
        
        return result
    
    def reset_statistics(self):
        """重置统计信息"""
        for stats in self.sender_stats.values():
            stats.total_sent = 0
            stats.success_count = 0
            stats.failed_count = 0
            stats.current_load = 0
            stats.avg_response_time = 0.0
        
        logger.info("发送统计信息已重置")
    
    def get_cc_emails(self) -> List[str]:
        """获取抄送邮箱列表"""
        return self.config.cc_emails.copy()
    
    def add_cc_email(self, email: str):
        """添加抄送邮箱"""
        if email and email not in self.config.cc_emails:
            self.config.cc_emails.append(email)
            logger.info(f"添加抄送邮箱: {email}")
    
    def remove_cc_email(self, email: str):
        """移除抄送邮箱"""
        if email in self.config.cc_emails:
            self.config.cc_emails.remove(email)
            logger.info(f"移除抄送邮箱: {email}")
    
    def can_send(self, sender_email: str) -> bool:
        """
        检查是否可以发送
        
        Args:
            sender_email: 发送者邮箱
        
        Returns:
            是否可以发送
        """
        stats = self.sender_stats.get(sender_email)
        if not stats:
            return True
        
        # 检查负载限制
        if stats.current_load >= self.config.max_concurrent_per_sender:
            return False
        
        # 检查时间间隔
        if self.should_wait(sender_email):
            return False
        
        return True
