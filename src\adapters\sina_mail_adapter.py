#!/usr/bin/env python3
"""
新浪邮箱界面适配器
专门针对新浪邮箱界面的元素识别和操作适配器
"""

import time
import re
from typing import Optional, List, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class SinaMailAdapter:
    """新浪邮箱界面适配器"""
    
    def __init__(self, driver: webdriver.Chrome):
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        
        # 新浪邮箱元素选择器配置
        self.selectors = {
            # 写邮件按钮 - 基于真实新浪邮箱界面
            'compose_buttons': [
                "//a[contains(text(), '写信')]",  # 新浪邮箱实际使用"写信"而不是"写邮件"
                "//a[contains(text(), '写邮件')]",
                "//button[contains(text(), '写信')]",
                "//button[contains(text(), '写邮件')]",
                "//div[contains(text(), '写信')]",
                "//span[contains(text(), '写信')]",
                "//a[@title='写信']",
                "//a[@title='写邮件']",
                "//a[contains(@href, 'compose')]",
                "//div[contains(@class, 'compose')]//a",
                "//div[contains(@id, 'compose')]//a",
                "//a[contains(@class, 'write')]",
                "//a[contains(@id, 'write')]"
            ],
            
            # 收件人字段
            'to_fields': [
                "//input[contains(@placeholder, '收件人')]",
                "//input[contains(@name, 'to')]",
                "//input[contains(@id, 'to')]",
                "//input[contains(@class, 'to')]",
                "//input[contains(@title, '收件人')]",
                "//textarea[contains(@placeholder, '收件人')]",
                "//textarea[contains(@name, 'to')]"
            ],
            
            # 主题字段
            'subject_fields': [
                "//input[contains(@placeholder, '主题')]",
                "//input[contains(@name, 'subject')]",
                "//input[contains(@id, 'subject')]", 
                "//input[contains(@class, 'subject')]",
                "//input[contains(@title, '主题')]"
            ],
            
            # 内容编辑器
            'content_editors': [
                # iframe编辑器
                "//iframe[contains(@id, 'editor')]",
                "//iframe[contains(@name, 'editor')]",
                "//iframe[contains(@class, 'editor')]",
                "//iframe[contains(@src, 'editor')]",
                
                # div编辑器
                "//div[@contenteditable='true']",
                "//div[contains(@class, 'editor')]",
                "//div[contains(@id, 'editor')]",
                
                # textarea编辑器
                "//textarea[contains(@name, 'content')]",
                "//textarea[contains(@id, 'content')]",
                "//textarea[contains(@class, 'content')]"
            ],
            
            # 发送按钮
            'send_buttons': [
                "//a[contains(@title, '发送')]",
                "//button[contains(@title, '发送')]",
                "//input[contains(@value, '发送')]",
                "//a[contains(text(), '发送')]",
                "//button[contains(text(), '发送')]",
                "//span[contains(text(), '发送')]",
                "//div[contains(text(), '发送')]",
                "//a[contains(@class, 'send')]",
                "//button[contains(@class, 'send')]"
            ],
            
            # 抄送字段
            'cc_fields': [
                "//input[contains(@placeholder, '抄送')]",
                "//input[contains(@name, 'cc')]",
                "//input[contains(@id, 'cc')]"
            ],
            
            # 密送字段
            'bcc_fields': [
                "//input[contains(@placeholder, '密送')]",
                "//input[contains(@name, 'bcc')]", 
                "//input[contains(@id, 'bcc')]"
            ]
        }
        
        # 成功/失败指示器
        self.indicators = {
            'success': [
                '发送成功', '已发送', 'sent successfully', 'message sent',
                '邮件已发送', '发送完成', '投递成功', '发送成功'
            ],
            'error': [
                '发送失败', 'send failed', 'error', '错误', 'failed',
                '投递失败', '发送错误', '网络错误', '服务器错误'
            ]
        }
    
    def find_element_by_selectors(self, selector_list: List[str], timeout: int = 5) -> Optional[Any]:
        """通过选择器列表查找元素"""
        for selector in selector_list:
            try:
                wait = WebDriverWait(self.driver, timeout)
                element = wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                if element and element.is_displayed():
                    logger.debug(f"✅ 找到元素: {selector}")
                    return element
            except Exception as e:
                logger.debug(f"选择器失败: {selector} - {e}")
                continue
        
        logger.warning(f"⚠️ 所有选择器都失败了: {len(selector_list)} 个")
        return None
    
    def find_clickable_element(self, selector_list: List[str], timeout: int = 5) -> Optional[Any]:
        """查找可点击的元素"""
        for selector in selector_list:
            try:
                wait = WebDriverWait(self.driver, timeout)
                element = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                if element:
                    logger.debug(f"✅ 找到可点击元素: {selector}")
                    return element
            except Exception as e:
                logger.debug(f"可点击选择器失败: {selector} - {e}")
                continue
        
        return None
    
    def click_compose_button(self) -> bool:
        """点击写邮件按钮"""
        try:
            logger.info("🔗 查找并点击写邮件按钮...")
            
            compose_button = self.find_clickable_element(self.selectors['compose_buttons'])
            if compose_button:
                compose_button.click()
                time.sleep(2)
                logger.info("✅ 写邮件按钮点击成功")
                return True
            else:
                logger.warning("⚠️ 未找到写邮件按钮")
                return False
                
        except Exception as e:
            logger.error(f"❌ 点击写邮件按钮失败: {e}")
            return False
    
    def fill_to_field(self, email: str) -> bool:
        """填写收件人字段"""
        try:
            logger.info(f"📧 填写收件人: {email}")
            
            to_field = self.find_element_by_selectors(self.selectors['to_fields'])
            if to_field:
                to_field.clear()
                to_field.send_keys(email)
                
                # 触发事件
                to_field.send_keys(Keys.TAB)
                time.sleep(0.5)
                
                logger.info("✅ 收件人填写成功")
                return True
            else:
                logger.warning("⚠️ 未找到收件人字段")
                return False
                
        except Exception as e:
            logger.error(f"❌ 填写收件人失败: {e}")
            return False
    
    def fill_subject_field(self, subject: str) -> bool:
        """填写主题字段"""
        try:
            logger.info(f"📝 填写主题: {subject}")
            
            subject_field = self.find_element_by_selectors(self.selectors['subject_fields'])
            if subject_field:
                subject_field.clear()
                subject_field.send_keys(subject)
                
                # 触发事件
                subject_field.send_keys(Keys.TAB)
                time.sleep(0.5)
                
                logger.info("✅ 主题填写成功")
                return True
            else:
                logger.warning("⚠️ 未找到主题字段")
                return False
                
        except Exception as e:
            logger.error(f"❌ 填写主题失败: {e}")
            return False
    
    def fill_content_field(self, content: str) -> bool:
        """填写内容字段"""
        try:
            logger.info("📄 填写邮件内容...")
            
            # 尝试iframe编辑器
            iframe_selectors = [s for s in self.selectors['content_editors'] if 'iframe' in s]
            for selector in iframe_selectors:
                try:
                    iframe = self.driver.find_element(By.XPATH, selector)
                    if iframe:
                        self.driver.switch_to.frame(iframe)
                        body = self.driver.find_element(By.TAG_NAME, "body")
                        if body:
                            body.clear()
                            body.send_keys(content)
                            self.driver.switch_to.default_content()
                            logger.info("✅ iframe内容填写成功")
                            return True
                except Exception as e:
                    logger.debug(f"iframe尝试失败: {e}")
                    try:
                        self.driver.switch_to.default_content()
                    except:
                        pass
                    continue
            
            # 尝试div编辑器
            div_selectors = [s for s in self.selectors['content_editors'] if 'div' in s]
            for selector in div_selectors:
                try:
                    div_element = self.driver.find_element(By.XPATH, selector)
                    if div_element and div_element.is_displayed():
                        div_element.clear()
                        div_element.send_keys(content)
                        logger.info("✅ div内容填写成功")
                        return True
                except Exception as e:
                    logger.debug(f"div尝试失败: {e}")
                    continue
            
            # 尝试textarea编辑器
            textarea_selectors = [s for s in self.selectors['content_editors'] if 'textarea' in s]
            for selector in textarea_selectors:
                try:
                    textarea = self.driver.find_element(By.XPATH, selector)
                    if textarea and textarea.is_displayed():
                        textarea.clear()
                        textarea.send_keys(content)
                        logger.info("✅ textarea内容填写成功")
                        return True
                except Exception as e:
                    logger.debug(f"textarea尝试失败: {e}")
                    continue
            
            logger.warning("⚠️ 所有内容编辑器都无法使用")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写内容失败: {e}")
            return False
    
    def click_send_button(self) -> bool:
        """点击发送按钮"""
        try:
            logger.info("📤 查找并点击发送按钮...")
            
            send_button = self.find_clickable_element(self.selectors['send_buttons'])
            if send_button:
                send_button.click()
                time.sleep(1)
                logger.info("✅ 发送按钮点击成功")
                return True
            else:
                logger.warning("⚠️ 未找到发送按钮")
                return False
                
        except Exception as e:
            logger.error(f"❌ 点击发送按钮失败: {e}")
            return False
    
    def check_send_result(self, timeout: int = 10) -> bool:
        """检查发送结果"""
        try:
            logger.info("🔍 检查发送结果...")
            
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                current_url = self.driver.current_url.lower()
                page_source = self.driver.page_source.lower()
                
                # 检查成功指示器
                for indicator in self.indicators['success']:
                    if indicator.lower() in page_source:
                        logger.info(f"✅ 发送成功确认: {indicator}")
                        return True
                
                # 检查失败指示器
                for indicator in self.indicators['error']:
                    if indicator.lower() in page_source:
                        logger.warning(f"❌ 发送失败确认: {indicator}")
                        return False
                
                # 检查URL变化
                if any(keyword in current_url for keyword in ['sent', 'success', 'complete']):
                    logger.info("✅ URL变化确认发送成功")
                    return True
                
                time.sleep(0.5)
            
            # 超时后假设成功
            logger.info("🤔 超时无明确指示器，假设发送成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 检查发送结果失败: {e}")
            return False
    
    def is_compose_page(self) -> bool:
        """检查是否在写邮件页面"""
        try:
            # 检查关键元素
            to_field = self.find_element_by_selectors(self.selectors['to_fields'], timeout=2)
            subject_field = self.find_element_by_selectors(self.selectors['subject_fields'], timeout=2)
            
            has_to = to_field is not None
            has_subject = subject_field is not None
            
            logger.debug(f"写邮件页面检查: 收件人={has_to}, 主题={has_subject}")
            
            return has_to and has_subject
            
        except Exception as e:
            logger.debug(f"写邮件页面检查失败: {e}")
            return False
    
    def get_page_info(self) -> Dict[str, Any]:
        """获取页面信息"""
        try:
            info = {
                'url': self.driver.current_url,
                'title': self.driver.title,
                'is_compose_page': self.is_compose_page(),
                'elements_found': {}
            }
            
            # 检查各种元素是否存在
            for element_type, selectors in self.selectors.items():
                element = self.find_element_by_selectors(selectors, timeout=1)
                info['elements_found'][element_type] = element is not None
            
            return info
            
        except Exception as e:
            logger.error(f"❌ 获取页面信息失败: {e}")
            return {}
    
    def navigate_to_compose(self) -> bool:
        """导航到写邮件页面"""
        try:
            logger.info("🔗 导航到写邮件页面...")
            
            # 尝试直接URL
            compose_urls = [
                "https://mail.sina.com.cn/classic/compose.php",
                "https://mail.sina.com.cn/compose.php"
            ]
            
            for url in compose_urls:
                try:
                    logger.info(f"访问: {url}")
                    self.driver.get(url)
                    time.sleep(3)
                    
                    if self.is_compose_page():
                        logger.info(f"✅ 成功导航到写邮件页面: {url}")
                        return True
                        
                except Exception as e:
                    logger.debug(f"URL导航失败: {url} - {e}")
                    continue
            
            # 尝试点击写邮件按钮
            if self.click_compose_button():
                time.sleep(2)
                if self.is_compose_page():
                    logger.info("✅ 通过按钮成功进入写邮件页面")
                    return True
            
            logger.warning("⚠️ 所有导航方法都失败了")
            return False
            
        except Exception as e:
            logger.error(f"❌ 导航到写邮件页面失败: {e}")
            return False
