#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie管理模块
实现基于Cookie的轻量化登录方案，支持100+账号同时管理而不消耗过多内存
"""

import json
import time
import pickle
import requests
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from src.utils.logger import get_logger
from src.utils.encryption import get_encryption_manager
from src.models.account import Account

logger = get_logger("CookieManager")


class CookieSession:
    """Cookie会话类"""
    
    def __init__(self, account: Account, proxy_manager=None):
        """
        初始化Cookie会话

        Args:
            account: 账号信息
            proxy_manager: 代理管理器
        """
        self.account = account
        self.session = requests.Session()
        self.cookies = {}
        self.proxy_manager = proxy_manager
        self.current_proxy = None

        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.session.headers.update(self.headers)

        # 设置代理（优先使用代理管理器）
        self._setup_proxy()

        self.login_time = None
        self.last_activity = None
        self.is_logged_in = False

    def _setup_proxy(self):
        """设置代理"""
        try:
            if self.proxy_manager:
                # 使用代理管理器获取代理
                proxy_info = self.proxy_manager.get_proxy_for_account(self.account.email)
                if proxy_info:
                    self.current_proxy = proxy_info
                    self.session.proxies = proxy_info.proxy_dict
                    logger.info(f"✅ 为账号 {self.account.email} 设置代理: {proxy_info.ip}:{proxy_info.port}")
                else:
                    logger.warning(f"⚠️ 未能为账号 {self.account.email} 获取代理")
            elif self.account.has_proxy():
                # 使用账号自带的代理配置
                proxy_info = self.account.get_proxy_info()
                if proxy_info:
                    proxy_url = f"http://{proxy_info['ip']}:{proxy_info['port']}"
                    if 'username' in proxy_info and 'password' in proxy_info:
                        proxy_url = f"http://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['ip']}:{proxy_info['port']}"

                    self.session.proxies = {
                        'http': proxy_url,
                        'https': proxy_url
                    }
                    logger.info(f"✅ 为账号 {self.account.email} 设置账号代理")

        except Exception as e:
            logger.error(f"❌ 设置代理失败: {self.account.email}, 错误: {e}")
    
    def login(self) -> Dict[str, Any]:
        """
        执行登录操作
        
        Returns:
            登录结果
        """
        result = {'success': False, 'message': '', 'cookies': None}
        
        try:
            # 访问登录页面获取初始cookies
            login_url = "https://mail.sina.com.cn/"
            response = self.session.get(login_url, timeout=30)
            
            if response.status_code != 200:
                result['message'] = f"访问登录页面失败: {response.status_code}"
                return result
            
            # 这里需要根据新浪邮箱的实际登录流程来实现
            # 由于新浪邮箱的登录机制比较复杂，这里提供一个框架
            
            # 1. 获取登录表单的必要参数
            login_data = self._prepare_login_data(response.text)
            
            # 2. 提交登录请求
            login_result = self._submit_login(login_data)
            
            if login_result['success']:
                self.cookies = dict(self.session.cookies)
                self.login_time = datetime.now()
                self.last_activity = datetime.now()
                self.is_logged_in = True
                
                result['success'] = True
                result['message'] = "登录成功"
                result['cookies'] = self.cookies
                
                logger.info(f"账号登录成功: {self.account.email}")
            else:
                result['message'] = login_result['message']
                logger.warning(f"账号登录失败: {self.account.email}, 原因: {login_result['message']}")
            
        except Exception as e:
            result['message'] = f"登录过程异常: {str(e)}"
            logger.error(f"账号登录异常: {self.account.email}, 错误: {e}")
        
        return result
    
    def _prepare_login_data(self, html_content: str) -> Dict[str, str]:
        """
        准备登录数据
        
        Args:
            html_content: 登录页面HTML内容
        
        Returns:
            登录数据字典
        """
        # 这里需要解析HTML页面，提取登录所需的参数
        # 例如：csrf_token, nonce, 等等
        
        from src.utils.encryption import get_password_manager
        password_manager = get_password_manager()
        password = password_manager.retrieve_password(self.account.password)
        
        login_data = {
            'username': self.account.email,
            'password': password,
            # 其他必要的参数需要从HTML中提取
        }
        
        return login_data
    
    def _submit_login(self, login_data: Dict[str, str]) -> Dict[str, Any]:
        """
        提交登录请求
        
        Args:
            login_data: 登录数据
        
        Returns:
            登录结果
        """
        result = {'success': False, 'message': ''}
        
        try:
            # 新浪邮箱的登录API端点
            login_api = "https://login.sina.com.cn/sso/login.php"
            
            response = self.session.post(login_api, data=login_data, timeout=30)
            
            # 检查登录是否成功
            if self._check_login_success(response):
                result['success'] = True
                result['message'] = "登录成功"
            else:
                result['message'] = "用户名或密码错误"
            
        except Exception as e:
            result['message'] = f"提交登录请求失败: {str(e)}"
        
        return result
    
    def _check_login_success(self, response: requests.Response) -> bool:
        """
        检查登录是否成功
        
        Args:
            response: 登录响应
        
        Returns:
            是否登录成功
        """
        # 检查响应状态码
        if response.status_code != 200:
            return False
        
        # 检查响应内容中的成功标识
        content = response.text.lower()
        success_indicators = ['success', '成功', 'inbox', '收件箱']
        error_indicators = ['error', '错误', 'failed', '失败', 'invalid']
        
        # 如果包含错误标识，则登录失败
        for indicator in error_indicators:
            if indicator in content:
                return False
        
        # 如果包含成功标识，则登录成功
        for indicator in success_indicators:
            if indicator in content:
                return True
        
        # 检查是否有邮箱相关的cookies
        for cookie in self.session.cookies:
            if 'mail' in cookie.name.lower() or 'sina' in cookie.name.lower():
                return True
        
        return False
    
    def send_email(self, to_email: str, subject: str, content: str) -> Dict[str, Any]:
        """
        发送邮件
        
        Args:
            to_email: 收件人
            subject: 主题
            content: 内容
        
        Returns:
            发送结果
        """
        result = {'success': False, 'message': ''}
        
        try:
            if not self.is_logged_in:
                result['message'] = "账号未登录"
                return result
            
            # 检查会话是否过期
            if self._is_session_expired():
                refresh_result = self.refresh_session()
                if not refresh_result['success']:
                    result['message'] = f"会话刷新失败: {refresh_result['message']}"
                    return result
            
            # 准备邮件数据
            email_data = {
                'to': to_email,
                'subject': subject,
                'content': content,
                # 其他必要的参数
            }
            
            # 发送邮件API
            send_api = "https://mail.sina.com.cn/classic/send.php"
            response = self.session.post(send_api, data=email_data, timeout=30)
            
            if self._check_send_success(response):
                result['success'] = True
                result['message'] = "邮件发送成功"
                self.last_activity = datetime.now()
                logger.info(f"邮件发送成功: {self.account.email} -> {to_email}")
            else:
                result['message'] = "邮件发送失败"
                logger.warning(f"邮件发送失败: {self.account.email} -> {to_email}")
            
        except Exception as e:
            result['message'] = f"发送邮件异常: {str(e)}"
            logger.error(f"发送邮件异常: {self.account.email} -> {to_email}, 错误: {e}")
        
        return result
    
    def _check_send_success(self, response: requests.Response) -> bool:
        """检查邮件发送是否成功"""
        if response.status_code != 200:
            return False
        
        content = response.text.lower()
        success_indicators = ['success', '成功', 'sent', '已发送']
        
        for indicator in success_indicators:
            if indicator in content:
                return True
        
        return False
    
    def _is_session_expired(self) -> bool:
        """检查会话是否过期"""
        if not self.last_activity:
            return True
        
        # 会话超时时间（1小时）
        timeout = timedelta(hours=1)
        return datetime.now() - self.last_activity > timeout
    
    def refresh_session(self) -> Dict[str, Any]:
        """刷新会话"""
        result = {'success': False, 'message': ''}
        
        try:
            # 访问邮箱主页来刷新会话
            inbox_url = "https://mail.sina.com.cn/classic/inbox.php"
            response = self.session.get(inbox_url, timeout=30)
            
            if response.status_code == 200:
                self.last_activity = datetime.now()
                result['success'] = True
                result['message'] = "会话刷新成功"
            else:
                # 会话已失效，需要重新登录
                login_result = self.login()
                result = login_result
            
        except Exception as e:
            result['message'] = f"刷新会话失败: {str(e)}"
        
        return result
    
    def close(self):
        """关闭会话"""
        try:
            self.session.close()
            self.is_logged_in = False
            logger.debug(f"会话已关闭: {self.account.email}")
        except Exception as e:
            logger.error(f"关闭会话失败: {self.account.email}, 错误: {e}")


class CookieManager:
    """Cookie管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Cookie管理器

        Args:
            config: 配置字典
        """
        self.config = config
        self.sessions: Dict[str, CookieSession] = {}
        self.encryption_manager = get_encryption_manager()

        # Cookie存储路径
        self.cookie_dir = Path("data/cookies")
        self.cookie_dir.mkdir(parents=True, exist_ok=True)

        # 会话池配置
        self.max_sessions = config.get('performance.max_concurrent_sessions', 100)
        self.session_timeout = config.get('session.timeout', 3600)  # 1小时

        # 代理管理器
        from .proxy_manager import ProxyManager
        self.proxy_manager = ProxyManager()

        logger.info("Cookie管理器初始化完成")
    
    def create_session(self, account: Account) -> str:
        """
        创建会话
        
        Args:
            account: 账号信息
        
        Returns:
            会话ID
        """
        session_id = f"session_{account.email}_{int(time.time())}"
        
        # 检查会话数量限制
        if len(self.sessions) >= self.max_sessions:
            self._cleanup_expired_sessions()
            
            if len(self.sessions) >= self.max_sessions:
                # 移除最旧的会话
                oldest_session_id = min(self.sessions.keys(), 
                                      key=lambda x: self.sessions[x].last_activity or datetime.min)
                self.close_session(oldest_session_id)
        
        # 创建新会话（传递代理管理器）
        session = CookieSession(account, self.proxy_manager)
        self.sessions[session_id] = session
        
        logger.info(f"会话创建成功: {session_id}")
        return session_id
    
    def login_session(self, session_id: str) -> Dict[str, Any]:
        """
        登录会话
        
        Args:
            session_id: 会话ID
        
        Returns:
            登录结果
        """
        if session_id not in self.sessions:
            return {'success': False, 'message': '会话不存在'}
        
        session = self.sessions[session_id]
        
        # 尝试加载已保存的cookies
        if self._load_cookies(session):
            logger.info(f"使用已保存的cookies登录: {session.account.email}")
            return {'success': True, 'message': '使用cookies登录成功'}
        
        # 执行完整登录流程
        result = session.login()
        
        if result['success']:
            # 保存cookies
            self._save_cookies(session)
        
        return result
    
    def send_email_with_session(self, session_id: str, to_email: str, 
                               subject: str, content: str) -> Dict[str, Any]:
        """
        使用会话发送邮件
        
        Args:
            session_id: 会话ID
            to_email: 收件人
            subject: 主题
            content: 内容
        
        Returns:
            发送结果
        """
        if session_id not in self.sessions:
            return {'success': False, 'message': '会话不存在'}
        
        session = self.sessions[session_id]
        return session.send_email(to_email, subject, content)
    
    def close_session(self, session_id: str):
        """关闭会话"""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            session.close()
            del self.sessions[session_id]
            logger.info(f"会话已关闭: {session_id}")
    
    def _load_cookies(self, session: CookieSession) -> bool:
        """
        加载已保存的cookies
        
        Args:
            session: 会话对象
        
        Returns:
            是否加载成功
        """
        try:
            cookie_file = self.cookie_dir / f"{session.account.email}.cookies"
            
            if not cookie_file.exists():
                return False
            
            # 读取加密的cookies
            with open(cookie_file, 'rb') as f:
                encrypted_data = f.read()
            
            # 解密cookies
            decrypted_data = self.encryption_manager.decrypt(encrypted_data.decode('utf-8'))
            cookies_data = json.loads(decrypted_data)
            
            # 检查cookies是否过期
            save_time = datetime.fromisoformat(cookies_data['save_time'])
            if datetime.now() - save_time > timedelta(hours=self.session_timeout / 3600):
                return False
            
            # 设置cookies到session
            for cookie in cookies_data['cookies']:
                session.session.cookies.set(**cookie)
            
            session.cookies = cookies_data['cookies']
            session.login_time = save_time
            session.last_activity = datetime.now()
            session.is_logged_in = True
            
            return True
            
        except Exception as e:
            logger.error(f"加载cookies失败: {session.account.email}, 错误: {e}")
            return False
    
    def _save_cookies(self, session: CookieSession):
        """
        保存cookies
        
        Args:
            session: 会话对象
        """
        try:
            cookie_file = self.cookie_dir / f"{session.account.email}.cookies"
            
            # 准备cookies数据
            cookies_data = {
                'save_time': datetime.now().isoformat(),
                'cookies': []
            }
            
            for cookie in session.session.cookies:
                cookies_data['cookies'].append({
                    'name': cookie.name,
                    'value': cookie.value,
                    'domain': cookie.domain,
                    'path': cookie.path
                })
            
            # 加密并保存
            json_data = json.dumps(cookies_data)
            encrypted_data = self.encryption_manager.encrypt(json_data)
            
            with open(cookie_file, 'wb') as f:
                f.write(encrypted_data.encode('utf-8'))
            
            logger.debug(f"Cookies保存成功: {session.account.email}")
            
        except Exception as e:
            logger.error(f"保存cookies失败: {session.account.email}, 错误: {e}")
    
    def _cleanup_expired_sessions(self):
        """清理过期的会话"""
        expired_sessions = []
        
        for session_id, session in self.sessions.items():
            if session._is_session_expired():
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.close_session(session_id)
        
        logger.info(f"清理过期会话: {len(expired_sessions)} 个")
    
    def get_session_count(self) -> int:
        """获取当前会话数量"""
        return len(self.sessions)
    
    def get_session_info(self) -> List[Dict[str, Any]]:
        """获取会话信息"""
        info_list = []
        
        for session_id, session in self.sessions.items():
            info = {
                'session_id': session_id,
                'email': session.account.email,
                'is_logged_in': session.is_logged_in,
                'login_time': session.login_time.isoformat() if session.login_time else None,
                'last_activity': session.last_activity.isoformat() if session.last_activity else None
            }
            info_list.append(info)
        
        return info_list
    
    def close_all_sessions(self):
        """关闭所有会话"""
        session_ids = list(self.sessions.keys())
        for session_id in session_ids:
            self.close_session(session_id)
        
        logger.info("所有会话已关闭")

    def list_cookie_files(self) -> List[str]:
        """
        列出所有有Cookie文件的账号邮箱

        Returns:
            账号邮箱列表
        """
        try:
            import os
            cookie_files = []

            if not os.path.exists(self.cookie_dir):
                return cookie_files

            # 遍历Cookie目录
            for filename in os.listdir(self.cookie_dir):
                if filename.endswith('.cookies'):
                    # 提取邮箱地址（去掉.cookies后缀并转换格式）
                    email_part = filename[:-8]  # 移除.cookies
                    # 将下划线格式转换为邮箱格式
                    # 例如: ceik42_sina_com -> <EMAIL>
                    if '_' in email_part:
                        parts = email_part.split('_')
                        if len(parts) >= 3:
                            username = parts[0]
                            domain_parts = parts[1:]
                            domain = '.'.join(domain_parts)
                            email = f"{username}@{domain}"
                            cookie_files.append(email)

            logger.debug(f"找到 {len(cookie_files)} 个Cookie文件")
            return cookie_files

        except Exception as e:
            logger.error(f"列出Cookie文件失败: {e}")
            return []

    def get_cookies(self, account_email: str) -> Optional[Dict]:
        """
        获取账号的Cookie信息（兼容轻量化发送器）

        Args:
            account_email: 账号邮箱

        Returns:
            包含cookies和session信息的字典，如果没有找到则返回None
        """
        try:
            logger.info(f"获取Cookie信息: {account_email}")

            # 查找对应的会话
            for session_id, session in self.sessions.items():
                if session.account.email == account_email:
                    # 检查会话是否有效
                    if session.is_logged_in and session.cookies:
                        # 转换Cookie格式为轻量化发送器期望的格式
                        cookies_list = []
                        for name, value in session.cookies.items():
                            cookie_dict = {
                                'name': name,
                                'value': value,
                                'domain': '.sina.com.cn',
                                'path': '/'
                            }
                            cookies_list.append(cookie_dict)

                        # 构建会话信息
                        session_info = {
                            'current_url': 'https://mail.sina.com.cn',
                            'user_agent': session.headers.get('User-Agent', ''),
                            'session_id': session_id,
                            'login_time': session.login_time.isoformat() if session.login_time else None,
                            'last_activity': session.last_activity.isoformat() if session.last_activity else None
                        }

                        result = {
                            'cookies': cookies_list,
                            'session': session_info
                        }

                        logger.info(f"✅ 找到Cookie信息: {account_email}, {len(cookies_list)} 个Cookie")
                        return result

            # 如果没有找到活跃会话，尝试从文件加载
            cookie_file = self.cookie_dir / f"{account_email.replace('@', '_').replace('.', '_')}.cookies"
            if cookie_file.exists():
                try:
                    # 检查文件是否为空
                    if cookie_file.stat().st_size == 0:
                        logger.warning(f"Cookie文件为空: {cookie_file}")
                        return None

                    with open(cookie_file, 'r', encoding='utf-8') as f:
                        encrypted_data = f.read().strip()

                    if not encrypted_data:
                        logger.warning(f"Cookie文件内容为空: {cookie_file}")
                        return None

                    # 解密Cookie数据
                    decrypted_data = self.encryption_manager.decrypt(encrypted_data)
                    cookie_data = json.loads(decrypted_data)

                    # 转换为期望的格式
                    if 'cookies' in cookie_data and cookie_data['cookies']:
                        result = {
                            'cookies': cookie_data['cookies'],
                            'session': {
                                'current_url': 'https://mail.sina.com.cn',
                                'load_time': cookie_data.get('save_time'),
                                'source': 'file'
                            }
                        }

                        logger.info(f"✅ 从文件加载Cookie: {account_email}, {len(cookie_data['cookies'])} 个Cookie")
                        return result
                    else:
                        logger.warning(f"Cookie文件格式无效: {cookie_file}")
                        return None

                except Exception as e:
                    logger.error(f"从文件加载Cookie失败: {e}")
                    # 尝试删除损坏的Cookie文件
                    try:
                        cookie_file.unlink()
                        logger.info(f"已删除损坏的Cookie文件: {cookie_file}")
                    except:
                        pass

            logger.warning(f"⚠️ 未找到Cookie信息: {account_email}")
            return None

        except Exception as e:
            logger.error(f"❌ 获取Cookie失败: {account_email}, 错误: {e}")
            return None

    def extract_cookies_from_driver(self, driver, account_email: str) -> bool:
        """
        从浏览器驱动中提取Cookie并保存

        Args:
            driver: 浏览器驱动
            account_email: 账号邮箱

        Returns:
            提取是否成功
        """
        try:
            logger.info(f"从浏览器提取Cookie: {account_email}")

            # 获取所有Cookie
            cookies = driver.get_cookies()

            # 转换为标准格式
            cookies_list = []
            for cookie in cookies:
                cookie_dict = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie.get('domain', '.sina.com.cn'),
                    'path': cookie.get('path', '/'),
                    'secure': cookie.get('secure', False),
                    'httpOnly': cookie.get('httpOnly', False)
                }
                cookies_list.append(cookie_dict)

            # 获取会话信息
            session_info = {
                'current_url': driver.current_url,
                'user_agent': driver.execute_script("return navigator.userAgent;"),
                'page_title': driver.title,
                'timestamp': datetime.now().isoformat()
            }

            # 构建完整的Cookie数据
            cookies_data = {
                'save_time': datetime.now().isoformat(),
                'cookies': cookies_list,
                'session': session_info
            }

            # 保存到文件
            cookie_file = self.cookie_dir / f"{account_email.replace('@', '_').replace('.', '_')}.cookies"

            try:
                # 确保目录存在
                cookie_file.parent.mkdir(parents=True, exist_ok=True)

                # 加密并保存
                json_data = json.dumps(cookies_data, ensure_ascii=False, indent=2)
                encrypted_data = self.encryption_manager.encrypt(json_data)

                # 写入文件
                with open(cookie_file, 'w', encoding='utf-8') as f:
                    f.write(encrypted_data)

                # 验证文件是否正确保存
                if cookie_file.exists() and cookie_file.stat().st_size > 0:
                    logger.info(f"✅ 成功提取并保存Cookie: {account_email}, {len(cookies_list)} 个Cookie")
                    logger.info(f"📁 Cookie文件路径: {cookie_file}")
                    return True
                else:
                    logger.error(f"❌ Cookie文件保存失败: {cookie_file}")
                    return False

            except Exception as save_error:
                logger.error(f"❌ Cookie文件保存异常: {save_error}")
                return False

        except Exception as e:
            logger.error(f"❌ 从浏览器提取Cookie失败: {account_email}, 错误: {e}")
            return False
