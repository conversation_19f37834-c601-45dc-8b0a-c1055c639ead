#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志系统模块
提供统一的日志记录功能，支持文件和控制台输出
"""

import os
import sys
from pathlib import Path
from loguru import logger
from typing import Optional


def setup_logger(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    max_file_size: str = "10MB",
    backup_count: int = 5
) -> logger:
    """
    设置日志系统
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件路径，如果为None则使用默认路径
        max_file_size: 单个日志文件最大大小
        backup_count: 保留的日志文件数量
    
    Returns:
        配置好的logger实例
    """
    # 移除默认的控制台处理器
    logger.remove()
    
    # 设置日志格式
    log_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        format=log_format,
        level=log_level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 设置日志文件路径
    if log_file is None:
        project_root = Path(__file__).parent.parent.parent
        logs_dir = project_root / "logs"
        logs_dir.mkdir(exist_ok=True)
        log_file = logs_dir / "sina_email_automation.log"
    
    # 添加文件输出
    logger.add(
        log_file,
        format=log_format,
        level=log_level,
        rotation=max_file_size,
        retention=backup_count,
        compression="zip",
        encoding="utf-8",
        backtrace=True,
        diagnose=True
    )
    
    # 添加错误日志文件
    error_log_file = str(log_file).replace(".log", "_error.log")
    logger.add(
        error_log_file,
        format=log_format,
        level="ERROR",
        rotation=max_file_size,
        retention=backup_count,
        compression="zip",
        encoding="utf-8",
        backtrace=True,
        diagnose=True
    )
    
    logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    return logger


def get_logger(name: str = None) -> logger:
    """
    获取logger实例
    
    Args:
        name: logger名称
    
    Returns:
        logger实例
    """
    if name:
        return logger.bind(name=name)
    return logger


# 创建默认logger实例
default_logger = get_logger("SinaEmailAutomation")
