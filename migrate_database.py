#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本
用于更新现有数据库表结构，添加新字段
"""

import sqlite3
import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import get_logger

logger = get_logger("DatabaseMigration")


def migrate_send_records_table(db_path: str):
    """迁移发送记录表，添加新字段"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='send_records'
        """)
        
        if not cursor.fetchone():
            logger.info("send_records 表不存在，跳过迁移")
            conn.close()
            return
        
        # 获取当前表结构
        cursor.execute("PRAGMA table_info(send_records)")
        columns = [row[1] for row in cursor.fetchall()]
        logger.info(f"当前 send_records 表字段: {columns}")
        
        # 需要添加的新字段
        new_fields = [
            ("task_id", "TEXT"),
            ("content", "TEXT"),
            ("content_type", "TEXT DEFAULT 'text/plain'"),
            ("response_time", "REAL"),
            ("browser_id", "TEXT"),
            ("proxy_ip", "TEXT"),
            ("retry_count", "INTEGER DEFAULT 0"),
            ("create_time", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
            ("update_time", "DATETIME DEFAULT CURRENT_TIMESTAMP")
        ]
        
        # 添加缺失的字段
        for field_name, field_type in new_fields:
            if field_name not in columns:
                try:
                    alter_sql = f"ALTER TABLE send_records ADD COLUMN {field_name} {field_type}"
                    cursor.execute(alter_sql)
                    logger.info(f"添加字段 {field_name} 到 send_records 表")
                except sqlite3.Error as e:
                    logger.warning(f"添加字段 {field_name} 失败: {e}")
        
        # 创建索引
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_send_records_task_id ON send_records(task_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_send_records_from_email ON send_records(from_email)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_send_records_status ON send_records(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_send_records_send_time ON send_records(send_time)")
            logger.info("创建 send_records 表索引完成")
        except sqlite3.Error as e:
            logger.warning(f"创建索引失败: {e}")
        
        conn.commit()
        conn.close()
        logger.info("send_records 表迁移完成")
        
    except Exception as e:
        logger.error(f"迁移 send_records 表失败: {e}")


def migrate_email_templates_table(db_path: str):
    """迁移邮件模板表，添加新字段"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='email_templates'
        """)
        
        if not cursor.fetchone():
            logger.info("email_templates 表不存在，跳过迁移")
            conn.close()
            return
        
        # 获取当前表结构
        cursor.execute("PRAGMA table_info(email_templates)")
        columns = [row[1] for row in cursor.fetchall()]
        logger.info(f"当前 email_templates 表字段: {columns}")
        
        # 需要添加的新字段
        new_fields = [
            ("content_type", "TEXT DEFAULT 'text/plain'"),
            ("variables", "TEXT DEFAULT ''")
        ]
        
        # 添加缺失的字段
        for field_name, field_type in new_fields:
            if field_name not in columns:
                try:
                    alter_sql = f"ALTER TABLE email_templates ADD COLUMN {field_name} {field_type}"
                    cursor.execute(alter_sql)
                    logger.info(f"添加字段 {field_name} 到 email_templates 表")
                except sqlite3.Error as e:
                    logger.warning(f"添加字段 {field_name} 失败: {e}")
        
        conn.commit()
        conn.close()
        logger.info("email_templates 表迁移完成")
        
    except Exception as e:
        logger.error(f"迁移 email_templates 表失败: {e}")


def backup_database(db_path: str):
    """备份数据库"""
    try:
        if not os.path.exists(db_path):
            logger.info("数据库文件不存在，无需备份")
            return None
        
        backup_path = f"{db_path}.backup"
        
        # 如果备份文件已存在，添加时间戳
        if os.path.exists(backup_path):
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{db_path}.backup_{timestamp}"
        
        # 复制数据库文件
        import shutil
        shutil.copy2(db_path, backup_path)
        logger.info(f"数据库备份完成: {backup_path}")
        return backup_path
        
    except Exception as e:
        logger.error(f"备份数据库失败: {e}")
        return None


def main():
    """主函数"""
    print("🔄 数据库迁移工具")
    print("=" * 50)
    
    # 查找数据库文件
    possible_db_paths = [
        "data/sina_email_automation.db",
        "data/sina_email.db",
        "sina_email.db",
        "database.db"
    ]
    
    db_path = None
    for path in possible_db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ 未找到数据库文件")
        print("请确保数据库文件存在于以下位置之一:")
        for path in possible_db_paths:
            print(f"  - {path}")
        return
    
    print(f"📁 找到数据库文件: {db_path}")
    
    # 备份数据库
    print("💾 备份数据库...")
    backup_path = backup_database(db_path)
    if backup_path:
        print(f"✅ 备份完成: {backup_path}")
    else:
        print("⚠️ 备份失败，但继续迁移")
    
    # 执行迁移
    print("🔄 开始数据库迁移...")
    
    print("  📋 迁移 email_templates 表...")
    migrate_email_templates_table(db_path)
    
    print("  📊 迁移 send_records 表...")
    migrate_send_records_table(db_path)
    
    print("✅ 数据库迁移完成！")
    print("\n🎉 现在可以重新启动应用程序了")
    print("运行命令: python main.py")


if __name__ == "__main__":
    main()
