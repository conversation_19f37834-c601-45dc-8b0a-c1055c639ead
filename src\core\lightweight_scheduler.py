#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轻量化邮件发送调度器
基于Cookie会话的轻量化邮件发送方案，支持100+账号同时管理
"""

import time
import random
import threading
from datetime import datetime
from typing import Dict, Any, List, Optional
from queue import Queue, Empty
from dataclasses import dataclass
from enum import Enum
from src.core.cookie_manager import CookieManager
from src.models.account import Account, AccountManager
from src.models.email_template import EmailTemplate, EmailTemplateManager
from src.models.database import DatabaseManager
from src.utils.logger import get_logger

logger = get_logger("LightweightScheduler")


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"


@dataclass
class LightweightEmailTask:
    """轻量化邮件任务"""
    id: str
    account_email: str
    to_email: str
    subject: str
    content: str
    template_id: Optional[int] = None
    priority: int = 0
    max_retry: int = 3
    retry_count: int = 0
    status: TaskStatus = TaskStatus.PENDING
    create_time: datetime = None
    send_time: Optional[datetime] = None
    error_message: str = ""
    
    def __post_init__(self):
        if self.create_time is None:
            self.create_time = datetime.now()


class LightweightEmailScheduler:
    """轻量化邮件发送调度器"""
    
    def __init__(self, config: Dict[str, Any], db_manager: DatabaseManager):
        """
        初始化轻量化调度器
        
        Args:
            config: 配置字典
            db_manager: 数据库管理器
        """
        self.config = config
        self.db_manager = db_manager
        
        # 管理器实例
        self.account_manager = AccountManager(db_manager)
        self.template_manager = EmailTemplateManager(db_manager)
        self.cookie_manager = CookieManager(config)
        
        # 任务队列
        self.task_queue = Queue()
        self.failed_queue = Queue()
        
        # 账号会话池
        self.account_sessions: Dict[str, str] = {}  # email -> session_id
        self.session_usage: Dict[str, int] = {}  # session_id -> usage_count
        
        # 发送配置
        self.email_config = config.get('email', {})
        self.send_interval_min = self.email_config.get('send_interval_min', 3)
        self.send_interval_max = self.email_config.get('send_interval_max', 8)
        self.max_retry_count = self.email_config.get('max_retry_count', 3)
        self.batch_size = self.email_config.get('batch_size', 20)
        self.daily_limit = self.email_config.get('daily_limit', 200)
        
        # 轻量化配置
        self.max_concurrent_sessions = config.get('performance.max_concurrent_sessions', 100)
        self.session_reuse_limit = config.get('performance.session_reuse_limit', 50)  # 每个会话最多发送50封邮件
        
        # 状态跟踪
        self.is_running = False
        self.worker_threads = []
        self.max_workers = min(config.get('performance.max_concurrent_emails', 10), 20)  # 限制最大工作线程
        self.current_account_index = 0
        self.daily_send_count = 0
        self.last_reset_date = datetime.now().date()
        
        # 统计信息
        self.stats = {
            'total_sent': 0,
            'total_failed': 0,
            'total_retry': 0,
            'daily_sent': 0,
            'active_sessions': 0,
            'memory_usage': 0
        }
        
        # 回调函数
        self.status_callback: Optional[callable] = None
        self.progress_callback: Optional[callable] = None
        
        logger.info("轻量化邮件发送调度器初始化完成")
    
    def add_task(self, to_email: str, subject: str = None, content: str = None,
                template_id: int = None, template_variables: Dict[str, str] = None,
                priority: int = 0, preferred_account: str = None) -> str:
        """
        添加邮件发送任务
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
            template_id: 模板ID
            template_variables: 模板变量
            priority: 优先级
            preferred_account: 首选发送账号
        
        Returns:
            任务ID
        """
        task_id = f"task_{int(time.time())}_{random.randint(1000, 9999)}"
        
        # 选择发送账号
        account_email = preferred_account or self._select_account()
        if not account_email:
            raise Exception("没有可用的发送账号")
        
        # 处理模板
        if template_id:
            template = self.template_manager.get_template_by_id(template_id)
            if template:
                variables = template_variables or {}
                rendered = template.render(variables)
                subject = rendered['subject']
                content = rendered['content']
        
        # 使用默认模板（如果需要）
        if not subject or not content:
            default_template = self.template_manager.get_default_template()
            if default_template:
                variables = template_variables or {}
                rendered = default_template.render(variables)
                subject = subject or rendered['subject']
                content = content or rendered['content']
                template_id = default_template.id
        
        task = LightweightEmailTask(
            id=task_id,
            account_email=account_email,
            to_email=to_email,
            subject=subject or "无主题",
            content=content or "无内容",
            template_id=template_id,
            priority=priority
        )
        
        self.task_queue.put(task)
        logger.info(f"轻量化邮件任务已添加: {task_id} -> {to_email} (使用账号: {account_email})")
        
        return task_id
    
    def start(self):
        """启动轻量化调度器"""
        if self.is_running:
            logger.warning("轻量化调度器已在运行")
            return
        
        self.is_running = True
        self._reset_daily_count()
        
        # 预创建会话池
        self._initialize_session_pool()
        
        # 启动工作线程
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker_thread,
                name=f"LightweightWorker-{i+1}",
                daemon=True
            )
            worker.start()
            self.worker_threads.append(worker)
        
        # 启动会话管理线程
        session_manager = threading.Thread(
            target=self._session_manager_thread,
            name="SessionManager",
            daemon=True
        )
        session_manager.start()
        self.worker_threads.append(session_manager)
        
        logger.info(f"轻量化邮件发送调度器已启动，工作线程数: {self.max_workers}")
        
        if self.status_callback:
            self.status_callback("轻量化调度器已启动")
    
    def stop(self):
        """停止轻量化调度器"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 等待工作线程结束
        for worker in self.worker_threads:
            worker.join(timeout=5)
        
        self.worker_threads.clear()
        
        # 关闭所有会话
        self.cookie_manager.close_all_sessions()
        self.account_sessions.clear()
        self.session_usage.clear()
        
        logger.info("轻量化邮件发送调度器已停止")
        
        if self.status_callback:
            self.status_callback("轻量化调度器已停止")
    
    def _initialize_session_pool(self):
        """初始化会话池"""
        try:
            accounts = self.account_manager.get_available_accounts()
            
            # 限制初始会话数量
            max_initial_sessions = min(len(accounts), self.max_concurrent_sessions // 2)
            
            for i, account in enumerate(accounts[:max_initial_sessions]):
                try:
                    session_id = self.cookie_manager.create_session(account)
                    login_result = self.cookie_manager.login_session(session_id)
                    
                    if login_result['success']:
                        self.account_sessions[account.email] = session_id
                        self.session_usage[session_id] = 0
                        logger.info(f"会话初始化成功: {account.email}")
                    else:
                        self.cookie_manager.close_session(session_id)
                        logger.warning(f"会话初始化失败: {account.email}, 原因: {login_result['message']}")
                
                except Exception as e:
                    logger.error(f"初始化会话异常: {account.email}, 错误: {e}")
            
            self.stats['active_sessions'] = len(self.account_sessions)
            logger.info(f"会话池初始化完成，活跃会话数: {self.stats['active_sessions']}")
            
        except Exception as e:
            logger.error(f"初始化会话池失败: {e}")
    
    def _worker_thread(self):
        """工作线程主循环"""
        thread_name = threading.current_thread().name
        logger.info(f"轻量化工作线程启动: {thread_name}")
        
        while self.is_running:
            try:
                # 检查每日发送限制
                if self._check_daily_limit():
                    time.sleep(60)
                    continue
                
                # 获取任务
                task = self._get_next_task()
                if not task:
                    time.sleep(1)
                    continue
                
                # 执行发送任务
                self._execute_lightweight_task(task)
                
                # 发送间隔
                interval = random.uniform(self.send_interval_min, self.send_interval_max)
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"轻量化工作线程异常: {thread_name}, 错误: {e}")
                time.sleep(5)
        
        logger.info(f"轻量化工作线程结束: {thread_name}")
    
    def _session_manager_thread(self):
        """会话管理线程"""
        logger.info("会话管理线程启动")
        
        while self.is_running:
            try:
                # 清理过期会话
                self._cleanup_sessions()
                
                # 更新统计信息
                self.stats['active_sessions'] = len(self.account_sessions)
                
                # 每30秒检查一次
                time.sleep(30)
                
            except Exception as e:
                logger.error(f"会话管理线程异常: {e}")
                time.sleep(10)
        
        logger.info("会话管理线程结束")
    
    def _execute_lightweight_task(self, task: LightweightEmailTask):
        """
        执行轻量化邮件发送任务
        
        Args:
            task: 邮件任务
        """
        task.status = TaskStatus.PROCESSING
        task.send_time = datetime.now()
        
        try:
            # 获取或创建会话
            session_id = self._get_or_create_session(task.account_email)
            if not session_id:
                raise Exception(f"无法获取账号会话: {task.account_email}")
            
            # 发送邮件
            send_result = self.cookie_manager.send_email_with_session(
                session_id=session_id,
                to_email=task.to_email,
                subject=task.subject,
                content=task.content
            )
            
            if send_result['success']:
                # 发送成功
                task.status = TaskStatus.SUCCESS
                self.stats['total_sent'] += 1
                self.stats['daily_sent'] += 1
                self.daily_send_count += 1
                
                # 更新会话使用计数
                self.session_usage[session_id] = self.session_usage.get(session_id, 0) + 1
                
                # 更新账号使用统计
                account = self.account_manager.get_account_by_email(task.account_email)
                if account:
                    self.account_manager.increment_send_count(account.id)
                
                # 记录发送结果
                self._record_send_result(task, True, "")
                
                logger.info(f"轻量化邮件发送成功: {task.id} -> {task.to_email}")
                
                # 检查会话是否需要重置
                if self.session_usage[session_id] >= self.session_reuse_limit:
                    self._reset_session(task.account_email, session_id)
                
            else:
                raise Exception(send_result['message'])
            
        except Exception as e:
            # 发送失败
            task.error_message = str(e)
            task.retry_count += 1
            
            if task.retry_count < task.max_retry:
                # 重试
                task.status = TaskStatus.RETRY
                self.failed_queue.put(task)
                self.stats['total_retry'] += 1
                logger.warning(f"轻量化邮件发送失败，将重试: {task.id} -> {task.to_email}, 错误: {e}")
            else:
                # 彻底失败
                task.status = TaskStatus.FAILED
                self.stats['total_failed'] += 1
                
                # 记录发送记录
                self._record_send_result(task, False, str(e))
                
                logger.error(f"轻量化邮件发送失败: {task.id} -> {task.to_email}, 错误: {e}")
        
        # 更新进度
        if self.progress_callback:
            self.progress_callback(self.stats)
    
    def _get_or_create_session(self, account_email: str) -> Optional[str]:
        """获取或创建会话"""
        try:
            # 检查是否已有会话
            if account_email in self.account_sessions:
                session_id = self.account_sessions[account_email]
                
                # 检查会话是否有效
                session_info = self.cookie_manager.sessions.get(session_id)
                if session_info and session_info.is_logged_in:
                    return session_id
                else:
                    # 会话无效，移除
                    del self.account_sessions[account_email]
                    if session_id in self.session_usage:
                        del self.session_usage[session_id]
            
            # 创建新会话
            account = self.account_manager.get_account_by_email(account_email)
            if not account:
                return None
            
            session_id = self.cookie_manager.create_session(account)
            login_result = self.cookie_manager.login_session(session_id)
            
            if login_result['success']:
                self.account_sessions[account_email] = session_id
                self.session_usage[session_id] = 0
                return session_id
            else:
                self.cookie_manager.close_session(session_id)
                return None
            
        except Exception as e:
            logger.error(f"获取或创建会话失败: {account_email}, 错误: {e}")
            return None
    
    def _reset_session(self, account_email: str, session_id: str):
        """重置会话"""
        try:
            # 关闭旧会话
            self.cookie_manager.close_session(session_id)
            
            # 从映射中移除
            if account_email in self.account_sessions:
                del self.account_sessions[account_email]
            if session_id in self.session_usage:
                del self.session_usage[session_id]
            
            logger.info(f"会话已重置: {account_email}")
            
        except Exception as e:
            logger.error(f"重置会话失败: {account_email}, 错误: {e}")
    
    def _cleanup_sessions(self):
        """清理会话"""
        try:
            expired_accounts = []
            
            for account_email, session_id in self.account_sessions.items():
                session = self.cookie_manager.sessions.get(session_id)
                if not session or session._is_session_expired():
                    expired_accounts.append(account_email)
            
            for account_email in expired_accounts:
                session_id = self.account_sessions.get(account_email)
                if session_id:
                    self._reset_session(account_email, session_id)
            
            if expired_accounts:
                logger.info(f"清理过期会话: {len(expired_accounts)} 个")
            
        except Exception as e:
            logger.error(f"清理会话失败: {e}")
    
    def _select_account(self) -> Optional[str]:
        """选择发送账号"""
        accounts = self.account_manager.get_available_accounts()
        if not accounts:
            return None
        
        # 轮换选择账号
        account = accounts[self.current_account_index % len(accounts)]
        self.current_account_index += 1
        
        return account.email
    
    def _get_next_task(self) -> Optional[LightweightEmailTask]:
        """获取下一个任务"""
        try:
            # 优先处理重试任务
            if not self.failed_queue.empty():
                return self.failed_queue.get_nowait()
            
            # 获取普通任务
            return self.task_queue.get_nowait()
            
        except Empty:
            return None
    
    def _check_daily_limit(self) -> bool:
        """检查是否达到每日发送限制"""
        self._reset_daily_count()
        return self.daily_send_count >= self.daily_limit
    
    def _reset_daily_count(self):
        """重置每日计数"""
        today = datetime.now().date()
        if today != self.last_reset_date:
            self.daily_send_count = 0
            self.stats['daily_sent'] = 0
            self.last_reset_date = today
            logger.info("每日发送计数已重置")
    
    def _record_send_result(self, task: LightweightEmailTask, success: bool, error_msg: str):
        """记录发送结果到数据库"""
        try:
            query = """
                INSERT INTO send_records (
                    from_email, to_email, subject, template_id, 
                    status, error_msg
                ) VALUES (?, ?, ?, ?, ?, ?)
            """
            status = "success" if success else "failed"
            params = (
                task.account_email,
                task.to_email,
                task.subject,
                task.template_id,
                status,
                error_msg
            )
            
            self.db_manager.execute_insert(query, params)
            
        except Exception as e:
            logger.error(f"记录发送结果失败: {e}")
    
    def get_queue_size(self) -> Dict[str, int]:
        """获取队列大小"""
        return {
            'pending': self.task_queue.qsize(),
            'failed': self.failed_queue.qsize()
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats['session_count'] = self.cookie_manager.get_session_count()
        return stats
    
    def get_session_info(self) -> List[Dict[str, Any]]:
        """获取会话信息"""
        return self.cookie_manager.get_session_info()
    
    def clear_queue(self):
        """清空任务队列"""
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
            except Empty:
                break
        
        while not self.failed_queue.empty():
            try:
                self.failed_queue.get_nowait()
            except Empty:
                break
        
        logger.info("轻量化任务队列已清空")
