#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理IP管理系统
支持HTTP/HTTPS/SOCKS代理的完整管理方案
- 代理池管理
- 健康检测
- 智能轮换
- 性能监控
"""

import json
import time
import random
import requests
import sqlite3
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urlparse

from ..utils.logger import get_logger

logger = get_logger("ProxyManager")


@dataclass
class ProxyInfo:
    """代理信息数据类"""
    ip: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    proxy_type: str = "http"  # http, https, socks4, socks5
    country: Optional[str] = None
    city: Optional[str] = None
    isp: Optional[str] = None
    
    # 状态信息
    is_active: bool = True
    last_check_time: Optional[datetime] = None
    response_time: float = 0.0
    success_count: int = 0
    failure_count: int = 0
    total_used: int = 0
    
    # 绑定信息
    bound_accounts: List[str] = field(default_factory=list)
    
    @property
    def proxy_url(self) -> str:
        """生成代理URL"""
        if self.username and self.password:
            auth = f"{self.username}:{self.password}@"
        else:
            auth = ""
        
        if self.proxy_type.lower() in ['socks4', 'socks5']:
            protocol = self.proxy_type.lower()
        else:
            protocol = "http"
        
        return f"{protocol}://{auth}{self.ip}:{self.port}"
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        total = self.success_count + self.failure_count
        if total == 0:
            return 100.0
        return (self.success_count / total) * 100
    
    @property
    def proxy_dict(self) -> Dict[str, str]:
        """生成requests使用的代理字典"""
        proxy_url = self.proxy_url
        return {
            'http': proxy_url,
            'https': proxy_url
        }


class ProxyManager:
    """代理IP管理器"""
    
    def __init__(self, db_path: str = "data/proxies.db"):
        self.db_path = db_path
        self.proxy_pool: Dict[str, ProxyInfo] = {}
        self.account_proxy_binding: Dict[str, str] = {}  # 账号->代理ID映射
        
        # 检测配置
        self.check_urls = [
            "http://httpbin.org/ip",
            "https://api.ipify.org?format=json",
            "http://ip-api.com/json"
        ]
        self.check_timeout = 10
        self.max_response_time = 5.0
        
        self._init_database()
        self._load_proxies()
        
        logger.info("代理IP管理器初始化完成")
    
    def _init_database(self):
        """初始化代理数据库"""
        try:
            import os
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建代理表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS proxies (
                        id TEXT PRIMARY KEY,
                        ip TEXT NOT NULL,
                        port INTEGER NOT NULL,
                        username TEXT,
                        password TEXT,
                        proxy_type TEXT DEFAULT 'http',
                        country TEXT,
                        city TEXT,
                        isp TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        last_check_time TEXT,
                        response_time REAL DEFAULT 0.0,
                        success_count INTEGER DEFAULT 0,
                        failure_count INTEGER DEFAULT 0,
                        total_used INTEGER DEFAULT 0,
                        created_time TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建账号代理绑定表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS account_proxy_bindings (
                        account_email TEXT PRIMARY KEY,
                        proxy_id TEXT NOT NULL,
                        bind_time TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (proxy_id) REFERENCES proxies (id)
                    )
                ''')
                
                conn.commit()
                logger.info("代理数据库初始化完成")
                
        except Exception as e:
            logger.error(f"初始化代理数据库失败: {e}")
            raise
    
    def _load_proxies(self):
        """从数据库加载代理"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM proxies WHERE is_active = 1')
                rows = cursor.fetchall()
                
                for row in rows:
                    proxy_id = row[0]
                    proxy_info = ProxyInfo(
                        ip=row[1],
                        port=row[2],
                        username=row[3],
                        password=row[4],
                        proxy_type=row[5] or 'http',
                        country=row[6],
                        city=row[7],
                        isp=row[8],
                        is_active=bool(row[9]),
                        last_check_time=datetime.fromisoformat(row[10]) if row[10] else None,
                        response_time=row[11] or 0.0,
                        success_count=row[12] or 0,
                        failure_count=row[13] or 0,
                        total_used=row[14] or 0
                    )
                    
                    self.proxy_pool[proxy_id] = proxy_info
                
                # 加载账号代理绑定
                cursor.execute('SELECT account_email, proxy_id FROM account_proxy_bindings')
                bindings = cursor.fetchall()
                
                for account_email, proxy_id in bindings:
                    if proxy_id in self.proxy_pool:
                        self.account_proxy_binding[account_email] = proxy_id
                        self.proxy_pool[proxy_id].bound_accounts.append(account_email)
                
                logger.info(f"加载了 {len(self.proxy_pool)} 个代理，{len(self.account_proxy_binding)} 个绑定")
                
        except Exception as e:
            logger.error(f"加载代理失败: {e}")
    
    def add_proxy(self, ip: str, port: int, username: str = None, password: str = None,
                  proxy_type: str = "http", country: str = None, city: str = None, 
                  isp: str = None) -> str:
        """添加代理"""
        try:
            proxy_id = f"{ip}:{port}"
            
            # 检查是否已存在
            if proxy_id in self.proxy_pool:
                logger.warning(f"代理已存在: {proxy_id}")
                return proxy_id
            
            # 创建代理信息
            proxy_info = ProxyInfo(
                ip=ip,
                port=port,
                username=username,
                password=password,
                proxy_type=proxy_type,
                country=country,
                city=city,
                isp=isp
            )
            
            # 保存到数据库
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO proxies 
                    (id, ip, port, username, password, proxy_type, country, city, isp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (proxy_id, ip, port, username, password, proxy_type, country, city, isp))
                conn.commit()
            
            # 添加到内存池
            self.proxy_pool[proxy_id] = proxy_info
            
            logger.info(f"✅ 添加代理成功: {proxy_id}")
            return proxy_id
            
        except Exception as e:
            logger.error(f"❌ 添加代理失败: {e}")
            return None
    
    def add_proxies_batch(self, proxies: List[Dict[str, Any]]) -> int:
        """批量添加代理"""
        try:
            success_count = 0
            
            for proxy_data in proxies:
                proxy_id = self.add_proxy(**proxy_data)
                if proxy_id:
                    success_count += 1
            
            logger.info(f"✅ 批量添加代理完成: {success_count}/{len(proxies)}")
            return success_count
            
        except Exception as e:
            logger.error(f"❌ 批量添加代理失败: {e}")
            return 0
    
    def check_proxy_health(self, proxy_id: str) -> bool:
        """检查单个代理健康状态"""
        try:
            if proxy_id not in self.proxy_pool:
                return False
            
            proxy_info = self.proxy_pool[proxy_id]
            
            # 测试代理连接
            start_time = time.time()
            
            try:
                # 使用代理请求测试URL
                response = requests.get(
                    random.choice(self.check_urls),
                    proxies=proxy_info.proxy_dict,
                    timeout=self.check_timeout,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                )
                
                response_time = time.time() - start_time
                
                if response.status_code == 200 and response_time <= self.max_response_time:
                    # 代理健康
                    proxy_info.success_count += 1
                    proxy_info.response_time = response_time
                    proxy_info.is_active = True
                    proxy_info.last_check_time = datetime.now()
                    
                    self._update_proxy_stats(proxy_id, proxy_info)
                    
                    logger.debug(f"✅ 代理健康: {proxy_id} ({response_time:.2f}s)")
                    return True
                else:
                    raise Exception(f"响应异常: {response.status_code}, 耗时: {response_time:.2f}s")
                    
            except Exception as e:
                # 代理不健康
                proxy_info.failure_count += 1
                proxy_info.last_check_time = datetime.now()
                
                # 连续失败超过阈值则标记为不活跃
                if proxy_info.failure_count >= 3:
                    proxy_info.is_active = False
                
                self._update_proxy_stats(proxy_id, proxy_info)
                
                logger.warning(f"⚠️ 代理不健康: {proxy_id}, 错误: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 检查代理健康失败: {proxy_id}, 错误: {e}")
            return False
    
    def check_all_proxies_health(self) -> Dict[str, bool]:
        """检查所有代理健康状态"""
        try:
            logger.info("🔍 开始检查所有代理健康状态...")
            
            results = {}
            
            # 使用线程池并发检查
            with ThreadPoolExecutor(max_workers=10) as executor:
                future_to_proxy = {
                    executor.submit(self.check_proxy_health, proxy_id): proxy_id
                    for proxy_id in self.proxy_pool.keys()
                }
                
                for future in as_completed(future_to_proxy):
                    proxy_id = future_to_proxy[future]
                    try:
                        result = future.result()
                        results[proxy_id] = result
                    except Exception as e:
                        logger.error(f"检查代理异常: {proxy_id}, 错误: {e}")
                        results[proxy_id] = False
            
            healthy_count = sum(1 for is_healthy in results.values() if is_healthy)
            total_count = len(results)
            
            logger.info(f"✅ 代理健康检查完成: {healthy_count}/{total_count} 个代理健康")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 检查所有代理健康失败: {e}")
            return {}
    
    def _update_proxy_stats(self, proxy_id: str, proxy_info: ProxyInfo):
        """更新代理统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE proxies SET
                        is_active = ?,
                        last_check_time = ?,
                        response_time = ?,
                        success_count = ?,
                        failure_count = ?
                    WHERE id = ?
                ''', (
                    proxy_info.is_active,
                    proxy_info.last_check_time.isoformat() if proxy_info.last_check_time else None,
                    proxy_info.response_time,
                    proxy_info.success_count,
                    proxy_info.failure_count,
                    proxy_id
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"更新代理统计失败: {e}")
    
    def get_healthy_proxies(self) -> List[str]:
        """获取健康的代理列表"""
        try:
            healthy_proxies = [
                proxy_id for proxy_id, proxy_info in self.proxy_pool.items()
                if proxy_info.is_active and proxy_info.success_rate >= 70
            ]
            
            # 按成功率和响应时间排序
            healthy_proxies.sort(key=lambda pid: (
                -self.proxy_pool[pid].success_rate,
                self.proxy_pool[pid].response_time
            ))
            
            return healthy_proxies
            
        except Exception as e:
            logger.error(f"获取健康代理失败: {e}")
            return []
    
    def bind_account_proxy(self, account_email: str, proxy_id: str = None) -> bool:
        """绑定账号和代理"""
        try:
            if proxy_id is None:
                # 自动选择最佳代理
                proxy_id = self.select_best_proxy_for_account(account_email)
            
            if proxy_id is None:
                logger.info(f"账号 {account_email} 将使用直连模式（无可用代理）")
                # 清除可能存在的旧绑定
                if account_email in self.account_proxy_binding:
                    old_proxy_id = self.account_proxy_binding[account_email]
                    if old_proxy_id in self.proxy_pool:
                        old_proxy = self.proxy_pool[old_proxy_id]
                        if account_email in old_proxy.bound_accounts:
                            old_proxy.bound_accounts.remove(account_email)
                    del self.account_proxy_binding[account_email]
                return True  # 直连模式也算绑定成功

            if proxy_id not in self.proxy_pool:
                logger.error(f"代理不存在: {proxy_id}")
                return False
            
            # 解除旧绑定
            if account_email in self.account_proxy_binding:
                old_proxy_id = self.account_proxy_binding[account_email]
                if old_proxy_id in self.proxy_pool:
                    old_proxy = self.proxy_pool[old_proxy_id]
                    if account_email in old_proxy.bound_accounts:
                        old_proxy.bound_accounts.remove(account_email)
            
            # 建立新绑定
            self.account_proxy_binding[account_email] = proxy_id
            self.proxy_pool[proxy_id].bound_accounts.append(account_email)
            
            # 保存到数据库
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO account_proxy_bindings 
                    (account_email, proxy_id) VALUES (?, ?)
                ''', (account_email, proxy_id))
                conn.commit()
            
            logger.info(f"✅ 账号代理绑定成功: {account_email} -> {proxy_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 绑定账号代理失败: {e}")
            return False
    
    def select_best_proxy_for_account(self, account_email: str) -> Optional[str]:
        """为账号选择最佳代理（支持直连模式）"""
        try:
            healthy_proxies = self.get_healthy_proxies()

            if not healthy_proxies:
                logger.info(f"没有可用的健康代理，账号 {account_email} 将使用直连模式")
                return None  # 返回None表示使用直连

            # 选择绑定账号最少的代理
            best_proxy_id = min(healthy_proxies,
                              key=lambda pid: len(self.proxy_pool[pid].bound_accounts))

            logger.info(f"为账号 {account_email} 选择代理: {best_proxy_id}")
            return best_proxy_id

        except Exception as e:
            logger.error(f"选择最佳代理失败: {e}")
            logger.info(f"代理选择异常，账号 {account_email} 将使用直连模式")
            return None
    
    def get_proxy_for_account(self, account_email: str) -> Optional[ProxyInfo]:
        """获取账号绑定的代理（支持直连模式）"""
        try:
            if account_email not in self.account_proxy_binding:
                # 自动绑定代理
                if self.bind_account_proxy(account_email):
                    # 检查是否成功绑定到代理
                    if account_email in self.account_proxy_binding:
                        proxy_id = self.account_proxy_binding[account_email]
                    else:
                        # 绑定成功但使用直连模式
                        logger.info(f"账号 {account_email} 使用直连模式")
                        return None
                else:
                    logger.info(f"账号 {account_email} 绑定失败，使用直连模式")
                    return None
            else:
                proxy_id = self.account_proxy_binding[account_email]

            # 检查代理是否存在（如果proxy_id为None，说明使用直连模式）
            if not proxy_id:
                logger.info(f"账号 {account_email} 使用直连模式")
                return None

            if proxy_id in self.proxy_pool:
                proxy_info = self.proxy_pool[proxy_id]

                # 检查代理是否健康
                if not proxy_info.is_active:
                    # 重新绑定健康代理
                    if self.bind_account_proxy(account_email):
                        if account_email in self.account_proxy_binding:
                            new_proxy_id = self.account_proxy_binding[account_email]
                            if new_proxy_id in self.proxy_pool:
                                return self.proxy_pool[new_proxy_id]
                    return None

                return proxy_info

            # 代理不存在，使用直连
            logger.info(f"账号 {account_email} 的代理不存在，使用直连模式")
            return None

        except Exception as e:
            logger.error(f"获取账号代理失败: {e}")
            logger.info(f"获取代理异常，账号 {account_email} 使用直连模式")
            return None
    
    def get_proxy_statistics(self) -> Dict[str, Any]:
        """获取代理统计信息"""
        try:
            total_proxies = len(self.proxy_pool)
            active_proxies = sum(1 for p in self.proxy_pool.values() if p.is_active)
            bound_accounts = len(self.account_proxy_binding)
            
            # 计算平均响应时间
            active_response_times = [p.response_time for p in self.proxy_pool.values() 
                                   if p.is_active and p.response_time > 0]
            avg_response_time = sum(active_response_times) / len(active_response_times) if active_response_times else 0
            
            # 计算平均成功率
            success_rates = [p.success_rate for p in self.proxy_pool.values() if p.is_active]
            avg_success_rate = sum(success_rates) / len(success_rates) if success_rates else 0
            
            stats = {
                'total_proxies': total_proxies,
                'active_proxies': active_proxies,
                'inactive_proxies': total_proxies - active_proxies,
                'bound_accounts': bound_accounts,
                'avg_response_time': round(avg_response_time, 3),
                'avg_success_rate': round(avg_success_rate, 1),
                'health_rate': round((active_proxies / max(total_proxies, 1)) * 100, 1)
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取代理统计失败: {e}")
            return {}
    
    def cleanup_inactive_proxies(self) -> int:
        """清理不活跃的代理"""
        try:
            inactive_proxies = [
                proxy_id for proxy_id, proxy_info in self.proxy_pool.items()
                if not proxy_info.is_active and proxy_info.failure_count >= 5
            ]
            
            for proxy_id in inactive_proxies:
                # 解除绑定
                proxy_info = self.proxy_pool[proxy_id]
                for account_email in proxy_info.bound_accounts:
                    if account_email in self.account_proxy_binding:
                        del self.account_proxy_binding[account_email]
                
                # 从数据库删除
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('DELETE FROM proxies WHERE id = ?', (proxy_id,))
                    cursor.execute('DELETE FROM account_proxy_bindings WHERE proxy_id = ?', (proxy_id,))
                    conn.commit()
                
                # 从内存删除
                del self.proxy_pool[proxy_id]
            
            logger.info(f"✅ 清理了 {len(inactive_proxies)} 个不活跃代理")
            return len(inactive_proxies)
            
        except Exception as e:
            logger.error(f"❌ 清理不活跃代理失败: {e}")
            return 0

    def rotate_proxy_for_account(self, account_email: str, reason: str = "manual") -> bool:
        """为账号轮换代理"""
        try:
            logger.info(f"🔄 为账号轮换代理: {account_email}, 原因: {reason}")

            # 获取当前代理
            current_proxy_id = self.account_proxy_binding.get(account_email)
            if current_proxy_id:
                current_proxy = self.proxy_pool.get(current_proxy_id)
                if current_proxy:
                    # 记录轮换原因
                    if reason == "failure":
                        current_proxy.failure_count += 1
                    elif reason == "rate_limit":
                        current_proxy.is_active = False
                        logger.warning(f"⚠️ 代理被限制: {current_proxy_id}")

            # 选择新代理（排除当前代理）
            healthy_proxies = self.get_healthy_proxies()
            if current_proxy_id in healthy_proxies:
                healthy_proxies.remove(current_proxy_id)

            if not healthy_proxies:
                logger.error(f"❌ 没有可用的代理进行轮换: {account_email}")
                return False

            # 选择使用次数最少的代理
            new_proxy_id = min(healthy_proxies,
                             key=lambda pid: self.proxy_pool[pid].total_used)

            # 执行绑定
            success = self.bind_account_proxy(account_email, new_proxy_id)

            if success:
                # 更新使用次数
                self.proxy_pool[new_proxy_id].total_used += 1
                self._update_proxy_usage(new_proxy_id)

                logger.info(f"✅ 代理轮换成功: {account_email} -> {new_proxy_id}")

            return success

        except Exception as e:
            logger.error(f"❌ 代理轮换失败: {account_email}, 错误: {e}")
            return False

    def _update_proxy_usage(self, proxy_id: str):
        """更新代理使用次数"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE proxies SET total_used = total_used + 1 WHERE id = ?
                ''', (proxy_id,))
                conn.commit()
        except Exception as e:
            logger.error(f"更新代理使用次数失败: {e}")

    def auto_rotate_overused_proxies(self, usage_threshold: int = 50) -> int:
        """自动轮换过度使用的代理"""
        try:
            logger.info(f"🔄 自动轮换过度使用的代理（阈值: {usage_threshold}）...")

            rotated_count = 0
            overused_accounts = []

            # 找出使用过度代理的账号
            for account_email, proxy_id in self.account_proxy_binding.items():
                if proxy_id in self.proxy_pool:
                    proxy_info = self.proxy_pool[proxy_id]
                    if proxy_info.total_used >= usage_threshold:
                        overused_accounts.append(account_email)

            # 为这些账号轮换代理
            for account_email in overused_accounts:
                if self.rotate_proxy_for_account(account_email, "overuse"):
                    rotated_count += 1

            logger.info(f"✅ 自动轮换完成: {rotated_count} 个账号")
            return rotated_count

        except Exception as e:
            logger.error(f"❌ 自动轮换失败: {e}")
            return 0

    def balance_proxy_load(self) -> int:
        """平衡代理负载"""
        try:
            logger.info("⚖️ 开始平衡代理负载...")

            healthy_proxies = self.get_healthy_proxies()
            if len(healthy_proxies) < 2:
                logger.warning("可用代理不足，无法进行负载平衡")
                return 0

            # 计算每个代理的负载
            proxy_loads = {}
            for proxy_id in healthy_proxies:
                proxy_loads[proxy_id] = len(self.proxy_pool[proxy_id].bound_accounts)

            # 找出负载最高和最低的代理
            max_load_proxy = max(proxy_loads, key=proxy_loads.get)
            min_load_proxy = min(proxy_loads, key=proxy_loads.get)

            max_load = proxy_loads[max_load_proxy]
            min_load = proxy_loads[min_load_proxy]

            # 如果负载差异超过阈值，进行平衡
            load_diff_threshold = 3
            if max_load - min_load > load_diff_threshold:
                # 从高负载代理转移一些账号到低负载代理
                high_load_accounts = self.proxy_pool[max_load_proxy].bound_accounts.copy()
                transfer_count = min(2, (max_load - min_load) // 2)

                transferred = 0
                for account_email in high_load_accounts[:transfer_count]:
                    if self.bind_account_proxy(account_email, min_load_proxy):
                        transferred += 1

                logger.info(f"✅ 负载平衡完成: 转移了 {transferred} 个账号")
                return transferred
            else:
                logger.info("代理负载已平衡，无需调整")
                return 0

        except Exception as e:
            logger.error(f"❌ 平衡代理负载失败: {e}")
            return 0

    def get_rotation_strategy(self, account_email: str) -> Dict[str, Any]:
        """获取账号的轮换策略建议"""
        try:
            current_proxy_id = self.account_proxy_binding.get(account_email)
            if not current_proxy_id or current_proxy_id not in self.proxy_pool:
                return {'action': 'bind', 'reason': '未绑定代理'}

            current_proxy = self.proxy_pool[current_proxy_id]

            # 检查是否需要轮换
            rotation_reasons = []

            # 1. 代理不健康
            if not current_proxy.is_active:
                rotation_reasons.append('代理不活跃')

            # 2. 成功率过低
            if current_proxy.success_rate < 70:
                rotation_reasons.append(f'成功率过低({current_proxy.success_rate:.1f}%)')

            # 3. 响应时间过长
            if current_proxy.response_time > 3.0:
                rotation_reasons.append(f'响应时间过长({current_proxy.response_time:.2f}s)')

            # 4. 使用次数过多
            if current_proxy.total_used > 100:
                rotation_reasons.append(f'使用次数过多({current_proxy.total_used}次)')

            # 5. 连续失败过多
            if current_proxy.failure_count > 5:
                rotation_reasons.append(f'连续失败过多({current_proxy.failure_count}次)')

            if rotation_reasons:
                return {
                    'action': 'rotate',
                    'reason': '; '.join(rotation_reasons),
                    'current_proxy': f"{current_proxy.ip}:{current_proxy.port}",
                    'priority': 'high' if len(rotation_reasons) > 2 else 'medium'
                }
            else:
                return {
                    'action': 'keep',
                    'reason': '代理状态良好',
                    'current_proxy': f"{current_proxy.ip}:{current_proxy.port}",
                    'priority': 'low'
                }

        except Exception as e:
            logger.error(f"获取轮换策略失败: {e}")
            return {'action': 'error', 'reason': f'策略分析失败: {e}'}

    def execute_rotation_strategy(self, strategy_results: List[Dict[str, Any]]) -> Dict[str, int]:
        """执行轮换策略"""
        try:
            logger.info("🎯 执行代理轮换策略...")

            results = {
                'rotated': 0,
                'bound': 0,
                'kept': 0,
                'failed': 0
            }

            # 按优先级排序
            priority_order = {'high': 0, 'medium': 1, 'low': 2}
            strategy_results.sort(key=lambda x: priority_order.get(x.get('priority', 'low'), 2))

            for strategy in strategy_results:
                account_email = strategy.get('account_email')
                action = strategy.get('action')

                if action == 'rotate':
                    if self.rotate_proxy_for_account(account_email, 'strategy'):
                        results['rotated'] += 1
                    else:
                        results['failed'] += 1

                elif action == 'bind':
                    if self.bind_account_proxy(account_email):
                        results['bound'] += 1
                    else:
                        results['failed'] += 1

                elif action == 'keep':
                    results['kept'] += 1

            logger.info(f"✅ 轮换策略执行完成: {results}")
            return results

        except Exception as e:
            logger.error(f"❌ 执行轮换策略失败: {e}")
            return {'error': 1}
