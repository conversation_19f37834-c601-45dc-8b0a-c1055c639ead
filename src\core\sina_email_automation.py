#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新浪邮箱自动化操作模块
实现新浪邮箱的自动登录、邮件发送等功能
"""

import time
import random
from typing import Dict, Any, Optional, List
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from src.core.browser_manager import BrowserManager
from src.models.account import Account
from src.utils.logger import get_logger
from src.utils.encryption import get_password_manager

logger = get_logger("SinaEmailAutomation")


class SinaEmailAutomation:
    """新浪邮箱自动化操作类"""
    
    def __init__(self, config: Dict[str, Any], browser_manager: BrowserManager):
        """
        初始化新浪邮箱自动化
        
        Args:
            config: 配置字典
            browser_manager: 浏览器管理器
        """
        self.config = config
        self.browser_manager = browser_manager
        self.password_manager = get_password_manager()
        
        # 新浪邮箱配置
        self.sina_config = config.get('sina_email', {})
        self.login_url = self.sina_config.get('login_url', 'https://mail.sina.com.cn/')
        self.compose_url = self.sina_config.get('compose_url', 'https://mail.sina.com.cn/classic/compose.php')
        
        # 选择器配置
        self.selectors = self.sina_config.get('login_selectors', {})
        self.compose_selectors = self.sina_config.get('compose_selectors', {})
        
        logger.info("新浪邮箱自动化初始化完成")
    
    def login(self, account: Account, driver_id: Optional[str] = None) -> Dict[str, Any]:
        """
        登录新浪邮箱
        
        Args:
            account: 账号信息
            driver_id: 浏览器驱动ID，如果为None则创建新驱动
        
        Returns:
            登录结果字典 {'success': bool, 'message': str, 'driver_id': str}
        """
        result = {'success': False, 'message': '', 'driver_id': ''}
        
        try:
            # 获取或创建浏览器驱动
            if driver_id is None:
                driver_id = self.browser_manager.create_driver(account)
            
            driver = self.browser_manager.get_driver(driver_id)
            if not driver:
                result['message'] = f"无法获取浏览器驱动: {driver_id}"
                return result
            
            result['driver_id'] = driver_id
            
            # 访问登录页面
            logger.info(f"访问新浪邮箱登录页面: {self.login_url}")
            driver.get(self.login_url)
            self.browser_manager.random_delay(2, 4)
            
            # 检查是否需要切换到登录框架
            self._switch_to_login_frame(driver)
            
            # 输入用户名
            username_selector = self.selectors.get('username_input', "input[name='username']")
            if not self.browser_manager.safe_send_keys(driver, By.CSS_SELECTOR, username_selector, account.email):
                result['message'] = "输入用户名失败"
                return result
            
            self.browser_manager.random_delay(0.5, 1.5)
            
            # 输入密码
            password = self.password_manager.retrieve_password(account.password)
            password_selector = self.selectors.get('password_input', "input[name='password']")
            if not self.browser_manager.safe_send_keys(driver, By.CSS_SELECTOR, password_selector, password):
                result['message'] = "输入密码失败"
                return result
            
            self.browser_manager.random_delay(0.5, 1.5)
            
            # 检查是否有验证码
            captcha_result = self._handle_captcha(driver)
            if not captcha_result['success']:
                result['message'] = f"验证码处理失败: {captcha_result['message']}"
                return result
            
            # 点击登录按钮
            login_button_selector = self.selectors.get('login_button', "input[type='submit']")
            if not self.browser_manager.safe_click(driver, By.CSS_SELECTOR, login_button_selector):
                result['message'] = "点击登录按钮失败"
                return result
            
            # 等待登录完成
            login_success = self._wait_for_login_success(driver)
            if login_success:
                result['success'] = True
                result['message'] = "登录成功"
                logger.info(f"新浪邮箱登录成功: {account.email}")
            else:
                result['message'] = "登录失败，请检查账号密码或网络连接"
                logger.warning(f"新浪邮箱登录失败: {account.email}")
            
            return result
            
        except Exception as e:
            result['message'] = f"登录过程发生异常: {str(e)}"
            logger.error(f"新浪邮箱登录异常: {account.email}, 错误: {e}")
            return result
    
    def _switch_to_login_frame(self, driver):
        """切换到登录框架（如果存在）"""
        try:
            # 检查是否有登录框架
            frames = driver.find_elements(By.TAG_NAME, "iframe")
            for frame in frames:
                try:
                    driver.switch_to.frame(frame)
                    # 检查是否能找到用户名输入框
                    username_selector = self.selectors.get('username_input', "input[name='username']")
                    driver.find_element(By.CSS_SELECTOR, username_selector)
                    logger.info("切换到登录框架成功")
                    return
                except:
                    driver.switch_to.default_content()
                    continue
        except Exception as e:
            logger.debug(f"切换登录框架失败: {e}")
    
    def _handle_captcha(self, driver) -> Dict[str, Any]:
        """
        处理验证码
        
        Args:
            driver: 浏览器驱动
        
        Returns:
            处理结果字典
        """
        result = {'success': True, 'message': ''}
        
        try:
            # 检查是否有验证码输入框
            captcha_selector = self.selectors.get('captcha_input', "input[name='door']")
            captcha_elements = driver.find_elements(By.CSS_SELECTOR, captcha_selector)
            
            if captcha_elements and captcha_elements[0].is_displayed():
                # 有验证码，需要人工处理
                logger.warning("检测到验证码，需要人工输入")
                result['success'] = False
                result['message'] = "检测到验证码，请人工输入后继续"
                
                # 等待用户输入验证码（最多等待60秒）
                for i in range(60):
                    time.sleep(1)
                    try:
                        captcha_value = captcha_elements[0].get_attribute('value')
                        if captcha_value and len(captcha_value) > 0:
                            result['success'] = True
                            result['message'] = "验证码输入完成"
                            logger.info("验证码输入完成")
                            break
                    except:
                        continue
                
                if not result['success']:
                    result['message'] = "验证码输入超时"
            
        except Exception as e:
            logger.error(f"处理验证码失败: {e}")
            result['success'] = False
            result['message'] = f"处理验证码异常: {str(e)}"
        
        return result
    
    def _wait_for_login_success(self, driver, timeout: int = 30) -> bool:
        """
        等待登录成功
        
        Args:
            driver: 浏览器驱动
            timeout: 超时时间（秒）
        
        Returns:
            是否登录成功
        """
        try:
            # 等待页面跳转或出现邮箱主界面元素
            WebDriverWait(driver, timeout).until(
                lambda d: "mail.sina.com.cn" in d.current_url and "login" not in d.current_url.lower()
            )
            
            # 检查是否有邮箱主界面的特征元素
            success_indicators = [
                "收件箱",
                "写邮件",
                "发件箱",
                "inbox",
                "compose"
            ]
            
            for indicator in success_indicators:
                try:
                    if indicator in driver.page_source:
                        return True
                except:
                    continue
            
            return False
            
        except TimeoutException:
            logger.warning("等待登录成功超时")
            return False
        except Exception as e:
            logger.error(f"等待登录成功失败: {e}")
            return False
    
    def send_email(self, driver_id: str, to_email: str, subject: str, content: str) -> Dict[str, Any]:
        """
        发送邮件
        
        Args:
            driver_id: 浏览器驱动ID
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
        
        Returns:
            发送结果字典
        """
        result = {'success': False, 'message': ''}
        
        try:
            driver = self.browser_manager.get_driver(driver_id)
            if not driver:
                result['message'] = f"无法获取浏览器驱动: {driver_id}"
                return result
            
            # 访问写邮件页面
            logger.info("访问写邮件页面")
            driver.get(self.compose_url)
            self.browser_manager.random_delay(2, 4)
            
            # 输入收件人
            to_selector = self.compose_selectors.get('to_input', "input[name='to']")
            if not self.browser_manager.safe_send_keys(driver, By.CSS_SELECTOR, to_selector, to_email):
                result['message'] = "输入收件人失败"
                return result
            
            self.browser_manager.random_delay(0.5, 1.0)
            
            # 输入主题
            subject_selector = self.compose_selectors.get('subject_input', "input[name='subject']")
            if not self.browser_manager.safe_send_keys(driver, By.CSS_SELECTOR, subject_selector, subject):
                result['message'] = "输入邮件主题失败"
                return result
            
            self.browser_manager.random_delay(0.5, 1.0)
            
            # 输入内容
            content_selector = self.compose_selectors.get('content_textarea', "textarea[name='content']")
            if not self.browser_manager.safe_send_keys(driver, By.CSS_SELECTOR, content_selector, content):
                result['message'] = "输入邮件内容失败"
                return result
            
            self.browser_manager.random_delay(1.0, 2.0)
            
            # 点击发送按钮
            send_button_selector = self.compose_selectors.get('send_button', "input[value='发送']")
            if not self.browser_manager.safe_click(driver, By.CSS_SELECTOR, send_button_selector):
                result['message'] = "点击发送按钮失败"
                return result
            
            # 等待发送完成
            if self._wait_for_send_success(driver):
                result['success'] = True
                result['message'] = "邮件发送成功"
                logger.info(f"邮件发送成功: {to_email}")
            else:
                result['message'] = "邮件发送失败"
                logger.warning(f"邮件发送失败: {to_email}")
            
            return result
            
        except Exception as e:
            result['message'] = f"发送邮件过程发生异常: {str(e)}"
            logger.error(f"发送邮件异常: {to_email}, 错误: {e}")
            return result
    
    def _wait_for_send_success(self, driver, timeout: int = 15) -> bool:
        """
        等待邮件发送成功
        
        Args:
            driver: 浏览器驱动
            timeout: 超时时间（秒）
        
        Returns:
            是否发送成功
        """
        try:
            # 等待发送成功的提示信息
            success_indicators = [
                "发送成功",
                "邮件已发送",
                "发送完成",
                "send success",
                "sent successfully"
            ]
            
            for i in range(timeout):
                time.sleep(1)
                page_source = driver.page_source.lower()
                
                for indicator in success_indicators:
                    if indicator.lower() in page_source:
                        return True
                
                # 检查是否返回到邮箱主页面
                if "收件箱" in driver.page_source or "inbox" in page_source:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"等待发送成功失败: {e}")
            return False
    
    def logout(self, driver_id: str) -> bool:
        """
        退出登录
        
        Args:
            driver_id: 浏览器驱动ID
        
        Returns:
            是否退出成功
        """
        try:
            driver = self.browser_manager.get_driver(driver_id)
            if not driver:
                return False
            
            # 查找退出链接并点击
            logout_selectors = [
                "a[href*='logout']",
                "a[href*='exit']",
                "退出",
                "注销"
            ]
            
            for selector in logout_selectors:
                try:
                    if self.browser_manager.safe_click(driver, By.CSS_SELECTOR, selector, timeout=5):
                        logger.info("退出登录成功")
                        return True
                except:
                    continue
            
            logger.warning("未找到退出登录链接")
            return False
            
        except Exception as e:
            logger.error(f"退出登录失败: {e}")
            return False
