#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件发送记录数据模型
管理邮件发送记录的存储、查询和统计功能
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
from src.models.database import DatabaseManager
from src.utils.logger import get_logger

logger = get_logger("SendRecordModel")


class SendStatus(Enum):
    """发送状态枚举"""
    PENDING = "pending"      # 待发送
    SENDING = "sending"      # 发送中
    SUCCESS = "success"      # 发送成功
    FAILED = "failed"        # 发送失败
    CANCELLED = "cancelled"  # 已取消


@dataclass
class SendRecord:
    """邮件发送记录数据类"""
    id: Optional[int] = None
    task_id: Optional[str] = None           # 任务ID
    from_email: str = ""                    # 发送邮箱
    to_email: str = ""                      # 接收邮箱
    subject: str = ""                       # 邮件主题
    content: str = ""                       # 邮件内容
    content_type: str = "text/plain"        # 内容类型
    status: SendStatus = SendStatus.PENDING # 发送状态
    error_msg: Optional[str] = None         # 错误信息
    send_time: Optional[datetime] = None    # 发送时间
    response_time: Optional[float] = None   # 响应时间(秒)
    browser_id: Optional[str] = None        # 浏览器ID
    proxy_ip: Optional[str] = None          # 代理IP
    retry_count: int = 0                    # 重试次数
    create_time: Optional[datetime] = None  # 创建时间
    update_time: Optional[datetime] = None  # 更新时间
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'from_email': self.from_email,
            'to_email': self.to_email,
            'subject': self.subject,
            'content': self.content,
            'content_type': self.content_type,
            'status': self.status.value if isinstance(self.status, SendStatus) else self.status,
            'error_msg': self.error_msg,
            'send_time': self.send_time.isoformat() if self.send_time else None,
            'response_time': self.response_time,
            'browser_id': self.browser_id,
            'proxy_ip': self.proxy_ip,
            'retry_count': self.retry_count,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SendRecord':
        """从字典创建发送记录对象"""
        record = cls()
        record.id = data.get('id')
        record.task_id = data.get('task_id')
        record.from_email = data.get('from_email', '')
        record.to_email = data.get('to_email', '')
        record.subject = data.get('subject', '')
        record.content = data.get('content', '')
        record.content_type = data.get('content_type', 'text/plain')
        
        # 处理状态
        status_value = data.get('status', 'pending')
        if isinstance(status_value, str):
            try:
                record.status = SendStatus(status_value)
            except ValueError:
                record.status = SendStatus.PENDING
        else:
            record.status = status_value
        
        record.error_msg = data.get('error_msg')
        record.response_time = data.get('response_time')
        record.browser_id = data.get('browser_id')
        record.proxy_ip = data.get('proxy_ip')
        record.retry_count = data.get('retry_count', 0)
        
        # 处理时间字段
        if data.get('send_time'):
            record.send_time = datetime.fromisoformat(data['send_time'])
        if data.get('create_time'):
            record.create_time = datetime.fromisoformat(data['create_time'])
        if data.get('update_time'):
            record.update_time = datetime.fromisoformat(data['update_time'])
        
        return record


class SendRecordManager:
    """邮件发送记录管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化发送记录管理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db = db_manager
        self.ensure_table_exists()
    
    def ensure_table_exists(self):
        """确保发送记录表存在"""
        try:
            create_table_sql = """
                CREATE TABLE IF NOT EXISTS send_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT,
                    from_email TEXT NOT NULL,
                    to_email TEXT NOT NULL,
                    subject TEXT,
                    content TEXT,
                    content_type TEXT DEFAULT 'text/plain',
                    status TEXT DEFAULT 'pending',
                    error_msg TEXT,
                    send_time DATETIME,
                    response_time REAL,
                    browser_id TEXT,
                    proxy_ip TEXT,
                    retry_count INTEGER DEFAULT 0,
                    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    update_time DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """
            self.db.execute_update(create_table_sql)
            
            # 创建索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_send_records_status ON send_records(status)",
                "CREATE INDEX IF NOT EXISTS idx_send_records_from_email ON send_records(from_email)",
                "CREATE INDEX IF NOT EXISTS idx_send_records_send_time ON send_records(send_time)",
                "CREATE INDEX IF NOT EXISTS idx_send_records_task_id ON send_records(task_id)"
            ]
            
            for index_sql in indexes:
                self.db.execute_update(index_sql)
            
            logger.info("发送记录表和索引创建完成")
            
        except Exception as e:
            logger.error(f"创建发送记录表失败: {e}")
            raise
    
    def add_record(self, record: SendRecord) -> int:
        """
        添加发送记录
        
        Args:
            record: 发送记录对象
        
        Returns:
            新添加记录的ID
        """
        try:
            query = """
                INSERT INTO send_records (
                    task_id, from_email, to_email, subject, content, content_type,
                    status, error_msg, send_time, response_time, browser_id, 
                    proxy_ip, retry_count
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                record.task_id,
                record.from_email,
                record.to_email,
                record.subject,
                record.content,
                record.content_type,
                record.status.value,
                record.error_msg,
                record.send_time,
                record.response_time,
                record.browser_id,
                record.proxy_ip,
                record.retry_count
            )
            
            record_id = self.db.execute_insert(query, params)
            logger.debug(f"发送记录添加成功: {record.to_email}, ID: {record_id}")
            return record_id
            
        except Exception as e:
            logger.error(f"添加发送记录失败: {record.to_email}, 错误: {e}")
            raise
    
    def update_record(self, record: SendRecord) -> bool:
        """
        更新发送记录
        
        Args:
            record: 发送记录对象
        
        Returns:
            是否更新成功
        """
        try:
            query = """
                UPDATE send_records SET 
                    task_id = ?, from_email = ?, to_email = ?, subject = ?, 
                    content = ?, content_type = ?, status = ?, error_msg = ?,
                    send_time = ?, response_time = ?, browser_id = ?, proxy_ip = ?,
                    retry_count = ?, update_time = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            params = (
                record.task_id,
                record.from_email,
                record.to_email,
                record.subject,
                record.content,
                record.content_type,
                record.status.value,
                record.error_msg,
                record.send_time,
                record.response_time,
                record.browser_id,
                record.proxy_ip,
                record.retry_count,
                record.id
            )
            
            rows_affected = self.db.execute_update(query, params)
            success = rows_affected > 0
            
            if success:
                logger.debug(f"发送记录更新成功: ID {record.id}")
            else:
                logger.warning(f"发送记录更新失败，未找到记录: ID {record.id}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新发送记录失败: ID {record.id}, 错误: {e}")
            return False
    
    def update_status(self, record_id: int, status: SendStatus, 
                     error_msg: Optional[str] = None,
                     response_time: Optional[float] = None) -> bool:
        """
        更新发送状态
        
        Args:
            record_id: 记录ID
            status: 新状态
            error_msg: 错误信息
            response_time: 响应时间
        
        Returns:
            是否更新成功
        """
        try:
            query = """
                UPDATE send_records SET 
                    status = ?, error_msg = ?, response_time = ?,
                    send_time = CASE WHEN ? = 'success' THEN CURRENT_TIMESTAMP ELSE send_time END,
                    update_time = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            params = (status.value, error_msg, response_time, status.value, record_id)
            
            rows_affected = self.db.execute_update(query, params)
            success = rows_affected > 0
            
            if success:
                logger.debug(f"发送状态更新成功: ID {record_id}, 状态: {status.value}")
            else:
                logger.warning(f"发送状态更新失败，未找到记录: ID {record_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新发送状态失败: ID {record_id}, 错误: {e}")
            return False

    def get_record_by_id(self, record_id: int) -> Optional[SendRecord]:
        """
        根据ID获取发送记录

        Args:
            record_id: 记录ID

        Returns:
            发送记录对象或None
        """
        try:
            query = "SELECT * FROM send_records WHERE id = ?"
            results = self.db.execute_query(query, (record_id,))

            if results:
                return SendRecord.from_dict(results[0])
            return None

        except Exception as e:
            logger.error(f"获取发送记录失败: ID {record_id}, 错误: {e}")
            return None

    def get_records_by_status(self, status: SendStatus, limit: int = 100) -> List[SendRecord]:
        """
        根据状态获取发送记录

        Args:
            status: 发送状态
            limit: 限制数量

        Returns:
            发送记录列表
        """
        try:
            query = """
                SELECT * FROM send_records
                WHERE status = ?
                ORDER BY create_time DESC
                LIMIT ?
            """
            results = self.db.execute_query(query, (status.value, limit))

            return [SendRecord.from_dict(row) for row in results]

        except Exception as e:
            logger.error(f"获取发送记录失败: 状态 {status.value}, 错误: {e}")
            return []

    def get_records_by_email(self, from_email: str, limit: int = 100) -> List[SendRecord]:
        """
        根据发送邮箱获取记录

        Args:
            from_email: 发送邮箱
            limit: 限制数量

        Returns:
            发送记录列表
        """
        try:
            query = """
                SELECT * FROM send_records
                WHERE from_email = ?
                ORDER BY create_time DESC
                LIMIT ?
            """
            results = self.db.execute_query(query, (from_email, limit))

            return [SendRecord.from_dict(row) for row in results]

        except Exception as e:
            logger.error(f"获取发送记录失败: 邮箱 {from_email}, 错误: {e}")
            return []

    def get_records_by_date_range(self, start_date: datetime, end_date: datetime) -> List[SendRecord]:
        """
        根据日期范围获取记录

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            发送记录列表
        """
        try:
            query = """
                SELECT * FROM send_records
                WHERE create_time BETWEEN ? AND ?
                ORDER BY create_time DESC
            """
            results = self.db.execute_query(query, (start_date, end_date))

            return [SendRecord.from_dict(row) for row in results]

        except Exception as e:
            logger.error(f"获取发送记录失败: 日期范围 {start_date} - {end_date}, 错误: {e}")
            return []

    def get_statistics(self, start_date: Optional[datetime] = None,
                      end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        获取发送统计信息

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            统计信息字典
        """
        try:
            # 构建查询条件
            where_clause = ""
            params = []

            if start_date and end_date:
                where_clause = "WHERE create_time BETWEEN ? AND ?"
                params = [start_date, end_date]
            elif start_date:
                where_clause = "WHERE create_time >= ?"
                params = [start_date]
            elif end_date:
                where_clause = "WHERE create_time <= ?"
                params = [end_date]

            # 总体统计
            total_query = f"SELECT COUNT(*) as total FROM send_records {where_clause}"
            total_result = self.db.execute_query(total_query, params)
            total_count = total_result[0]['total'] if total_result else 0

            # 按状态统计
            status_query = f"""
                SELECT status, COUNT(*) as count
                FROM send_records {where_clause}
                GROUP BY status
            """
            status_results = self.db.execute_query(status_query, params)
            status_stats = {row['status']: row['count'] for row in status_results}

            # 按发送邮箱统计
            email_query = f"""
                SELECT from_email, COUNT(*) as count,
                       SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_count
                FROM send_records {where_clause}
                GROUP BY from_email
                ORDER BY count DESC
                LIMIT 10
            """
            email_results = self.db.execute_query(email_query, params)

            # 计算成功率
            success_count = status_stats.get('success', 0)
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0

            # 平均响应时间
            avg_response_query = f"""
                SELECT AVG(response_time) as avg_response_time
                FROM send_records
                {where_clause} AND response_time IS NOT NULL
            """
            avg_response_result = self.db.execute_query(avg_response_query, params)
            avg_response_time = avg_response_result[0]['avg_response_time'] if avg_response_result else 0

            # 每日发送统计
            daily_query = f"""
                SELECT DATE(create_time) as date, COUNT(*) as count,
                       SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_count
                FROM send_records {where_clause}
                GROUP BY DATE(create_time)
                ORDER BY date DESC
                LIMIT 30
            """
            daily_results = self.db.execute_query(daily_query, params)

            return {
                'total_count': total_count,
                'success_count': success_count,
                'failed_count': status_stats.get('failed', 0),
                'pending_count': status_stats.get('pending', 0),
                'success_rate': round(success_rate, 2),
                'avg_response_time': round(avg_response_time or 0, 3),
                'status_distribution': status_stats,
                'top_senders': email_results,
                'daily_stats': daily_results
            }

        except Exception as e:
            logger.error(f"获取发送统计失败: {e}")
            return {}

    def delete_old_records(self, days: int = 30) -> int:
        """
        删除旧记录

        Args:
            days: 保留天数

        Returns:
            删除的记录数
        """
        try:
            cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            cutoff_date = cutoff_date.replace(day=cutoff_date.day - days)

            query = "DELETE FROM send_records WHERE create_time < ?"
            rows_affected = self.db.execute_update(query, (cutoff_date,))

            logger.info(f"删除了 {rows_affected} 条旧发送记录")
            return rows_affected

        except Exception as e:
            logger.error(f"删除旧记录失败: {e}")
            return 0

    def export_records(self, start_date: Optional[datetime] = None,
                      end_date: Optional[datetime] = None,
                      status: Optional[SendStatus] = None) -> List[Dict[str, Any]]:
        """
        导出发送记录

        Args:
            start_date: 开始日期
            end_date: 结束日期
            status: 状态过滤

        Returns:
            记录列表
        """
        try:
            # 构建查询条件
            conditions = []
            params = []

            if start_date:
                conditions.append("create_time >= ?")
                params.append(start_date)

            if end_date:
                conditions.append("create_time <= ?")
                params.append(end_date)

            if status:
                conditions.append("status = ?")
                params.append(status.value)

            where_clause = ""
            if conditions:
                where_clause = "WHERE " + " AND ".join(conditions)

            query = f"""
                SELECT * FROM send_records
                {where_clause}
                ORDER BY create_time DESC
            """

            results = self.db.execute_query(query, params)

            # 转换为字典列表
            export_data = []
            for row in results:
                record = SendRecord.from_dict(row)
                export_data.append(record.to_dict())

            logger.info(f"导出了 {len(export_data)} 条发送记录")
            return export_data

        except Exception as e:
            logger.error(f"导出发送记录失败: {e}")
            return []
