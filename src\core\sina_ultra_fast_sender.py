#!/usr/bin/env python3
"""
新浪邮箱超高速发送器 - 修正版
正确流程: Cookies登录 → 点击写信 → 右侧界面发送
基于真实新浪邮箱界面的超高速邮件发送功能
"""

import time
from typing import Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class SinaUltraFastSender:
    """新浪邮箱超高速发送器 - 修正版

    正确流程:
    1. 假设已通过cookies登录到新浪邮箱主界面
    2. 点击"写信"按钮
    3. 在右侧显示的写邮件界面中填写和发送
    """

    def __init__(self, driver: webdriver.Chrome):
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        self.is_ready = False
        self.send_count = 0
        self.success_count = 0

        # 正确的新浪邮箱选择器 - 基于登录后的真实界面
        self.selectors = {
            # 写信按钮 - 在登录后的主界面左侧
            'write_buttons': [
                "//a[contains(text(), '写信')]",
                "//a[contains(text(), '写邮件')]",
                "//button[contains(text(), '写信')]",
                "//div[contains(text(), '写信')]//a",
                "//span[contains(text(), '写信')]//parent::a",
                "//a[@title='写信']",
                "//a[contains(@href, 'compose')]",
                "//a[contains(@class, 'write')]"
            ],

            # 右侧写邮件界面的元素 - 点击写信后右侧显示
            'to_inputs': [
                "//input[contains(@name, 'to')]",
                "//input[contains(@placeholder, '收件人')]",
                "//input[contains(@id, 'to')]",
                "//textarea[contains(@name, 'to')]"
            ],

            'subject_inputs': [
                "//input[contains(@name, 'subject')]",
                "//input[contains(@placeholder, '主题')]",
                "//input[contains(@id, 'subject')]"
            ],

            # 邮件内容区域 - 可能是iframe或富文本编辑器
            'content_areas': [
                "//iframe[contains(@id, 'editor')]",
                "//iframe[contains(@name, 'content')]",
                "//div[@contenteditable='true']",
                "//textarea[contains(@name, 'content')]",
                "//div[contains(@class, 'editor')]"
            ],

            # 发送按钮 - 在右侧写邮件界面
            'send_buttons': [
                "//input[@type='submit'][contains(@value, '发送')]",
                "//button[contains(text(), '发送')]",
                "//a[contains(text(), '发送')]",
                "//input[contains(@value, '发送')]"
            ]
        }
        
    def prepare_compose_page(self) -> bool:
        """准备写邮件页面 - 正确流程：点击写信按钮"""
        try:
            logger.info("🚀 准备新浪邮箱写邮件页面（正确流程）...")

            # 检查是否在新浪邮箱主页面（登录后）
            current_url = self.driver.current_url.lower()
            if 'mail.sina.com.cn' not in current_url:
                logger.error("❌ 未检测到新浪邮箱登录状态，请先使用cookies登录")
                return False

            logger.info("✅ 检测到已登录新浪邮箱主界面")

            # 检查右侧是否已经显示写邮件界面
            if self._is_compose_interface_visible():
                logger.info("✅ 右侧写邮件界面已显示")
                self.is_ready = True
                return True

            # 点击写信按钮，让右侧显示写邮件界面
            logger.info("🔍 查找并点击写信按钮...")
            if self._click_write_button():
                # 等待右侧界面加载
                time.sleep(2)

                if self._is_compose_interface_visible():
                    logger.info("✅ 成功点击写信按钮，右侧写邮件界面已显示")
                    self.is_ready = True
                    return True
                else:
                    logger.warning("⚠️ 点击写信按钮后，右侧界面未正确显示")
                    return False
            else:
                logger.error("❌ 未找到或无法点击写信按钮")
                return False

        except Exception as e:
            logger.error(f"❌ 准备写邮件页面失败: {e}")
            return False

    def _click_write_button(self) -> bool:
        """点击写信按钮"""
        try:
            for selector in self.selectors['write_buttons']:
                try:
                    element = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    if element:
                        logger.info(f"✅ 找到写信按钮: {selector}")
                        element.click()
                        return True
                except:
                    continue
            return False
        except Exception as e:
            logger.error(f"❌ 点击写信按钮失败: {e}")
            return False

    def _is_compose_interface_visible(self) -> bool:
        """检查右侧写邮件界面是否可见"""
        try:
            # 检查收件人输入框是否存在且可见
            for selector in self.selectors['to_inputs']:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element and element.is_displayed():
                        logger.debug(f"✅ 检测到写邮件界面元素: {selector}")
                        return True
                except:
                    continue
            return False
        except Exception as e:
            logger.debug(f"检查写邮件界面失败: {e}")
            return False
    
    def _is_compose_page(self) -> bool:
        """检查是否在写邮件页面"""
        try:
            # 检查关键元素是否存在
            indicators = [
                "//input[contains(@placeholder, '收件人') or contains(@name, 'to')]",
                "//input[contains(@placeholder, '主题') or contains(@name, 'subject')]",
                "//iframe[contains(@id, 'editor')] | //div[contains(@class, 'editor')] | //textarea[contains(@name, 'content')]"
            ]
            
            found_count = 0
            for xpath in indicators:
                try:
                    element = self.driver.find_element(By.XPATH, xpath)
                    if element:
                        found_count += 1
                except:
                    pass
            
            is_compose = found_count >= 2
            logger.debug(f"写邮件页面检查: {found_count}/3 个元素找到, 结果: {is_compose}")
            return is_compose
            
        except Exception as e:
            logger.debug(f"检查写邮件页面失败: {e}")
            return False
    
    def _click_compose_button(self) -> bool:
        """点击写邮件按钮"""
        try:
            logger.info("🔗 尝试点击写邮件按钮...")
            
            # 新浪邮箱写邮件按钮的可能选择器
            compose_selectors = [
                "//a[contains(text(), '写邮件')]",
                "//button[contains(text(), '写邮件')]",
                "//a[contains(@href, 'compose')]",
                "//div[contains(text(), '写邮件')]",
                "//span[contains(text(), '写邮件')]",
                "//a[@title='写邮件']"
            ]
            
            for selector in compose_selectors:
                try:
                    element = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    if element:
                        logger.info(f"✅ 找到写邮件按钮: {selector}")
                        element.click()
                        time.sleep(2)
                        
                        if self._is_compose_page():
                            self.is_ready = True
                            logger.info("✅ 成功进入写邮件页面")
                            return True
                            
                except Exception as e:
                    logger.debug(f"按钮点击失败: {selector} - {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.debug(f"点击写邮件按钮失败: {e}")
            return False
    
    def _navigate_to_compose(self) -> bool:
        """直接导航到写邮件页面"""
        try:
            logger.info("🔗 直接导航到写邮件页面...")
            
            # 新浪邮箱写邮件页面URL
            compose_urls = [
                "https://mail.sina.com.cn/classic/compose.php",
                "https://mail.sina.com.cn/compose.php"
            ]
            
            for url in compose_urls:
                try:
                    logger.info(f"访问: {url}")
                    self.driver.get(url)
                    time.sleep(3)
                    
                    if self._is_compose_page():
                        self.is_ready = True
                        logger.info(f"✅ 成功访问写邮件页面: {url}")
                        return True
                        
                except Exception as e:
                    logger.debug(f"URL访问失败: {url} - {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"导航到写邮件页面失败: {e}")
            return False
    
    def send_email_ultra_fast(self, to_email: str, subject: str, content: str) -> bool:
        """超高速发送邮件"""
        try:
            if not self.is_ready:
                logger.warning("⚠️ 页面未准备就绪，尝试重新准备...")
                if not self.prepare_compose_page():
                    return False
            
            logger.info(f"⚡ 超高速发送邮件: {to_email}")
            start_time = time.time()
            
            # 策略1: JavaScript超高速填写 (最快)
            if self._send_with_javascript_ultra_fast(to_email, subject, content):
                elapsed = time.time() - start_time
                logger.info(f"✅ JavaScript超高速发送成功 ({elapsed:.2f}秒)")
                self.success_count += 1
                return True
            
            # 策略2: 适配器发送 (快速且可靠)
            if self._send_with_adapter(to_email, subject, content):
                elapsed = time.time() - start_time
                logger.info(f"✅ 适配器发送成功 ({elapsed:.2f}秒)")
                self.success_count += 1
                return True
            
            # 策略3: 标准发送 (兼容)
            if self._send_with_standard_method(to_email, subject, content):
                elapsed = time.time() - start_time
                logger.info(f"✅ 标准发送成功 ({elapsed:.2f}秒)")
                self.success_count += 1
                return True
            
            elapsed = time.time() - start_time
            logger.error(f"❌ 所有发送策略都失败了 ({elapsed:.2f}秒)")
            return False
            
        except Exception as e:
            elapsed = time.time() - start_time if 'start_time' in locals() else 0
            logger.error(f"❌ 超高速发送异常: {e} ({elapsed:.2f}秒)")
            return False
        finally:
            self.send_count += 1
    
    def _send_with_javascript_ultra_fast(self, to_email: str, subject: str, content: str) -> bool:
        """使用JavaScript超高速发送"""
        try:
            logger.info("⚡ JavaScript超高速发送...")
            
            # 转义特殊字符
            safe_email = to_email.replace("'", "\\'").replace('"', '\\"')
            safe_subject = subject.replace("'", "\\'").replace('"', '\\"')
            safe_content = content.replace("'", "\\'").replace('"', '\\"').replace('\n', '\\n')
            
            # 超高速JavaScript代码
            js_code = f"""
            try {{
                console.log('⚡ 开始JavaScript超高速发送...');
                
                // 1. 填写收件人 - 多种选择器
                var toField = document.querySelector('input[placeholder*="收件人"]') ||
                             document.querySelector('input[name*="to"]') ||
                             document.querySelector('input[id*="to"]') ||
                             document.querySelector('input[class*="to"]');
                
                if (toField) {{
                    toField.value = '{safe_email}';
                    toField.dispatchEvent(new Event('input', {{bubbles: true}}));
                    toField.dispatchEvent(new Event('change', {{bubbles: true}}));
                    toField.dispatchEvent(new Event('blur', {{bubbles: true}}));
                    console.log('✅ 收件人已填写');
                }} else {{
                    console.log('❌ 未找到收件人字段');
                    return false;
                }}
                
                // 2. 填写主题 - 多种选择器
                var subjectField = document.querySelector('input[placeholder*="主题"]') ||
                                  document.querySelector('input[name*="subject"]') ||
                                  document.querySelector('input[id*="subject"]') ||
                                  document.querySelector('input[class*="subject"]');
                
                if (subjectField) {{
                    subjectField.value = '{safe_subject}';
                    subjectField.dispatchEvent(new Event('input', {{bubbles: true}}));
                    subjectField.dispatchEvent(new Event('change', {{bubbles: true}}));
                    console.log('✅ 主题已填写');
                }}
                
                // 3. 填写内容 - 处理富文本编辑器
                var contentFilled = false;
                
                // 尝试iframe编辑器
                var iframe = document.querySelector('iframe[id*="editor"]') ||
                            document.querySelector('iframe[name*="editor"]');
                
                if (iframe) {{
                    try {{
                        var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        var body = iframeDoc.body || iframeDoc.querySelector('body');
                        if (body) {{
                            body.innerHTML = '{safe_content}';
                            body.dispatchEvent(new Event('input', {{bubbles: true}}));
                            contentFilled = true;
                            console.log('✅ iframe内容已填写');
                        }}
                    }} catch(e) {{
                        console.log('iframe访问失败:', e);
                    }}
                }}
                
                // 尝试div编辑器
                if (!contentFilled) {{
                    var contentDiv = document.querySelector('div[contenteditable="true"]') ||
                                    document.querySelector('div[class*="editor"]');
                    
                    if (contentDiv) {{
                        contentDiv.innerHTML = '{safe_content}';
                        contentDiv.dispatchEvent(new Event('input', {{bubbles: true}}));
                        contentFilled = true;
                        console.log('✅ div内容已填写');
                    }}
                }}
                
                // 尝试textarea
                if (!contentFilled) {{
                    var textarea = document.querySelector('textarea[name*="content"]') ||
                                  document.querySelector('textarea[id*="content"]');
                    
                    if (textarea) {{
                        textarea.value = '{safe_content}';
                        textarea.dispatchEvent(new Event('input', {{bubbles: true}}));
                        contentFilled = true;
                        console.log('✅ textarea内容已填写');
                    }}
                }}
                
                if (!contentFilled) {{
                    console.log('⚠️ 内容字段未找到，继续发送...');
                }}
                
                // 4. 等待一下然后发送
                setTimeout(function() {{
                    var sendButton = document.querySelector('a[title*="发送"]') ||
                                   document.querySelector('button[title*="发送"]') ||
                                   document.querySelector('input[value*="发送"]') ||
                                   document.querySelector('a:contains("发送")') ||
                                   document.querySelector('button:contains("发送")') ||
                                   document.querySelector('span:contains("发送")');
                    
                    if (sendButton) {{
                        console.log('✅ 找到发送按钮，准备发送...');
                        sendButton.click();
                        console.log('✅ 发送按钮已点击');
                        return true;
                    }} else {{
                        console.log('❌ 未找到发送按钮');
                        return false;
                    }}
                }}, 300);
                
                return true;
                
            }} catch(e) {{
                console.error('JavaScript发送失败:', e);
                return false;
            }}
            """
            
            # 执行JavaScript
            result = self.driver.execute_script(js_code)
            
            if result:
                time.sleep(2)  # 等待发送完成
                return self._check_send_success()
            
            return False
            
        except Exception as e:
            logger.error(f"❌ JavaScript超高速发送失败: {e}")
            return False

    def _send_with_adapter(self, to_email: str, subject: str, content: str) -> bool:
        """使用适配器发送"""
        try:
            logger.info("⚡ 适配器发送...")

            # 使用适配器填写字段
            if not self.adapter.fill_to_field(to_email):
                logger.warning("⚠️ 收件人填写失败")
                return False

            if not self.adapter.fill_subject_field(subject):
                logger.warning("⚠️ 主题填写失败")

            if not self.adapter.fill_content_field(content):
                logger.warning("⚠️ 内容填写失败")

            # 发送邮件
            if not self.adapter.click_send_button():
                logger.warning("⚠️ 发送按钮点击失败")
                return False

            # 检查发送结果
            return self.adapter.check_send_result()

        except Exception as e:
            logger.error(f"❌ 适配器发送失败: {e}")
            return False

    def _send_with_direct_elements(self, to_email: str, subject: str, content: str) -> bool:
        """直接元素操作发送"""
        try:
            logger.info("⚡ 直接元素操作发送...")
            
            # 填写收件人
            to_selectors = [
                "input[placeholder*='收件人']",
                "input[name*='to']",
                "input[id*='to']"
            ]
            
            to_field = self._find_element_by_selectors(to_selectors)
            if to_field:
                to_field.clear()
                to_field.send_keys(to_email)
                logger.info("✅ 收件人已填写")
            else:
                logger.warning("⚠️ 未找到收件人字段")
                return False
            
            # 填写主题
            subject_selectors = [
                "input[placeholder*='主题']",
                "input[name*='subject']",
                "input[id*='subject']"
            ]
            
            subject_field = self._find_element_by_selectors(subject_selectors)
            if subject_field:
                subject_field.clear()
                subject_field.send_keys(subject)
                logger.info("✅ 主题已填写")
            
            # 填写内容 - 处理富文本编辑器
            content_filled = False
            
            # 尝试iframe
            try:
                iframe = self.driver.find_element(By.CSS_SELECTOR, "iframe[id*='editor'], iframe[name*='editor']")
                if iframe:
                    self.driver.switch_to.frame(iframe)
                    body = self.driver.find_element(By.TAG_NAME, "body")
                    body.clear()
                    body.send_keys(content)
                    self.driver.switch_to.default_content()
                    content_filled = True
                    logger.info("✅ iframe内容已填写")
            except:
                pass
            
            # 尝试contenteditable div
            if not content_filled:
                try:
                    content_div = self.driver.find_element(By.CSS_SELECTOR, "div[contenteditable='true']")
                    if content_div:
                        content_div.clear()
                        content_div.send_keys(content)
                        content_filled = True
                        logger.info("✅ div内容已填写")
                except:
                    pass
            
            # 尝试textarea
            if not content_filled:
                try:
                    textarea = self.driver.find_element(By.CSS_SELECTOR, "textarea[name*='content'], textarea[id*='content']")
                    if textarea:
                        textarea.clear()
                        textarea.send_keys(content)
                        content_filled = True
                        logger.info("✅ textarea内容已填写")
                except:
                    pass
            
            # 发送邮件
            send_selectors = [
                "a[title*='发送']",
                "button[title*='发送']",
                "input[value*='发送']"
            ]
            
            send_button = self._find_element_by_selectors(send_selectors)
            if send_button:
                send_button.click()
                logger.info("✅ 发送按钮已点击")
                time.sleep(2)
                return self._check_send_success()
            else:
                logger.warning("⚠️ 未找到发送按钮")
                return False
            
        except Exception as e:
            logger.error(f"❌ 直接元素操作发送失败: {e}")
            return False
    
    def _send_with_standard_method(self, to_email: str, subject: str, content: str) -> bool:
        """标准发送方法"""
        try:
            logger.info("📧 标准发送方法...")
            
            # 使用WebDriverWait等待元素
            # 收件人
            to_field = self.wait.until(EC.presence_of_element_located((
                By.XPATH, "//input[contains(@placeholder, '收件人') or contains(@name, 'to')]"
            )))
            to_field.clear()
            to_field.send_keys(to_email)
            
            # 主题
            try:
                subject_field = self.driver.find_element(By.XPATH, "//input[contains(@placeholder, '主题') or contains(@name, 'subject')]")
                subject_field.clear()
                subject_field.send_keys(subject)
            except:
                logger.warning("⚠️ 主题字段未找到")
            
            # 内容
            try:
                # 尝试多种内容输入方式
                content_field = self.driver.find_element(By.XPATH, "//textarea | //div[@contenteditable='true']")
                content_field.clear()
                content_field.send_keys(content)
            except:
                logger.warning("⚠️ 内容字段未找到")
            
            # 发送
            send_button = self.wait.until(EC.element_to_be_clickable((
                By.XPATH, "//a[contains(@title, '发送')] | //button[contains(text(), '发送')] | //input[contains(@value, '发送')]"
            )))
            send_button.click()
            
            time.sleep(3)
            return self._check_send_success()
            
        except Exception as e:
            logger.error(f"❌ 标准发送方法失败: {e}")
            return False
    
    def _find_element_by_selectors(self, selectors: list):
        """通过多个选择器查找元素"""
        for selector in selectors:
            try:
                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                if element and element.is_displayed():
                    return element
            except:
                continue
        return None
    
    def _check_send_success(self) -> bool:
        """检查发送是否成功"""
        try:
            time.sleep(2)  # 等待页面响应
            
            current_url = self.driver.current_url.lower()
            page_source = self.driver.page_source.lower()
            
            # 成功指标
            success_indicators = [
                '发送成功', '已发送', 'sent successfully', 'message sent',
                '邮件已发送', '发送完成', '投递成功'
            ]
            
            # 失败指标
            error_indicators = [
                '发送失败', 'send failed', 'error', '错误', 'failed',
                '投递失败', '发送错误'
            ]
            
            has_success = any(indicator in page_source for indicator in success_indicators)
            has_error = any(indicator in page_source for indicator in error_indicators)
            url_success = any(keyword in current_url for keyword in ['sent', 'success', 'complete'])
            
            if has_success or (url_success and not has_error):
                logger.info("✅ 邮件发送成功确认")
                return True
            elif has_error:
                logger.warning("⚠️ 检测到发送错误")
                return False
            else:
                logger.info("🤔 无明确指标，假设发送成功")
                return True
                
        except Exception as e:
            logger.error(f"❌ 检查发送结果失败: {e}")
            return False
    
    def reset_for_next_email(self) -> bool:
        """为下一封邮件重置状态"""
        try:
            logger.info("🔄 为下一封邮件重置状态...")

            # 重新准备写邮件页面
            if self.adapter.navigate_to_compose():
                self.is_ready = True
                logger.info("✅ 重置状态成功")
                return True
            else:
                logger.warning("⚠️ 重置状态失败，尝试备用方法")
                return self.prepare_compose_page()

        except Exception as e:
            logger.error(f"❌ 重置状态失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取发送统计"""
        success_rate = (self.success_count / max(self.send_count, 1)) * 100
        return {
            'send_count': self.send_count,
            'success_count': self.success_count,
            'fail_count': self.send_count - self.success_count,
            'success_rate': round(success_rate, 1)
        }
