#!/usr/bin/env python3
"""
新浪邮箱SMTP发送器
基于SMTP协议的邮件发送，结合Cookie复用实现高效发送
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from email.header import Header
import time
from typing import Dict, List, Optional, Tuple, Any
from src.models.account import Account
from src.utils.logger import setup_logger

# 设置日志
logger = setup_logger("INFO")

class SinaSMTPSender:
    """新浪邮箱SMTP发送器"""
    
    def __init__(self):
        # 新浪邮箱SMTP配置
        self.smtp_config = {
            'server': 'smtp.sina.com',
            'port': 25,  # 或者587 (TLS), 465 (SSL)
            'use_tls': False,
            'use_ssl': False,
            'timeout': 30
        }
        
        # 连接池
        self.connections = {}
        
    def create_smtp_connection(self, account: Account) -> Optional[smtplib.SMTP]:
        """创建SMTP连接"""
        try:
            logger.info(f"🔗 创建SMTP连接: {account.email}")
            
            # 创建SMTP对象
            if self.smtp_config['use_ssl']:
                # SSL连接 (端口465)
                context = ssl.create_default_context()
                smtp = smtplib.SMTP_SSL(
                    self.smtp_config['server'], 
                    self.smtp_config['port'], 
                    timeout=self.smtp_config['timeout'],
                    context=context
                )
            else:
                # 普通连接
                smtp = smtplib.SMTP(
                    self.smtp_config['server'], 
                    self.smtp_config['port'], 
                    timeout=self.smtp_config['timeout']
                )
                
                # 如果需要TLS
                if self.smtp_config['use_tls']:
                    smtp.starttls()
            
            # 登录
            logger.info(f"🔐 SMTP登录: {account.email}")
            smtp.login(account.email, account.password)
            
            logger.info(f"✅ SMTP连接成功: {account.email}")
            return smtp
            
        except Exception as e:
            logger.error(f"❌ SMTP连接失败: {account.email} - {e}")
            return None
    
    def get_smtp_connection(self, account: Account) -> Optional[smtplib.SMTP]:
        """获取SMTP连接（支持连接复用）"""
        try:
            # 检查是否有现有连接
            if account.email in self.connections:
                smtp = self.connections[account.email]
                try:
                    # 测试连接是否有效
                    smtp.noop()
                    logger.info(f"♻️ 复用SMTP连接: {account.email}")
                    return smtp
                except:
                    # 连接已失效，删除
                    logger.warning(f"⚠️ SMTP连接已失效，重新创建: {account.email}")
                    del self.connections[account.email]
            
            # 创建新连接
            smtp = self.create_smtp_connection(account)
            if smtp:
                self.connections[account.email] = smtp
            
            return smtp
            
        except Exception as e:
            logger.error(f"❌ 获取SMTP连接失败: {account.email} - {e}")
            return None
    
    def send_email_smtp(self, account: Account, to_email: str, subject: str, content: str, content_type: str = "text/plain") -> Dict[str, Any]:
        """使用SMTP发送邮件"""
        try:
            logger.info(f"📧 SMTP发送邮件: {account.email} -> {to_email}")
            logger.info(f"📝 邮件主题: {subject}")
            
            # 获取SMTP连接
            smtp = self.get_smtp_connection(account)
            if not smtp:
                return {'success': False, 'message': 'SMTP连接失败'}
            
            # 创建邮件对象
            if content_type == "text/html":
                msg = MIMEMultipart('alternative')
                part = MIMEText(content, 'html', 'utf-8')
            else:
                msg = MIMEText(content, 'plain', 'utf-8')
                part = None
            
            # 设置邮件头
            msg['From'] = Header(account.email, 'utf-8')
            msg['To'] = Header(to_email, 'utf-8')
            msg['Subject'] = Header(subject, 'utf-8')
            
            if part:
                msg.attach(part)
            
            # 发送邮件
            logger.info("📤 开始发送SMTP邮件...")
            smtp.send_message(msg, account.email, [to_email])
            
            logger.info("✅ SMTP邮件发送成功")
            return {'success': True, 'message': 'SMTP邮件发送成功'}
            
        except Exception as e:
            logger.error(f"❌ SMTP邮件发送失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {'success': False, 'message': f'SMTP发送失败: {e}'}
    
    def test_smtp_configs(self, account: Account) -> Dict[str, Any]:
        """测试不同的SMTP配置"""
        logger.info(f"🧪 测试SMTP配置: {account.email}")
        
        # 不同的配置组合
        configs = [
            {'server': 'smtp.sina.com', 'port': 25, 'use_tls': False, 'use_ssl': False, 'name': '标准SMTP'},
            {'server': 'smtp.sina.com', 'port': 587, 'use_tls': True, 'use_ssl': False, 'name': 'SMTP+TLS'},
            {'server': 'smtp.sina.com', 'port': 465, 'use_tls': False, 'use_ssl': True, 'name': 'SMTP+SSL'},
            {'server': 'smtp.sina.cn', 'port': 25, 'use_tls': False, 'use_ssl': False, 'name': '备用服务器'},
        ]
        
        working_configs = []
        
        for config in configs:
            try:
                logger.info(f"🧪 测试配置: {config['name']} ({config['server']}:{config['port']})")
                
                # 临时设置配置
                original_config = self.smtp_config.copy()
                self.smtp_config.update(config)
                
                # 尝试连接
                smtp = self.create_smtp_connection(account)
                if smtp:
                    # 测试发送
                    test_result = self.send_email_smtp(
                        account, 
                        account.email,  # 发送给自己
                        "SMTP配置测试", 
                        f"这是{config['name']}配置的测试邮件"
                    )
                    
                    if test_result['success']:
                        working_configs.append({
                            **config,
                            'test_result': test_result
                        })
                        logger.info(f"✅ 配置有效: {config['name']}")
                    else:
                        logger.warning(f"⚠️ 配置发送失败: {config['name']}")
                    
                    # 关闭连接
                    smtp.quit()
                else:
                    logger.warning(f"⚠️ 配置连接失败: {config['name']}")
                
                # 恢复原配置
                self.smtp_config = original_config
                
            except Exception as e:
                logger.warning(f"⚠️ 配置测试异常: {config['name']} - {e}")
        
        return {
            'tested_configs': len(configs),
            'working_configs': working_configs,
            'success': len(working_configs) > 0
        }
    
    def close_connections(self):
        """关闭所有SMTP连接"""
        for email, smtp in self.connections.items():
            try:
                smtp.quit()
                logger.info(f"🔒 关闭SMTP连接: {email}")
            except:
                pass
        self.connections.clear()

def test_sina_smtp():
    """测试新浪邮箱SMTP发送"""
    print("🧪 新浪邮箱SMTP发送测试")
    print("="*50)
    
    # 创建测试账号（需要真实的邮箱密码）
    account = Account(
        email="<EMAIL>",
        password="your_email_password",  # 需要替换为真实密码
        status="active"
    )
    
    # 创建SMTP发送器
    sender = SinaSMTPSender()
    
    try:
        # 测试不同配置
        print("🔍 测试SMTP配置...")
        config_results = sender.test_smtp_configs(account)
        
        print(f"\n📊 配置测试结果:")
        print(f"测试配置数: {config_results['tested_configs']}")
        print(f"有效配置数: {len(config_results['working_configs'])}")
        
        if config_results['working_configs']:
            print("\n✅ 有效的SMTP配置:")
            for i, config in enumerate(config_results['working_configs'], 1):
                print(f"{i}. {config['name']}")
                print(f"   服务器: {config['server']}:{config['port']}")
                print(f"   TLS: {config['use_tls']}, SSL: {config['use_ssl']}")
            
            # 使用第一个有效配置发送测试邮件
            best_config = config_results['working_configs'][0]
            sender.smtp_config.update(best_config)
            
            print(f"\n📧 使用最佳配置发送测试邮件...")
            result = sender.send_email_smtp(
                account,
                "<EMAIL>",
                "新浪邮箱SMTP测试邮件",
                "这是通过新浪邮箱SMTP协议发送的测试邮件。\n\n如果您收到这封邮件，说明SMTP发送功能正常工作！"
            )
            
            print(f"\n📊 发送结果:")
            print(f"成功: {result['success']}")
            print(f"消息: {result['message']}")
            
        else:
            print("\n❌ 未找到有效的SMTP配置")
            print("可能的原因:")
            print("1. 邮箱密码不正确")
            print("2. 需要开启SMTP服务")
            print("3. 需要使用授权码而不是登录密码")
    
    finally:
        # 清理连接
        sender.close_connections()

if __name__ == "__main__":
    print("⚠️ 注意：运行此测试需要真实的新浪邮箱账号和密码")
    print("请在代码中设置正确的邮箱密码后再运行测试")
    print()
    
    # 取消注释下面的行来运行测试
    # test_sina_smtp()
