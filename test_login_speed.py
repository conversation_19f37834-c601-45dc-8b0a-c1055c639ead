#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录速度测试脚本
用于测试和验证登录速度优化效果
"""

import time
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.models.database import DatabaseManager
from src.models.account import AccountManager
from src.core.browser_manager import BrowserManager
from src.core.stealth_login_manager import StealthLoginManager
from src.utils.logger import get_logger

logger = get_logger("LoginSpeedTest")

class LoginSpeedTest:
    """登录速度测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        self.config = {
            'browser': {
                'implicit_wait': 3,
                'page_load_timeout': 20,
                'window_size': [1920, 1080]
            },
            'performance': {
                'max_concurrent_browsers': 1
            }
        }
        
        # 初始化数据库和账号管理器
        self.db_manager = DatabaseManager()
        self.account_manager = AccountManager(self.db_manager)
        
        # 初始化浏览器管理器和登录管理器
        self.browser_manager = BrowserManager(self.config)
        self.login_manager = StealthLoginManager(self.browser_manager, None)
        
        logger.info("🚀 登录速度测试环境初始化完成")
    
    def test_single_account_speed(self, email: str) -> dict:
        """测试单个账号的登录速度"""
        try:
            logger.info(f"🧪 开始测试账号登录速度: {email}")
            
            # 获取账号信息
            account = self.account_manager.get_account_by_email(email)
            if not account:
                logger.error(f"❌ 未找到账号: {email}")
                return {'success': False, 'error': '账号不存在'}
            
            # 记录开始时间
            start_time = time.time()
            
            # 执行登录测试
            success, message = self.login_manager.stealth_login(account, stealth_mode=True)
            
            # 记录结束时间
            end_time = time.time()
            total_time = end_time - start_time
            
            result = {
                'success': success,
                'message': message,
                'total_time': total_time,
                'email': email
            }
            
            if success:
                logger.info(f"✅ 登录成功: {email}, 总耗时: {total_time:.2f}秒")
            else:
                logger.error(f"❌ 登录失败: {email}, 原因: {message}, 耗时: {total_time:.2f}秒")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 测试异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def test_batch_accounts_speed(self, emails: list) -> dict:
        """测试批量账号的登录速度"""
        try:
            logger.info(f"🧪 开始批量登录速度测试，共 {len(emails)} 个账号")
            
            results = []
            total_start_time = time.time()
            
            for i, email in enumerate(emails, 1):
                logger.info(f"📧 测试账号 {i}/{len(emails)}: {email}")
                
                result = self.test_single_account_speed(email)
                results.append(result)
                
                # 批量操作间隔
                if i < len(emails):
                    time.sleep(0.5)
            
            total_end_time = time.time()
            total_batch_time = total_end_time - total_start_time
            
            # 统计结果
            success_count = sum(1 for r in results if r.get('success', False))
            failed_count = len(results) - success_count
            
            if success_count > 0:
                avg_time = sum(r.get('total_time', 0) for r in results if r.get('success', False)) / success_count
            else:
                avg_time = 0
            
            batch_result = {
                'total_accounts': len(emails),
                'success_count': success_count,
                'failed_count': failed_count,
                'success_rate': (success_count / len(emails)) * 100,
                'total_batch_time': total_batch_time,
                'average_time_per_account': avg_time,
                'results': results
            }
            
            logger.info(f"📊 批量测试完成:")
            logger.info(f"   总账号数: {batch_result['total_accounts']}")
            logger.info(f"   成功数: {batch_result['success_count']}")
            logger.info(f"   失败数: {batch_result['failed_count']}")
            logger.info(f"   成功率: {batch_result['success_rate']:.1f}%")
            logger.info(f"   总耗时: {batch_result['total_batch_time']:.2f}秒")
            logger.info(f"   平均每账号: {batch_result['average_time_per_account']:.2f}秒")
            
            return batch_result
            
        except Exception as e:
            logger.error(f"❌ 批量测试异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def benchmark_speed_improvements(self):
        """基准测试 - 对比优化前后的速度"""
        try:
            logger.info("🏁 开始登录速度基准测试")
            
            # 获取测试账号
            test_accounts = self.account_manager.get_all_accounts()[:3]  # 取前3个账号测试
            
            if not test_accounts:
                logger.error("❌ 没有可用的测试账号")
                return
            
            test_emails = [acc.email for acc in test_accounts]
            
            # 执行批量测试
            result = self.test_batch_accounts_speed(test_emails)
            
            # 输出基准报告
            logger.info("📈 速度优化基准报告:")
            logger.info("=" * 50)
            logger.info(f"测试账号数量: {result.get('total_accounts', 0)}")
            logger.info(f"登录成功率: {result.get('success_rate', 0):.1f}%")
            logger.info(f"平均登录时间: {result.get('average_time_per_account', 0):.2f}秒")
            logger.info(f"总测试时间: {result.get('total_batch_time', 0):.2f}秒")
            
            # 性能评估
            avg_time = result.get('average_time_per_account', 0)
            if avg_time < 10:
                logger.info("🚀 性能评级: 优秀 (< 10秒)")
            elif avg_time < 20:
                logger.info("⚡ 性能评级: 良好 (10-20秒)")
            elif avg_time < 30:
                logger.info("🔄 性能评级: 一般 (20-30秒)")
            else:
                logger.info("🐌 性能评级: 需要优化 (> 30秒)")
            
            logger.info("=" * 50)
            
        except Exception as e:
            logger.error(f"❌ 基准测试异常: {e}")
    
    def cleanup(self):
        """清理测试环境"""
        try:
            if hasattr(self, 'browser_manager'):
                self.browser_manager.close_all_drivers()
            logger.info("🧹 测试环境清理完成")
        except Exception as e:
            logger.error(f"❌ 清理异常: {e}")

def main():
    """主函数"""
    test = LoginSpeedTest()
    
    try:
        # 执行基准测试
        test.benchmark_speed_improvements()
        
    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断测试")
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
    finally:
        test.cleanup()

if __name__ == "__main__":
    main()
