#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号验证工具
用于验证邮箱账号的有效性和登录状态
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def validate_accounts():
    """验证所有账号"""
    print("=" * 60)
    print("新浪邮箱账号验证工具")
    print("=" * 60)
    
    try:
        from src.models.database import DatabaseManager
        from src.models.account import AccountManager
        from src.core.cookie_manager import CookieManager
        from src.utils.config_manager import ConfigManager
        
        # 初始化
        config_manager = ConfigManager()
        config = config_manager.load_config()
        db_path = config_manager.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        account_manager = AccountManager(db_manager)
        cookie_manager = CookieManager(config)
        
        # 获取所有账号
        accounts = account_manager.get_all_accounts()
        
        if not accounts:
            print("❌ 没有找到任何账号")
            print("\n请先在程序中添加邮箱账号：")
            print("1. 启动主程序: python main.py")
            print("2. 在'账号管理'选项卡中添加账号")
            return
        
        print(f"📧 找到 {len(accounts)} 个账号，开始验证...")
        print()
        
        valid_accounts = 0
        invalid_accounts = 0
        
        for i, account in enumerate(accounts, 1):
            print(f"[{i}/{len(accounts)}] 验证账号: {account.email}")
            
            try:
                # 创建会话
                session_id = cookie_manager.create_session(account)
                
                # 尝试登录
                login_result = cookie_manager.login_session(session_id)
                
                if login_result['success']:
                    print(f"  ✅ 登录成功")
                    valid_accounts += 1

                    # 更新账号状态
                    account.status = "active"
                    account_manager.update_account(account)

                else:
                    print(f"  ❌ 登录失败: {login_result['message']}")
                    invalid_accounts += 1

                    # 更新账号状态
                    account.status = "inactive"
                    account_manager.update_account(account)
                
                # 关闭会话
                cookie_manager.close_session(session_id)
                
            except Exception as e:
                print(f"  ❌ 验证异常: {e}")
                invalid_accounts += 1
                account.status = "error"
                account_manager.update_account(account)
            
            # 添加延迟避免频繁请求
            if i < len(accounts):
                time.sleep(2)
        
        print()
        print("=" * 60)
        print("验证结果汇总:")
        print(f"✅ 有效账号: {valid_accounts}")
        print(f"❌ 无效账号: {invalid_accounts}")
        print(f"📊 成功率: {(valid_accounts / len(accounts) * 100):.1f}%")
        
        if valid_accounts > 0:
            print(f"\n🎉 您有 {valid_accounts} 个可用账号，可以开始使用轻量化发送功能！")
            print("\n使用建议:")
            print("1. 在程序中点击'轻量化发送'选项卡")
            print("2. 确保'启用轻量化模式'已勾选")
            print("3. 配置邮件内容并开始发送")
        else:
            print("\n⚠️ 没有可用的账号，请检查:")
            print("1. 邮箱地址是否正确")
            print("2. 密码是否正确")
            print("3. 账号是否被锁定或需要验证")
            print("4. 网络连接是否正常")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        print("\n请确保:")
        print("1. 程序已正确安装所有依赖")
        print("2. 数据库文件存在且可访问")
        print("3. 配置文件正确")

def show_account_info():
    """显示账号信息"""
    try:
        from src.models.database import DatabaseManager
        from src.models.account import AccountManager
        from src.utils.config_manager import ConfigManager
        
        # 初始化
        config_manager = ConfigManager()
        config = config_manager.load_config()
        db_path = config_manager.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        account_manager = AccountManager(db_manager)
        accounts = account_manager.get_all_accounts()
        
        if not accounts:
            print("❌ 没有找到任何账号")
            return
        
        print("📧 当前账号列表:")
        print("-" * 80)
        print(f"{'序号':<4} {'邮箱地址':<30} {'状态':<10} {'代理IP':<20} {'添加时间'}")
        print("-" * 80)
        
        for i, account in enumerate(accounts, 1):
            proxy_info = f"{account.proxy_ip}:{account.proxy_port}" if account.proxy_ip else "无"
            create_time = account.create_time.strftime("%Y-%m-%d %H:%M") if account.create_time else "未知"
            print(f"{i:<4} {account.email:<30} {account.status:<10} {proxy_info:<20} {create_time}")
        
        print("-" * 80)
        print(f"总计: {len(accounts)} 个账号")
        
    except Exception as e:
        print(f"❌ 获取账号信息失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        if command == "info":
            show_account_info()
        elif command == "validate":
            validate_accounts()
        else:
            print("未知命令，支持的命令:")
            print("  info     - 显示账号信息")
            print("  validate - 验证账号有效性")
    else:
        print("新浪邮箱账号验证工具")
        print()
        print("使用方法:")
        print("  python tools/account_validator.py info      # 显示账号信息")
        print("  python tools/account_validator.py validate  # 验证账号有效性")
        print()
        
        # 默认显示账号信息
        show_account_info()

if __name__ == "__main__":
    main()
