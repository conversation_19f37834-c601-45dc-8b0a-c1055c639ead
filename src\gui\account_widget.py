#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号管理界面模块
提供新浪邮箱账号的管理界面，包括添加、编辑、删除、导入等功能
"""

import csv
import time
from pathlib import Path
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QLabel, QMessageBox, QFileDialog, QDialog,
    QFormLayout, QDialogButtonBox, QHeaderView, QMenu, QAction,
    QGroupBox, QSpinBox, QComboBox, QTextEdit, QProgressBar
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QIcon
from typing import List, Dict, Any, Optional
from src.models.account import Account, AccountManager
from src.models.database import DatabaseManager
from src.utils.encryption import get_password_manager
from src.utils.logger import get_logger
# 移除旧的登录管理器导入
from src.core.browser_manager import BrowserManager

logger = get_logger("AccountWidget")


class AccountDialog(QDialog):
    """账号编辑对话框"""
    
    def __init__(self, account: Optional[Account] = None, parent=None):
        """
        初始化账号对话框
        
        Args:
            account: 要编辑的账号，None表示新建
            parent: 父窗口
        """
        super().__init__(parent)
        self.account = account
        self.is_edit_mode = account is not None
        self.password_manager = get_password_manager()
        self.init_ui()
        
        if self.is_edit_mode:
            self.load_account_data()
    
    def init_ui(self):
        """初始化用户界面"""
        title = "编辑账号" if self.is_edit_mode else "添加账号"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(400, 350)
        
        layout = QVBoxLayout(self)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)
        
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("<EMAIL>")
        basic_layout.addRow("邮箱地址:", self.email_edit)
        
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("请输入密码")
        basic_layout.addRow("密码:", self.password_edit)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["active", "disabled", "error"])
        basic_layout.addRow("状态:", self.status_combo)
        
        layout.addWidget(basic_group)
        
        # 代理设置组
        proxy_group = QGroupBox("代理设置（可选）")
        proxy_layout = QFormLayout(proxy_group)
        
        self.proxy_ip_edit = QLineEdit()
        self.proxy_ip_edit.setPlaceholderText("127.0.0.1")
        proxy_layout.addRow("代理IP:", self.proxy_ip_edit)
        
        self.proxy_port_spin = QSpinBox()
        self.proxy_port_spin.setRange(1, 65535)
        self.proxy_port_spin.setValue(8080)
        proxy_layout.addRow("代理端口:", self.proxy_port_spin)
        
        self.proxy_user_edit = QLineEdit()
        self.proxy_user_edit.setPlaceholderText("用户名（可选）")
        proxy_layout.addRow("代理用户名:", self.proxy_user_edit)
        
        self.proxy_pass_edit = QLineEdit()
        self.proxy_pass_edit.setEchoMode(QLineEdit.Password)
        self.proxy_pass_edit.setPlaceholderText("密码（可选）")
        proxy_layout.addRow("代理密码:", self.proxy_pass_edit)
        
        layout.addWidget(proxy_group)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def load_account_data(self):
        """加载账号数据到界面"""
        if not self.account:
            return
        
        self.email_edit.setText(self.account.email)
        
        # 直接显示明文密码
        self.password_edit.setText(self.account.password or "")
        
        self.status_combo.setCurrentText(self.account.status)
        
        if self.account.proxy_ip:
            self.proxy_ip_edit.setText(self.account.proxy_ip)
        if self.account.proxy_port:
            self.proxy_port_spin.setValue(self.account.proxy_port)
        if self.account.proxy_user:
            self.proxy_user_edit.setText(self.account.proxy_user)
        if self.account.proxy_pass:
            self.proxy_pass_edit.setText(self.account.proxy_pass)
    
    def get_account_data(self) -> Account:
        """获取界面输入的账号数据"""
        account = self.account if self.is_edit_mode else Account()
        
        account.email = self.email_edit.text().strip()
        
        # 明文密码存储
        password = self.password_edit.text()
        if password:
            account.password = password
        
        account.status = self.status_combo.currentText()
        
        # 代理设置
        proxy_ip = self.proxy_ip_edit.text().strip()
        if proxy_ip:
            account.proxy_ip = proxy_ip
            account.proxy_port = self.proxy_port_spin.value()
            
            proxy_user = self.proxy_user_edit.text().strip()
            if proxy_user:
                account.proxy_user = proxy_user
            
            proxy_pass = self.proxy_pass_edit.text()
            if proxy_pass:
                account.proxy_pass = proxy_pass
        else:
            account.proxy_ip = None
            account.proxy_port = None
            account.proxy_user = None
            account.proxy_pass = None
        
        return account
    
    def accept(self):
        """确认按钮点击事件"""
        # 验证输入
        email = self.email_edit.text().strip()
        password = self.password_edit.text()
        
        if not email:
            QMessageBox.warning(self, "输入错误", "请输入邮箱地址")
            return
        
        if not password and not self.is_edit_mode:
            QMessageBox.warning(self, "输入错误", "请输入密码")
            return
        
        # 简单的邮箱格式验证
        if "@" not in email or "." not in email:
            QMessageBox.warning(self, "输入错误", "请输入有效的邮箱地址")
            return
        
        super().accept()


class ImportThread(QThread):
    """导入账号的后台线程"""
    
    progress_updated = pyqtSignal(int, str)  # 进度, 消息
    import_finished = pyqtSignal(dict)  # 导入结果
    
    def __init__(self, file_path: str, account_manager: AccountManager):
        super().__init__()
        self.file_path = file_path
        self.account_manager = account_manager
        self.password_manager = get_password_manager()
    
    def run(self):
        """执行导入任务"""
        try:
            accounts_data = []
            
            # 读取CSV文件
            with open(self.file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                total_rows = sum(1 for _ in reader)
                f.seek(0)
                reader = csv.DictReader(f)
                
                for i, row in enumerate(reader):
                    # 更新进度
                    progress = int((i + 1) / total_rows * 50)  # 前50%用于读取
                    self.progress_updated.emit(progress, f"读取第 {i+1} 行数据...")
                    
                    # 处理数据
                    account_data = {
                        'email': row.get('email', '').strip(),
                        'password': row.get('password', '').strip(),
                        'proxy_ip': row.get('proxy_ip', '').strip() or None,
                        'proxy_port': int(row.get('proxy_port', 0)) or None,
                        'proxy_user': row.get('proxy_user', '').strip() or None,
                        'proxy_pass': row.get('proxy_pass', '').strip() or None,
                        'status': row.get('status', 'active').strip()
                    }
                    
                    # 密码明文存储（不加密）
                    
                    if account_data['email'] and account_data['password']:
                        accounts_data.append(account_data)
            
            # 批量导入
            self.progress_updated.emit(50, "开始导入账号...")
            result = self.account_manager.batch_import_accounts(accounts_data)
            
            self.progress_updated.emit(100, "导入完成")
            self.import_finished.emit(result)
            
        except Exception as e:
            logger.error(f"导入账号失败: {e}")
            self.import_finished.emit({'error': str(e)})


class AccountWidget(QWidget):
    """账号管理主界面"""
    
    def __init__(self, db_manager: DatabaseManager, config: Dict[str, Any] = None):
        """
        初始化账号管理界面

        Args:
            db_manager: 数据库管理器
            config: 配置字典
        """
        super().__init__()
        self.db_manager = db_manager
        self.config = config or {}
        self.account_manager = AccountManager(db_manager)

        # 初始化浏览器管理器和隐藏式登录管理器（已包含Cookie管理器）
        self.browser_manager = BrowserManager(self.config)
        from ..core.stealth_login_manager import StealthLoginManager
        self.login_manager = StealthLoginManager(self.browser_manager, self)

        self.init_ui()
        self.load_accounts()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.add_btn = QPushButton("添加账号")
        self.add_btn.clicked.connect(self.add_account)
        toolbar_layout.addWidget(self.add_btn)
        
        self.import_btn = QPushButton("导入账号")
        self.import_btn.clicked.connect(self.import_accounts)
        toolbar_layout.addWidget(self.import_btn)
        
        self.export_btn = QPushButton("导出账号")
        self.export_btn.clicked.connect(self.export_accounts)
        toolbar_layout.addWidget(self.export_btn)

        toolbar_layout.addStretch()

        # 登录验证按钮
        self.batch_login_btn = QPushButton("🔐 批量登录验证")
        self.batch_login_btn.clicked.connect(self.batch_login_all_accounts)
        self.batch_login_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        toolbar_layout.addWidget(self.batch_login_btn)

        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_accounts)
        toolbar_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 账号表格
        self.table = QTableWidget()
        self.setup_table()
        layout.addWidget(self.table)
        
        # 状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        self.count_label = QLabel("总计: 0 个账号")
        status_layout.addWidget(self.count_label)
        
        layout.addLayout(status_layout)
    
    def setup_table(self):
        """设置表格"""
        headers = ["ID", "邮箱地址", "状态", "Cookie状态", "代理IP", "代理端口", "发送次数", "最后使用", "创建时间"]
        self.table.setColumnCount(len(headers))
        self.table.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        
        # 设置列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 邮箱
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 状态
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 代理IP
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 代理端口
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 发送次数
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # 最后使用
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # 创建时间
        
        # 右键菜单
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)
        
        # 双击编辑
        self.table.doubleClicked.connect(self.edit_account)
    
    def load_accounts(self):
        """加载账号数据"""
        try:
            accounts = self.account_manager.get_all_accounts()
            self.table.setRowCount(len(accounts))
            
            for row, account in enumerate(accounts):
                self.table.setItem(row, 0, QTableWidgetItem(str(account.id)))
                self.table.setItem(row, 1, QTableWidgetItem(account.email))
                self.table.setItem(row, 2, QTableWidgetItem(account.status))

                # 检查Cookie状态
                cookie_status = "❌ 无Cookie"
                try:
                    if self.login_manager and self.login_manager.has_valid_cookies(account.email):
                        cookie_status = "✅ 有效"
                    else:
                        cookie_status = "❌ 无Cookie"
                except:
                    cookie_status = "❓ 未知"

                self.table.setItem(row, 3, QTableWidgetItem(cookie_status))
                self.table.setItem(row, 4, QTableWidgetItem(account.proxy_ip or ""))
                self.table.setItem(row, 5, QTableWidgetItem(str(account.proxy_port) if account.proxy_port else ""))
                self.table.setItem(row, 6, QTableWidgetItem(str(account.send_count)))

                last_used = account.last_used.strftime("%Y-%m-%d %H:%M") if account.last_used else ""
                self.table.setItem(row, 7, QTableWidgetItem(last_used))

                create_time = account.create_time.strftime("%Y-%m-%d %H:%M") if account.create_time else ""
                self.table.setItem(row, 8, QTableWidgetItem(create_time))
                
                # 设置行数据
                self.table.item(row, 0).setData(Qt.UserRole, account)
            
            self.count_label.setText(f"总计: {len(accounts)} 个账号")
            self.status_label.setText("账号列表加载完成")
            
        except Exception as e:
            logger.error(f"加载账号列表失败: {e}")
            QMessageBox.critical(self, "错误", f"加载账号列表失败: {e}")
    
    def add_account(self):
        """添加账号"""
        dialog = AccountDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                account = dialog.get_account_data()
                self.account_manager.add_account(account)
                self.load_accounts()
                self.status_label.setText("账号添加成功")
            except Exception as e:
                logger.error(f"添加账号失败: {e}")
                QMessageBox.critical(self, "错误", f"添加账号失败: {e}")
    
    def edit_account(self):
        """编辑账号"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return
        
        account = self.table.item(current_row, 0).data(Qt.UserRole)
        if not account:
            return
        
        dialog = AccountDialog(account, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                updated_account = dialog.get_account_data()
                self.account_manager.update_account(updated_account)
                self.load_accounts()
                self.status_label.setText("账号更新成功")
            except Exception as e:
                logger.error(f"更新账号失败: {e}")
                QMessageBox.critical(self, "错误", f"更新账号失败: {e}")
    
    def delete_account(self):
        """删除账号"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return
        
        account = self.table.item(current_row, 0).data(Qt.UserRole)
        if not account:
            return
        
        reply = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除账号 {account.email} 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.account_manager.delete_account(account.id)
                self.load_accounts()
                self.status_label.setText("账号删除成功")
            except Exception as e:
                logger.error(f"删除账号失败: {e}")
                QMessageBox.critical(self, "错误", f"删除账号失败: {e}")
    
    def import_accounts(self):
        """导入账号"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择账号文件",
            "",
            "CSV文件 (*.csv);;所有文件 (*)"
        )
        
        if not file_path:
            return
        
        # 显示进度对话框
        progress_dialog = QDialog(self)
        progress_dialog.setWindowTitle("导入账号")
        progress_dialog.setModal(True)
        progress_dialog.resize(400, 150)
        
        layout = QVBoxLayout(progress_dialog)
        
        progress_label = QLabel("准备导入...")
        layout.addWidget(progress_label)
        
        progress_bar = QProgressBar()
        layout.addWidget(progress_bar)
        
        # 启动导入线程
        self.import_thread = ImportThread(file_path, self.account_manager)
        self.import_thread.progress_updated.connect(
            lambda progress, message: (
                progress_bar.setValue(progress),
                progress_label.setText(message)
            )
        )
        self.import_thread.import_finished.connect(
            lambda result: self.on_import_finished(result, progress_dialog)
        )
        
        self.import_thread.start()
        progress_dialog.exec_()
    
    def on_import_finished(self, result: Dict[str, Any], progress_dialog: QDialog):
        """导入完成回调"""
        progress_dialog.accept()
        
        if 'error' in result:
            QMessageBox.critical(self, "导入失败", f"导入账号失败: {result['error']}")
        else:
            message = (
                f"导入完成!\n"
                f"成功: {result['success']} 个\n"
                f"失败: {result['failed']} 个\n"
                f"重复: {result['duplicate']} 个"
            )
            QMessageBox.information(self, "导入完成", message)
            self.load_accounts()
    
    def export_accounts(self):
        """导出账号"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存账号文件",
            "accounts.csv",
            "CSV文件 (*.csv);;所有文件 (*)"
        )
        
        if not file_path:
            return
        
        try:
            accounts = self.account_manager.get_all_accounts()
            
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                
                # 写入表头
                writer.writerow([
                    'email', 'password', 'proxy_ip', 'proxy_port',
                    'proxy_user', 'proxy_pass', 'status'
                ])
                
                # 写入数据
                for account in accounts:
                    # 密码明文显示（不解密）
                    password = account.password or ""
                    proxy_pass = account.proxy_pass or ""
                    
                    writer.writerow([
                        account.email,
                        password,
                        account.proxy_ip or "",
                        account.proxy_port or "",
                        account.proxy_user or "",
                        proxy_pass,
                        account.status
                    ])
            
            QMessageBox.information(self, "导出成功", f"账号已导出到: {file_path}")
            self.status_label.setText("账号导出成功")
            
        except Exception as e:
            logger.error(f"导出账号失败: {e}")
            QMessageBox.critical(self, "错误", f"导出账号失败: {e}")
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.table.itemAt(position) is None:
            return

        menu = QMenu(self)

        # 登录验证相关菜单
        login_action = QAction("🔐 登录验证", self)
        login_action.triggered.connect(self.login_selected_account)
        menu.addAction(login_action)

        # 检查是否选择了多个账号
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())

        if len(selected_rows) > 1:
            batch_login_action = QAction(f"🔐 批量登录验证 ({len(selected_rows)}个)", self)
            batch_login_action.triggered.connect(self.batch_login_selected_accounts)
            menu.addAction(batch_login_action)

        menu.addSeparator()

        # 编辑和删除
        edit_action = QAction("✏️ 编辑", self)
        edit_action.triggered.connect(self.edit_account)
        menu.addAction(edit_action)

        delete_action = QAction("🗑️ 删除", self)
        delete_action.triggered.connect(self.delete_account)
        menu.addAction(delete_action)

        menu.addSeparator()

        # 批量操作（如果选择了多个账号）
        if len(selected_rows) > 1:
            batch_delete_action = QAction(f"🗑️ 批量删除 ({len(selected_rows)}个)", self)
            batch_delete_action.triggered.connect(self.batch_delete_selected_accounts)
            menu.addAction(batch_delete_action)

        menu.exec_(self.table.mapToGlobal(position))

    def login_selected_account(self):
        """登录选中的账号"""
        try:
            current_row = self.table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "提示", "请选择要登录验证的账号")
                return

            # 获取选中的账号
            account_item = self.table.item(current_row, 0)
            if not account_item:
                return

            account = account_item.data(Qt.UserRole)
            if not account:
                return

            # 确认登录
            reply = QMessageBox.question(
                self,
                "确认登录",
                f"确定要登录验证账号 {account.email} 吗？\n\n"
                f"系统将打开浏览器进行登录，如遇到验证码请手动完成。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 执行隐藏式登录验证
                try:
                    # 询问用户选择登录模式
                    mode_msg = QMessageBox()
                    mode_msg.setWindowTitle("选择登录模式")
                    mode_msg.setText("请选择登录模式：")
                    mode_msg.setInformativeText(
                        "🥷 隐藏模式：完全后台运行，只在需要验证时弹出\n"
                        "📱 最小化模式：极小窗口，几乎不可见\n\n"
                        "推荐使用隐藏模式，资源占用最少"
                    )

                    stealth_btn = mode_msg.addButton("🥷 隐藏模式", QMessageBox.AcceptRole)
                    mini_btn = mode_msg.addButton("📱 最小化模式", QMessageBox.AcceptRole)
                    cancel_btn = mode_msg.addButton("取消", QMessageBox.RejectRole)
                    mode_msg.setDefaultButton(stealth_btn)

                    mode_msg.exec_()
                    clicked_button = mode_msg.clickedButton()

                    if clicked_button == cancel_btn:
                        return

                    stealth_mode = (clicked_button == stealth_btn)
                    mode_name = "隐藏模式" if stealth_mode else "最小化模式"

                    logger.info(f"🚀 开始{mode_name}登录验证: {account.email}")

                    success, message = self.login_manager.stealth_login(account, stealth_mode)

                    if success:
                        # 刷新账号列表
                        self.load_accounts()

                        # 显示登录成功和后续操作选项
                        self._show_login_success_options(account, mode_name, message)
                    else:
                        QMessageBox.warning(self, "登录失败",
                                          f"❌ 账号 {account.email} 登录失败\n\n"
                                          f"模式: {mode_name}\n"
                                          f"原因: {message}")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"隐藏式登录验证异常：{e}")

        except Exception as e:
            logger.error(f"单个账号登录失败: {e}")
            QMessageBox.critical(self, "错误", f"登录验证失败: {e}")

    def batch_login_selected_accounts(self):
        """批量登录选中的账号"""
        try:
            # 获取选中的账号
            selected_accounts = []
            selected_rows = set()

            for item in self.table.selectedItems():
                row = item.row()
                if row not in selected_rows:
                    selected_rows.add(row)
                    account_item = self.table.item(row, 0)
                    if account_item:
                        account = account_item.data(Qt.UserRole)
                        if account:
                            selected_accounts.append(account)

            if not selected_accounts:
                QMessageBox.warning(self, "提示", "请选择要登录验证的账号")
                return

            # 确认批量登录
            reply = QMessageBox.question(
                self,
                "确认批量登录",
                f"确定要批量登录验证 {len(selected_accounts)} 个账号吗？\n\n"
                f"系统将依次打开浏览器进行登录，如遇到验证码请手动完成。\n"
                f"建议在网络稳定的环境下进行操作。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 执行批量隐藏式登录验证
                try:
                    # 询问批量登录模式
                    mode_msg = QMessageBox()
                    mode_msg.setWindowTitle("选择批量登录模式")
                    mode_msg.setText(f"即将对 {len(selected_accounts)} 个账号进行批量登录验证")
                    mode_msg.setInformativeText(
                        "🥷 隐藏模式：所有账号完全后台运行\n"
                        "📱 最小化模式：所有账号使用极小窗口\n\n"
                        "批量操作推荐使用隐藏模式，避免窗口过多"
                    )

                    stealth_btn = mode_msg.addButton("🥷 隐藏模式", QMessageBox.AcceptRole)
                    mini_btn = mode_msg.addButton("📱 最小化模式", QMessageBox.AcceptRole)
                    cancel_btn = mode_msg.addButton("取消", QMessageBox.RejectRole)
                    mode_msg.setDefaultButton(stealth_btn)

                    mode_msg.exec_()
                    clicked_button = mode_msg.clickedButton()

                    if clicked_button == cancel_btn:
                        return

                    stealth_mode = (clicked_button == stealth_btn)
                    mode_name = "隐藏模式" if stealth_mode else "最小化模式"

                    success_count = 0
                    failed_count = 0
                    results = []

                    logger.info(f"🚀 开始批量{mode_name}登录验证，共 {len(selected_accounts)} 个账号")

                    for i, account in enumerate(selected_accounts, 1):
                        try:
                            logger.info(f"📧 处理账号 {i}/{len(selected_accounts)}: {account.email}")
                            success, message = self.login_manager.stealth_login(account, stealth_mode)

                            if success:
                                success_count += 1
                                results.append(f"✅ {account.email}: 成功 - {message}")
                            else:
                                failed_count += 1
                                results.append(f"❌ {account.email}: 失败 - {message}")

                        except Exception as e:
                            failed_count += 1
                            results.append(f"💥 {account.email}: 异常 - {e}")

                        # 批量操作间隔，避免过于频繁
                        if i < len(selected_accounts):
                            time.sleep(1)

                    # 刷新账号列表
                    self.load_accounts()

                    # 显示结果摘要
                    result_text = (
                        f"🎯 批量隐藏式登录验证完成\n\n"
                        f"模式: {mode_name}\n"
                        f"总数: {len(selected_accounts)}\n"
                        f"成功: {success_count}\n"
                        f"失败: {failed_count}\n"
                        f"成功率: {(success_count/len(selected_accounts)*100):.1f}%\n\n"
                        f"详细结果:\n" + "\n".join(results)
                    )
                    QMessageBox.information(self, "批量验证结果", result_text)
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"批量隐藏式登录验证异常：{e}")
                success_count = results.get('success', 0)
                failed_count = results.get('failed', 0)
                cancelled_count = results.get('cancelled', 0)

                message = f"批量登录验证完成！\n\n"
                message += f"✅ 成功: {success_count} 个\n"
                message += f"❌ 失败: {failed_count} 个\n"
                if cancelled_count > 0:
                    message += f"⏹ 取消: {cancelled_count} 个\n"

                QMessageBox.information(self, "批量登录结果", message)

        except Exception as e:
            logger.error(f"批量账号登录失败: {e}")
            QMessageBox.critical(self, "错误", f"批量登录验证失败: {e}")

    def batch_delete_selected_accounts(self):
        """批量删除选中的账号"""
        try:
            # 获取选中的账号
            selected_accounts = []
            selected_rows = set()

            for item in self.table.selectedItems():
                row = item.row()
                if row not in selected_rows:
                    selected_rows.add(row)
                    account_item = self.table.item(row, 0)
                    if account_item:
                        account = account_item.data(Qt.UserRole)
                        if account:
                            selected_accounts.append(account)

            if not selected_accounts:
                QMessageBox.warning(self, "提示", "请选择要删除的账号")
                return

            # 确认批量删除
            account_list = "\n".join([f"• {acc.email}" for acc in selected_accounts[:10]])
            if len(selected_accounts) > 10:
                account_list += f"\n... 还有 {len(selected_accounts) - 10} 个账号"

            reply = QMessageBox.question(
                self,
                "确认批量删除",
                f"确定要删除以下 {len(selected_accounts)} 个账号吗？\n\n{account_list}\n\n"
                f"⚠️ 此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 执行批量删除
                success_count = 0
                failed_count = 0

                for account in selected_accounts:
                    try:
                        self.account_manager.delete_account(account.id)
                        success_count += 1
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"删除账号失败: {account.email}, 错误: {e}")

                # 刷新账号列表
                self.load_accounts()

                # 显示结果
                if failed_count == 0:
                    QMessageBox.information(self, "成功", f"成功删除 {success_count} 个账号")
                else:
                    QMessageBox.warning(self, "部分成功",
                                      f"成功删除 {success_count} 个账号\n失败 {failed_count} 个账号")

        except Exception as e:
            logger.error(f"批量删除账号失败: {e}")
            QMessageBox.critical(self, "错误", f"批量删除失败: {e}")

    def batch_login_all_accounts(self):
        """批量登录所有账号"""
        try:
            # 获取所有账号
            all_accounts = self.account_manager.get_all_accounts()

            if not all_accounts:
                QMessageBox.information(self, "提示", "没有可用的账号进行登录验证")
                return

            # 确认批量登录
            reply = QMessageBox.question(
                self,
                "确认批量登录",
                f"确定要对所有 {len(all_accounts)} 个账号进行隐藏式登录验证吗？\n\n"
                f"🥷 系统将使用隐藏模式后台登录，只在需要验证时弹出窗口\n"
                f"⚡ 全网最轻量的登录方案，资源占用最少\n"
                f"🔐 如遇到验证码会自动弹出指导窗口\n\n"
                f"⚠️ 此操作可能需要较长时间，请耐心等待。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 执行批量隐藏式登录验证（全部使用隐藏模式）
                try:
                    success_count = 0
                    failed_count = 0
                    results = []

                    logger.info(f"🥷 开始批量隐藏式登录验证，共 {len(all_accounts)} 个账号")

                    for i, account in enumerate(all_accounts, 1):
                        try:
                            logger.info(f"📧 处理账号 {i}/{len(all_accounts)}: {account.email}")
                            # 全部使用隐藏模式，最轻量
                            success, message = self.login_manager.stealth_login(account, stealth_mode=True)

                            if success:
                                success_count += 1
                                results.append(f"✅ {account.email}: 成功 - {message}")
                            else:
                                failed_count += 1
                                results.append(f"❌ {account.email}: 失败 - {message}")

                        except Exception as e:
                            failed_count += 1
                            results.append(f"💥 {account.email}: 异常 - {e}")

                        # 批量操作间隔
                        if i < len(all_accounts):
                            time.sleep(1)

                    # 刷新账号列表
                    self.load_accounts()

                    # 显示详细结果
                    message = f"🥷 批量隐藏式登录验证完成！\n\n"
                    message += f"模式: 隐藏模式（全网最轻量）\n"
                    message += f"总数: {len(all_accounts)}\n"
                    message += f"✅ 成功: {success_count} 个\n"
                    message += f"❌ 失败: {failed_count} 个\n"

                    # 显示成功率
                    total_processed = success_count + failed_count
                    if total_processed > 0:
                        success_rate = (success_count / total_processed) * 100
                        message += f"📊 成功率: {success_rate:.1f}%\n\n"
                        message += f"详细结果:\n" + "\n".join(results[:10])  # 只显示前10个结果
                        if len(results) > 10:
                            message += f"\n... 还有 {len(results)-10} 个结果"

                    QMessageBox.information(self, "批量登录结果", message)
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"批量隐藏式登录验证异常：{e}")

        except Exception as e:
            logger.error(f"批量登录所有账号失败: {e}")
            QMessageBox.critical(self, "错误", f"批量登录验证失败: {e}")

    def _show_login_success_options(self, account, mode_name: str, message: str):
        """显示登录成功和后续操作选项"""
        try:
            # 创建自定义对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("🎉 登录成功")
            dialog.setModal(True)
            dialog.resize(500, 400)

            layout = QVBoxLayout(dialog)

            # 成功信息
            success_label = QLabel(f"🎉 账号 {account.email} 隐藏式登录成功！")
            success_label.setFont(QFont("Arial", 12, QFont.Bold))
            layout.addWidget(success_label)

            # 详细信息
            detail_text = QTextEdit()
            detail_text.setReadOnly(True)
            detail_text.setMaximumHeight(100)
            detail_info = f"模式: {mode_name}\n结果: {message}\n登录时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
            detail_text.setText(detail_info)
            layout.addWidget(detail_text)

            # 后续操作选项
            options_label = QLabel("🎯 选择后续操作：")
            options_label.setFont(QFont("Arial", 10, QFont.Bold))
            layout.addWidget(options_label)

            # 操作按钮组
            button_layout = QVBoxLayout()

            # 获取可用操作
            next_actions = self.login_manager.get_next_action_menu()

            for action in next_actions:
                btn = QPushButton(action)
                btn.clicked.connect(lambda checked, a=action: self._handle_next_action(account, a, dialog))
                button_layout.addWidget(btn)

            layout.addLayout(button_layout)

            # 关闭按钮
            close_layout = QHBoxLayout()
            close_layout.addStretch()
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.accept)
            close_layout.addWidget(close_btn)
            layout.addLayout(close_layout)

            # 显示对话框
            dialog.exec_()

        except Exception as e:
            logger.error(f"显示登录成功选项失败: {e}")
            # 降级到简单消息框
            QMessageBox.information(self, "登录成功",
                                  f"🎉 账号 {account.email} 隐藏式登录成功！\n\n"
                                  f"模式: {mode_name}\n"
                                  f"结果: {message}")

    def _handle_next_action(self, account, action: str, dialog: QDialog):
        """处理后续操作"""
        try:
            logger.info(f"🎯 执行后续操作: {action}")

            if "查看收件箱" in action:
                self._open_inbox(account)
            elif "写新邮件" in action:
                self._compose_email(account)
            elif "管理通讯录" in action:
                self._manage_contacts(account)
            elif "邮箱设置" in action:
                self._open_settings(account)
            elif "保持登录" in action:
                self._keep_login(account)
            elif "安全退出" in action:
                self._safe_logout(account)

            # 关闭对话框
            dialog.accept()

        except Exception as e:
            logger.error(f"处理后续操作失败: {e}")
            QMessageBox.warning(self, "操作失败", f"执行操作失败：{e}")

    def _open_inbox(self, account):
        """打开收件箱"""
        QMessageBox.information(self, "收件箱", f"正在为账号 {account.email} 打开收件箱...")
        # 这里可以扩展为实际的收件箱操作

    def _compose_email(self, account):
        """写新邮件"""
        QMessageBox.information(self, "写邮件", f"正在为账号 {account.email} 打开写邮件界面...")
        # 这里可以扩展为实际的写邮件操作

    def _manage_contacts(self, account):
        """管理通讯录"""
        QMessageBox.information(self, "通讯录", f"正在为账号 {account.email} 打开通讯录管理...")
        # 这里可以扩展为实际的通讯录操作

    def _open_settings(self, account):
        """打开设置"""
        QMessageBox.information(self, "设置", f"正在为账号 {account.email} 打开邮箱设置...")
        # 这里可以扩展为实际的设置操作

    def _keep_login(self, account):
        """保持登录状态"""
        reply = QMessageBox.question(self, "保持登录",
                                    f"账号 {account.email} 将保持登录状态\n"
                                    f"浏览器窗口将最小化到后台\n\n"
                                    f"确定要保持登录吗？",
                                    QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "保持登录", "账号已保持登录状态，浏览器已最小化")

    def _safe_logout(self, account):
        """安全退出"""
        reply = QMessageBox.question(self, "安全退出",
                                    f"确定要安全退出账号 {account.email} 吗？\n"
                                    f"这将关闭浏览器并清理登录状态",
                                    QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            # 这里可以调用登录管理器的退出方法
            QMessageBox.information(self, "安全退出", "账号已安全退出")
