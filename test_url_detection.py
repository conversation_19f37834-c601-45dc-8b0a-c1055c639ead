#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL检测测试脚本
用于测试新增的 m0.mail.sina.com.cn URL识别功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.browser_manager import BrowserManager
from src.core.stealth_login_manager import StealthLoginManager
from src.utils.logger import get_logger

logger = get_logger("URLDetectionTest")

class URLDetectionTest:
    """URL检测测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        self.config = {
            'browser': {
                'implicit_wait': 3,
                'page_load_timeout': 20,
                'window_size': [1920, 1080]
            }
        }
        
        # 初始化浏览器管理器和登录管理器
        self.browser_manager = BrowserManager(self.config)
        self.login_manager = StealthLoginManager(self.browser_manager, None)
        
        logger.info("🧪 URL检测测试环境初始化完成")
    
    def test_success_url_detection(self):
        """测试登录成功URL检测"""
        try:
            logger.info("🔍 开始测试登录成功URL检测...")
            
            # 测试用例：不同的URL和页面内容组合
            test_cases = [
                {
                    "name": "新浪邮箱主页 - m0.mail.sina.com.cn",
                    "url": "https://m0.mail.sina.com.cn",
                    "page_content": """
                    <html>
                        <body>
                            <div class="mailbox">
                                <div class="inbox">收件箱</div>
                                <div class="compose">写邮件</div>
                            </div>
                        </body>
                    </html>
                    """.lower(),
                    "expected": True
                },
                {
                    "name": "新浪邮箱经典版",
                    "url": "https://m0.mail.sina.com.cn/classic/index.php",
                    "page_content": """
                    <html>
                        <body>
                            <div class="mail-container">
                                <div class="maillist">邮件列表</div>
                                <div class="toolbar">工具栏</div>
                            </div>
                        </body>
                    </html>
                    """.lower(),
                    "expected": True
                },
                {
                    "name": "登录页面 - 不应该识别为成功",
                    "url": "https://mail.sina.com.cn/#",
                    "page_content": """
                    <html>
                        <body>
                            <div class="login-form">
                                <input name="freename" placeholder="邮箱地址">
                                <input name="freepassword" placeholder="密码">
                                <input type="submit" value="登录">
                            </div>
                        </body>
                    </html>
                    """.lower(),
                    "expected": False
                },
                {
                    "name": "带验证码的登录页面",
                    "url": "https://mail.sina.com.cn/#",
                    "page_content": """
                    <html>
                        <body>
                            <div class="login-form">
                                <input name="freename" placeholder="邮箱地址">
                                <input name="freepassword" placeholder="密码">
                                <div class="captcha">验证码</div>
                                <input type="submit" value="登录">
                            </div>
                        </body>
                    </html>
                    """.lower(),
                    "expected": False
                }
            ]
            
            # 执行测试用例
            passed_tests = 0
            total_tests = len(test_cases)
            
            for i, test_case in enumerate(test_cases, 1):
                logger.info(f"📋 测试用例 {i}: {test_case['name']}")
                
                # 执行检测
                success, message = self.login_manager._enhanced_login_success_detection(
                    test_case['page_content'], 
                    test_case['url']
                )
                
                # 验证结果
                if success == test_case['expected']:
                    logger.info(f"✅ 测试通过: {message}")
                    passed_tests += 1
                else:
                    expected_str = "成功" if test_case['expected'] else "失败"
                    actual_str = "成功" if success else "失败"
                    logger.error(f"❌ 测试失败: 期望{expected_str}，实际{actual_str} - {message}")
            
            # 输出测试结果
            success_rate = (passed_tests / total_tests) * 100
            logger.info(f"📊 URL检测测试结果: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
            
            if success_rate >= 100:
                logger.info("🎉 URL检测测试完全通过！")
            elif success_rate >= 75:
                logger.info("✅ URL检测测试基本通过")
            else:
                logger.warning("⚠️ URL检测测试需要改进")
            
            return success_rate >= 75
            
        except Exception as e:
            logger.error(f"❌ URL检测测试异常: {e}")
            return False
    
    def test_verification_url_handling(self):
        """测试验证码状态下的URL处理"""
        try:
            logger.info("🔐 开始测试验证码状态下的URL处理...")
            
            # 模拟在m0.mail.sina.com.cn但仍有验证码的情况
            test_url = "https://m0.mail.sina.com.cn"
            test_content_with_verification = """
            <html>
                <body>
                    <div class="verification-container">
                        <div class="captcha-box">
                            <span>验证码</span>
                            <div class="geetest-challenge">请完成验证</div>
                        </div>
                    </div>
                </body>
            </html>
            """.lower()
            
            # 测试严格验证码检测
            has_verification = self.login_manager._strict_verification_detection(test_content_with_verification)
            
            if has_verification:
                logger.info("✅ 验证码检测正确：在m0.mail.sina.com.cn上仍能检测到验证码")
            else:
                logger.error("❌ 验证码检测失败：未能在m0.mail.sina.com.cn上检测到验证码")
            
            # 测试登录成功检测（应该因为有验证码而返回失败）
            success, message = self.login_manager._enhanced_login_success_detection(
                test_content_with_verification, 
                test_url
            )
            
            if not success:
                logger.info("✅ 登录检测正确：有验证码时不会误判为登录成功")
            else:
                logger.error("❌ 登录检测错误：有验证码时误判为登录成功")
            
            return has_verification and not success
            
        except Exception as e:
            logger.error(f"❌ 验证码URL处理测试异常: {e}")
            return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        try:
            logger.info("🚀 开始URL检测综合测试")
            logger.info("=" * 50)
            
            test_results = []
            
            # 1. 登录成功URL检测测试
            logger.info("📋 测试1: 登录成功URL检测")
            result1 = self.test_success_url_detection()
            test_results.append(("登录成功URL检测", result1))
            
            # 2. 验证码状态下的URL处理测试
            logger.info("📋 测试2: 验证码状态下的URL处理")
            result2 = self.test_verification_url_handling()
            test_results.append(("验证码URL处理", result2))
            
            # 输出测试结果
            logger.info("=" * 50)
            logger.info("📊 综合测试结果:")
            
            passed_tests = 0
            total_tests = len(test_results)
            
            for test_name, result in test_results:
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"   {test_name}: {status}")
                if result:
                    passed_tests += 1
            
            success_rate = (passed_tests / total_tests) * 100
            logger.info(f"📈 测试通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
            
            if success_rate >= 100:
                logger.info("🎉 URL检测功能完全正常！")
                logger.info("✅ m0.mail.sina.com.cn URL识别已正确添加")
            else:
                logger.warning("⚠️ URL检测功能需要进一步优化")
            
            logger.info("=" * 50)
            
        except Exception as e:
            logger.error(f"❌ 综合测试异常: {e}")

def main():
    """主函数"""
    test = URLDetectionTest()
    
    try:
        # 执行综合测试
        test.run_comprehensive_test()
        
    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断测试")
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    main()
